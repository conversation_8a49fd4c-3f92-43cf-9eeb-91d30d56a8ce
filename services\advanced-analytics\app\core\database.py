"""
AthenaTrader Phase 11 Advanced Analytics Engine Database Configuration
"""

import asyncio
import logging
from typing import Optional, AsyncGenerator
import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, JSON
from datetime import datetime
from .config import settings

logger = logging.getLogger(__name__)

# Database engine and session
engine = None
async_session_maker = None

# SQLAlchemy Base
Base = declarative_base()


class MLModelMetadata(Base):
    """ML Model metadata table."""
    __tablename__ = "ml_model_metadata"
    
    id = Column(Integer, primary_key=True, index=True)
    model_name = Column(String(100), unique=True, index=True, nullable=False)
    model_type = Column(String(50), nullable=False)  # lstm, transformer, sentiment, etc.
    version = Column(String(20), nullable=False)
    accuracy = Column(Float, nullable=True)
    training_date = Column(DateTime, default=datetime.utcnow)
    model_path = Column(String(500), nullable=False)
    parameters = Column(JSON, nullable=True)
    status = Column(String(20), default="active")  # active, deprecated, training
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class MarketPrediction(Base):
    """Market prediction results table."""
    __tablename__ = "market_predictions"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), index=True, nullable=False)
    model_name = Column(String(100), index=True, nullable=False)
    prediction_timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    horizon_minutes = Column(Integer, nullable=False)  # prediction horizon
    predicted_price = Column(Float, nullable=False)
    confidence_score = Column(Float, nullable=False)
    actual_price = Column(Float, nullable=True)  # filled later for accuracy calculation
    prediction_accuracy = Column(Float, nullable=True)
    features_used = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class SentimentAnalysis(Base):
    """Sentiment analysis results table."""
    __tablename__ = "sentiment_analysis"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), index=True, nullable=False)
    source = Column(String(50), nullable=False)  # news, twitter, reddit, etc.
    content = Column(Text, nullable=False)
    sentiment_score = Column(Float, nullable=False)  # -1 to 1
    sentiment_label = Column(String(20), nullable=False)  # positive, negative, neutral
    confidence = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    source_url = Column(String(1000), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class MarketRegime(Base):
    """Market regime detection results table."""
    __tablename__ = "market_regimes"
    
    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(20), index=True, nullable=False)
    regime_timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    regime_type = Column(String(20), nullable=False)  # bull, bear, sideways
    regime_probability = Column(Float, nullable=False)
    volatility_level = Column(String(20), nullable=False)  # low, medium, high
    trend_strength = Column(Float, nullable=False)
    regime_duration_days = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class BacktestResult(Base):
    """Enhanced backtesting results table."""
    __tablename__ = "backtest_results"
    
    id = Column(Integer, primary_key=True, index=True)
    strategy_id = Column(String(100), index=True, nullable=False)
    backtest_type = Column(String(50), nullable=False)  # agent_based, monte_carlo, walk_forward
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    total_return = Column(Float, nullable=False)
    sharpe_ratio = Column(Float, nullable=False)
    max_drawdown = Column(Float, nullable=False)
    win_rate = Column(Float, nullable=False)
    total_trades = Column(Integer, nullable=False)
    simulation_parameters = Column(JSON, nullable=True)
    performance_metrics = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class ComplianceRecord(Base):
    """Regulatory compliance records table."""
    __tablename__ = "compliance_records"
    
    id = Column(Integer, primary_key=True, index=True)
    trade_id = Column(String(100), index=True, nullable=False)
    regulation_type = Column(String(50), nullable=False)  # emir, dodd_frank, mifid2
    compliance_check = Column(String(100), nullable=False)
    status = Column(String(20), nullable=False)  # compliant, violation, pending
    risk_score = Column(Float, nullable=False)
    details = Column(JSON, nullable=True)
    reported_to_regulator = Column(Boolean, default=False)
    blockchain_hash = Column(String(100), nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)


class MultiAssetPosition(Base):
    """Multi-asset position tracking table."""
    __tablename__ = "multi_asset_positions"
    
    id = Column(Integer, primary_key=True, index=True)
    portfolio_id = Column(String(100), index=True, nullable=False)
    asset_class = Column(String(50), nullable=False)  # equity, crypto, fixed_income, derivative
    symbol = Column(String(50), index=True, nullable=False)
    quantity = Column(Float, nullable=False)
    average_price = Column(Float, nullable=False)
    current_price = Column(Float, nullable=False)
    market_value = Column(Float, nullable=False)
    unrealized_pnl = Column(Float, nullable=False)
    risk_metrics = Column(JSON, nullable=True)
    last_updated = Column(DateTime, default=datetime.utcnow, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)


async def init_db():
    """Initialize database connections and create tables."""
    global engine, async_session_maker
    
    try:
        # Create async engine
        engine = create_async_engine(
            settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://"),
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            echo=settings.DEBUG
        )
        
        # Create session maker
        async_session_maker = async_sessionmaker(
            engine, 
            class_=AsyncSession, 
            expire_on_commit=False
        )
        
        # Create tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_db():
    """Close database connections."""
    global engine
    
    if engine:
        await engine.dispose()
        logger.info("Database connections closed")


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session."""
    if not async_session_maker:
        raise RuntimeError("Database not initialized")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            await session.close()


class DatabaseManager:
    """Database operations manager."""
    
    @staticmethod
    async def save_model_metadata(model_name: str, model_type: str, version: str, 
                                 accuracy: float, model_path: str, parameters: dict):
        """Save ML model metadata."""
        async with async_session_maker() as session:
            try:
                # Check if model exists
                existing_model = await session.get(MLModelMetadata, {"model_name": model_name})
                
                if existing_model:
                    # Update existing model
                    existing_model.version = version
                    existing_model.accuracy = accuracy
                    existing_model.model_path = model_path
                    existing_model.parameters = parameters
                    existing_model.updated_at = datetime.utcnow()
                else:
                    # Create new model record
                    new_model = MLModelMetadata(
                        model_name=model_name,
                        model_type=model_type,
                        version=version,
                        accuracy=accuracy,
                        model_path=model_path,
                        parameters=parameters
                    )
                    session.add(new_model)
                
                await session.commit()
                logger.info(f"Model metadata saved: {model_name}")
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save model metadata: {e}")
                raise
    
    @staticmethod
    async def save_prediction(symbol: str, model_name: str, horizon_minutes: int,
                            predicted_price: float, confidence_score: float, features_used: dict):
        """Save market prediction."""
        async with async_session_maker() as session:
            try:
                prediction = MarketPrediction(
                    symbol=symbol,
                    model_name=model_name,
                    horizon_minutes=horizon_minutes,
                    predicted_price=predicted_price,
                    confidence_score=confidence_score,
                    features_used=features_used
                )
                session.add(prediction)
                await session.commit()
                logger.info(f"Prediction saved: {symbol} - {model_name}")
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save prediction: {e}")
                raise
    
    @staticmethod
    async def save_sentiment_analysis(symbol: str, source: str, content: str,
                                    sentiment_score: float, sentiment_label: str, 
                                    confidence: float, source_url: str = None):
        """Save sentiment analysis result."""
        async with async_session_maker() as session:
            try:
                sentiment = SentimentAnalysis(
                    symbol=symbol,
                    source=source,
                    content=content,
                    sentiment_score=sentiment_score,
                    sentiment_label=sentiment_label,
                    confidence=confidence,
                    source_url=source_url
                )
                session.add(sentiment)
                await session.commit()
                logger.info(f"Sentiment analysis saved: {symbol} - {source}")
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save sentiment analysis: {e}")
                raise
    
    @staticmethod
    async def save_compliance_record(trade_id: str, regulation_type: str, compliance_check: str,
                                   status: str, risk_score: float, details: dict,
                                   blockchain_hash: str = None):
        """Save compliance record."""
        async with async_session_maker() as session:
            try:
                compliance_record = ComplianceRecord(
                    trade_id=trade_id,
                    regulation_type=regulation_type,
                    compliance_check=compliance_check,
                    status=status,
                    risk_score=risk_score,
                    details=details,
                    blockchain_hash=blockchain_hash
                )
                session.add(compliance_record)
                await session.commit()
                logger.info(f"Compliance record saved: {trade_id} - {regulation_type}")
                
            except Exception as e:
                await session.rollback()
                logger.error(f"Failed to save compliance record: {e}")
                raise
