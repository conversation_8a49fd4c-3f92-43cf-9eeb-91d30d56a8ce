"""
AthenaTrader Phase 4: Data Protection Compliance Module

GDPR (General Data Protection Regulation) and CCPA (California Consumer Privacy Act)
compliance implementation for comprehensive data protection and privacy rights management.

Key Features:
- Data subject rights management (access, rectification, erasure)
- Privacy by design and data minimization principles
- Consent management and lawful basis documentation
- Data breach notification within 72 hours
- Consumer rights management (know, delete, opt-out)
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_db
from ...core.logging_config import get_logger
from ...compliance.data_protection_engine import GDPREngine, CCPAEngine, ConsentManagementEngine
from ...models.compliance import DataSubjectRequest, ConsentRecord, ComplianceStatus

# Setup logging with Winston-style configuration
logger = get_logger(__name__)
compliance_logger = get_logger("compliance.data_protection")

# Create router
router = APIRouter()

# Initialize data protection engines
gdpr_engine = GDPREngine()
ccpa_engine = CCPAEngine()
consent_management = ConsentManagementEngine()


class DataSubjectRightsRequest(BaseModel):
    """Data subject rights request."""
    
    request_id: str = Field(..., description="Unique request identifier")
    data_subject_id: str = Field(..., description="Data subject identifier")
    request_type: str = Field(..., description="Type of request")
    jurisdiction: str = Field(..., description="Applicable jurisdiction")
    request_details: Dict[str, Any] = Field(..., description="Request details")
    verification_method: str = Field(..., description="Identity verification method")
    
    @validator('request_type')
    def validate_request_type(cls, v):
        """Validate request type."""
        allowed_types = [
            'ACCESS', 'RECTIFICATION', 'ERASURE', 'PORTABILITY', 'RESTRICTION',
            'OBJECTION', 'KNOW', 'DELETE', 'OPT_OUT', 'NON_DISCRIMINATION'
        ]
        if v.upper() not in allowed_types:
            raise ValueError(f"Request type must be one of: {allowed_types}")
        return v.upper()
    
    @validator('jurisdiction')
    def validate_jurisdiction(cls, v):
        """Validate jurisdiction."""
        allowed_jurisdictions = ['GDPR', 'CCPA', 'BOTH']
        if v.upper() not in allowed_jurisdictions:
            raise ValueError(f"Jurisdiction must be one of: {allowed_jurisdictions}")
        return v.upper()


class ConsentRequest(BaseModel):
    """Consent management request."""
    
    data_subject_id: str = Field(..., description="Data subject identifier")
    processing_purpose: str = Field(..., description="Purpose of data processing")
    data_categories: List[str] = Field(..., description="Categories of personal data")
    consent_given: bool = Field(..., description="Consent status")
    lawful_basis: str = Field(..., description="Lawful basis for processing")
    retention_period: Optional[int] = Field(None, description="Data retention period in days")
    
    @validator('lawful_basis')
    def validate_lawful_basis(cls, v):
        """Validate lawful basis."""
        allowed_bases = [
            'CONSENT', 'CONTRACT', 'LEGAL_OBLIGATION', 'VITAL_INTERESTS',
            'PUBLIC_TASK', 'LEGITIMATE_INTERESTS'
        ]
        if v.upper() not in allowed_bases:
            raise ValueError(f"Lawful basis must be one of: {allowed_bases}")
        return v.upper()


class DataProtectionResponse(BaseModel):
    """Data protection compliance response."""
    
    request_id: str
    compliance_status: str
    processing_status: str
    response_deadline: datetime
    actions_taken: List[str]
    data_provided: Optional[Dict[str, Any]]
    violations: List[str]
    recommendations: List[str]
    timestamp: datetime


@router.post("/data-subject-request", response_model=DataProtectionResponse)
async def process_data_subject_request(
    request: DataSubjectRightsRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Process data subject rights request under GDPR/CCPA.
    
    Handles various data subject rights including access, rectification,
    erasure, portability, and restriction requests with appropriate
    response timelines and verification procedures.
    """
    try:
        # Log request processing start
        compliance_logger.info(
            f"Processing data subject request - Request ID: {request.request_id}, "
            f"Type: {request.request_type}, "
            f"Subject: {request.data_subject_id}, "
            f"Jurisdiction: {request.jurisdiction}"
        )
        
        # Route to appropriate engine based on jurisdiction
        if request.jurisdiction == 'GDPR':
            result = await gdpr_engine.process_data_subject_request(
                request_id=request.request_id,
                data_subject_id=request.data_subject_id,
                request_type=request.request_type,
                request_details=request.request_details,
                verification_method=request.verification_method
            )
        elif request.jurisdiction == 'CCPA':
            result = await ccpa_engine.process_consumer_request(
                request_id=request.request_id,
                consumer_id=request.data_subject_id,
                request_type=request.request_type,
                request_details=request.request_details,
                verification_method=request.verification_method
            )
        else:  # BOTH
            gdpr_result = await gdpr_engine.process_data_subject_request(
                request_id=f"{request.request_id}_GDPR",
                data_subject_id=request.data_subject_id,
                request_type=request.request_type,
                request_details=request.request_details,
                verification_method=request.verification_method
            )
            ccpa_result = await ccpa_engine.process_consumer_request(
                request_id=f"{request.request_id}_CCPA",
                consumer_id=request.data_subject_id,
                request_type=request.request_type,
                request_details=request.request_details,
                verification_method=request.verification_method
            )
            result = await _merge_jurisdiction_results(gdpr_result, ccpa_result)
        
        # Calculate response deadline
        response_deadline = await _calculate_response_deadline(
            request.request_type, request.jurisdiction
        )
        
        # Log request processing completion
        compliance_logger.info(
            f"Data subject request processed - Request ID: {request.request_id}, "
            f"Status: {result['status']}, "
            f"Actions: {len(result['actions_taken'])}"
        )
        
        return DataProtectionResponse(
            request_id=request.request_id,
            compliance_status=result['status'],
            processing_status=result['processing_status'],
            response_deadline=response_deadline,
            actions_taken=result['actions_taken'],
            data_provided=result.get('data_provided'),
            violations=result.get('violations', []),
            recommendations=result.get('recommendations', []),
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Data subject request processing failed for {request.request_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Data subject request processing failed: {str(e)}"
        )


@router.post("/consent-management")
async def manage_consent(
    consent_request: ConsentRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Manage consent for data processing activities.
    
    Handles consent collection, withdrawal, and management
    with proper documentation and audit trails.
    """
    try:
        # Log consent management start
        compliance_logger.info(
            f"Managing consent - Subject: {consent_request.data_subject_id}, "
            f"Purpose: {consent_request.processing_purpose}, "
            f"Consent Given: {consent_request.consent_given}"
        )
        
        # Process consent management
        consent_result = await consent_management.process_consent(
            data_subject_id=consent_request.data_subject_id,
            processing_purpose=consent_request.processing_purpose,
            data_categories=consent_request.data_categories,
            consent_given=consent_request.consent_given,
            lawful_basis=consent_request.lawful_basis,
            retention_period=consent_request.retention_period
        )
        
        # Log consent management completion
        compliance_logger.info(
            f"Consent managed - Subject: {consent_request.data_subject_id}, "
            f"Consent ID: {consent_result['consent_id']}, "
            f"Status: {consent_result['status']}"
        )
        
        return {
            "data_subject_id": consent_request.data_subject_id,
            "consent_id": consent_result['consent_id'],
            "processing_purpose": consent_request.processing_purpose,
            "consent_status": consent_result['status'],
            "lawful_basis": consent_request.lawful_basis,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Consent management failed for {consent_request.data_subject_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Consent management failed: {str(e)}"
        )


@router.post("/data-breach-notification")
async def notify_data_breach(
    breach_data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """
    Handle data breach notification requirements.
    
    Manages data breach notifications to supervisory authorities
    and data subjects within required timeframes (72 hours for GDPR).
    """
    try:
        # Log breach notification start
        compliance_logger.info(
            f"Processing data breach notification - Breach ID: {breach_data.get('breach_id')}, "
            f"Severity: {breach_data.get('severity')}"
        )
        
        # Process GDPR breach notification
        gdpr_notification = await gdpr_engine.notify_data_breach(breach_data)
        
        # Process CCPA breach notification if applicable
        ccpa_notification = None
        if breach_data.get('affects_california_residents'):
            ccpa_notification = await ccpa_engine.notify_data_breach(breach_data)
        
        # Log breach notification completion
        compliance_logger.info(
            f"Data breach notification processed - Breach ID: {breach_data.get('breach_id')}, "
            f"GDPR Status: {gdpr_notification['status']}, "
            f"CCPA Status: {ccpa_notification['status'] if ccpa_notification else 'N/A'}"
        )
        
        return {
            "breach_id": breach_data.get('breach_id'),
            "gdpr_notification": gdpr_notification,
            "ccpa_notification": ccpa_notification,
            "notification_timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Data breach notification failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Data breach notification failed: {str(e)}"
        )


@router.get("/privacy-impact-assessment/{processing_activity_id}")
async def get_privacy_impact_assessment(
    processing_activity_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    Get privacy impact assessment for a processing activity.
    
    Provides DPIA (Data Protection Impact Assessment) results
    for high-risk processing activities under GDPR Article 35.
    """
    try:
        # Get privacy impact assessment
        pia_result = await gdpr_engine.get_privacy_impact_assessment(processing_activity_id)
        
        return {
            "processing_activity_id": processing_activity_id,
            "privacy_impact_assessment": pia_result,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Privacy impact assessment retrieval failed for {processing_activity_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Privacy impact assessment retrieval failed: {str(e)}"
        )


@router.get("/data-inventory")
async def get_data_inventory(
    data_category: Optional[str] = Query(None),
    processing_purpose: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive data inventory and mapping.
    
    Provides detailed inventory of personal data processing
    activities for compliance documentation and audits.
    """
    try:
        # Get data inventory
        inventory = await gdpr_engine.get_data_inventory(
            data_category=data_category,
            processing_purpose=processing_purpose
        )
        
        return {
            "data_category": data_category,
            "processing_purpose": processing_purpose,
            "data_inventory": inventory,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Data inventory retrieval failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Data inventory retrieval failed: {str(e)}"
        )


async def _calculate_response_deadline(request_type: str, jurisdiction: str) -> datetime:
    """Calculate response deadline based on request type and jurisdiction."""
    base_date = datetime.now()
    
    if jurisdiction == 'GDPR':
        # GDPR: 1 month (can be extended to 3 months for complex requests)
        return base_date + timedelta(days=30)
    elif jurisdiction == 'CCPA':
        # CCPA: 45 days (can be extended to 90 days)
        return base_date + timedelta(days=45)
    else:  # BOTH
        # Use the stricter deadline
        return base_date + timedelta(days=30)


async def _merge_jurisdiction_results(gdpr_result: Dict, ccpa_result: Dict) -> Dict:
    """Merge results from both GDPR and CCPA processing."""
    return {
        "status": "COMPLIANT" if gdpr_result['status'] == 'COMPLIANT' and ccpa_result['status'] == 'COMPLIANT' else "NON_COMPLIANT",
        "processing_status": "PROCESSED",
        "actions_taken": gdpr_result['actions_taken'] + ccpa_result['actions_taken'],
        "data_provided": {
            "gdpr_data": gdpr_result.get('data_provided'),
            "ccpa_data": ccpa_result.get('data_provided')
        },
        "violations": gdpr_result.get('violations', []) + ccpa_result.get('violations', []),
        "recommendations": gdpr_result.get('recommendations', []) + ccpa_result.get('recommendations', [])
    }


@router.get("/health")
async def data_protection_health_check():
    """Data protection compliance module health check."""
    try:
        # Check GDPR engine status
        gdpr_status = await gdpr_engine.health_check()
        
        # Check CCPA engine status
        ccpa_status = await ccpa_engine.health_check()
        
        # Check consent management status
        consent_status = await consent_management.health_check()
        
        return {
            "status": "healthy",
            "module": "Data Protection Compliance",
            "gdpr_status": gdpr_status,
            "ccpa_status": ccpa_status,
            "consent_management_status": consent_status,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Data protection health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Data protection compliance module unhealthy: {str(e)}"
        )
