"""
AthenaTrader Phase 4: MiFID II Compliance Module

Markets in Financial Instruments Directive II (MiFID II) compliance
implementation for European financial markets regulation.

Key Features:
- Best execution monitoring and reporting framework
- Transaction reporting to competent authorities within T+1
- Market abuse surveillance and suspicious transaction reporting
- Client categorization (retail, professional, eligible counterparty)
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_db
from ...core.logging_config import get_logger
from ...compliance.mifid_engine import MiFIDIIComplianceEngine, BestExecutionEngine, TransactionReportingEngine
from ...models.compliance import MiFIDTransaction, BestExecutionReport, ComplianceStatus

# Setup logging with Winston-style configuration
logger = get_logger(__name__)
compliance_logger = get_logger("compliance.mifid")

# Create router
router = APIRouter()

# Initialize MiFID II engines
mifid_compliance = MiFIDIIComplianceEngine()
best_execution = BestExecutionEngine()
transaction_reporting = TransactionReportingEngine()


class MiFIDTransactionRequest(BaseModel):
    """MiFID II transaction validation request."""
    
    trade_id: str = Field(..., description="Unique trade identifier")
    client_id: str = Field(..., description="Client identifier")
    instrument_id: str = Field(..., description="Financial instrument identifier")
    instrument_type: str = Field(..., description="Instrument classification")
    quantity: Decimal = Field(..., description="Trade quantity")
    price: Decimal = Field(..., description="Execution price")
    currency: str = Field(..., description="Trade currency")
    execution_timestamp: datetime = Field(..., description="Execution timestamp")
    venue: str = Field(..., description="Execution venue")
    client_category: str = Field(..., description="Client categorization")
    order_type: str = Field(..., description="Order type")
    
    @validator('quantity')
    def validate_quantity(cls, v):
        """Validate quantity is positive."""
        if v <= 0:
            raise ValueError("Quantity must be positive")
        return v
    
    @validator('price')
    def validate_price(cls, v):
        """Validate price is positive."""
        if v <= 0:
            raise ValueError("Price must be positive")
        return v
    
    @validator('client_category')
    def validate_client_category(cls, v):
        """Validate client category."""
        allowed_categories = ['RETAIL', 'PROFESSIONAL', 'ELIGIBLE_COUNTERPARTY']
        if v.upper() not in allowed_categories:
            raise ValueError(f"Client category must be one of: {allowed_categories}")
        return v.upper()


class MiFIDComplianceResponse(BaseModel):
    """MiFID II compliance validation response."""
    
    trade_id: str
    compliance_status: str
    transaction_reporting_status: str
    best_execution_status: str
    market_abuse_status: str
    client_protection_status: str
    risk_score: float
    violations: List[str]
    recommendations: List[str]
    reporting_deadline: Optional[datetime]
    timestamp: datetime


@router.post("/validate", response_model=MiFIDComplianceResponse)
async def validate_mifid_compliance(
    transaction: MiFIDTransactionRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Validate MiFID II compliance for a financial transaction.
    
    Performs comprehensive MiFID II compliance checks including:
    - Transaction reporting requirements
    - Best execution obligations
    - Market abuse surveillance
    - Client protection measures
    """
    try:
        # Log compliance validation start
        compliance_logger.info(
            f"Starting MiFID II compliance validation - Trade ID: {transaction.trade_id}, "
            f"Client: {transaction.client_id}, "
            f"Instrument: {transaction.instrument_id}, "
            f"Venue: {transaction.venue}"
        )
        
        # Validate transaction reporting requirements
        reporting_status = await transaction_reporting.validate_reporting_requirements(
            trade_id=transaction.trade_id,
            instrument_type=transaction.instrument_type,
            quantity=transaction.quantity,
            price=transaction.price,
            execution_timestamp=transaction.execution_timestamp,
            venue=transaction.venue
        )
        
        # Assess best execution compliance
        best_execution_status = await best_execution.assess_best_execution(
            trade_id=transaction.trade_id,
            instrument_id=transaction.instrument_id,
            quantity=transaction.quantity,
            price=transaction.price,
            venue=transaction.venue,
            order_type=transaction.order_type,
            client_category=transaction.client_category
        )
        
        # Perform market abuse surveillance
        market_abuse_status = await mifid_compliance.market_abuse_surveillance(
            trade_id=transaction.trade_id,
            client_id=transaction.client_id,
            instrument_id=transaction.instrument_id,
            quantity=transaction.quantity,
            price=transaction.price,
            execution_timestamp=transaction.execution_timestamp
        )
        
        # Validate client protection measures
        client_protection_status = await mifid_compliance.validate_client_protection(
            client_id=transaction.client_id,
            client_category=transaction.client_category,
            instrument_type=transaction.instrument_type,
            trade_value=transaction.quantity * transaction.price
        )
        
        # Calculate reporting deadline (T+1 for MiFID II)
        reporting_deadline = None
        if reporting_status['requires_reporting']:
            reporting_deadline = transaction.execution_timestamp + timedelta(days=1)
        
        # Calculate overall risk score
        risk_score = await mifid_compliance.calculate_risk_score(
            reporting_status, best_execution_status, market_abuse_status, client_protection_status
        )
        
        # Compile violations and recommendations
        violations = []
        recommendations = []
        
        if reporting_status['status'] == 'VIOLATION':
            violations.extend(reporting_status['violations'])
            recommendations.extend(reporting_status['recommendations'])
        
        if best_execution_status['status'] == 'VIOLATION':
            violations.extend(best_execution_status['violations'])
            recommendations.extend(best_execution_status['recommendations'])
        
        if market_abuse_status['suspicious']:
            violations.append("Potential market abuse detected")
            recommendations.append("Review transaction for market manipulation indicators")
        
        if client_protection_status['status'] == 'VIOLATION':
            violations.extend(client_protection_status['violations'])
            recommendations.extend(client_protection_status['recommendations'])
        
        # Determine overall compliance status
        overall_status = "COMPLIANT"
        if violations:
            overall_status = "NON_COMPLIANT"
        elif risk_score > 0.7:
            overall_status = "HIGH_RISK"
        
        # Log compliance validation result
        compliance_logger.info(
            f"MiFID II compliance validation completed - Trade ID: {transaction.trade_id}, "
            f"Status: {overall_status}, "
            f"Risk Score: {risk_score}, "
            f"Requires Reporting: {reporting_status['requires_reporting']}"
        )
        
        return MiFIDComplianceResponse(
            trade_id=transaction.trade_id,
            compliance_status=overall_status,
            transaction_reporting_status=reporting_status['status'],
            best_execution_status=best_execution_status['status'],
            market_abuse_status=market_abuse_status['status'],
            client_protection_status=client_protection_status['status'],
            risk_score=risk_score,
            violations=violations,
            recommendations=recommendations,
            reporting_deadline=reporting_deadline,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"MiFID II compliance validation failed for trade {transaction.trade_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"MiFID II compliance validation failed: {str(e)}"
        )


@router.post("/transaction-report")
async def submit_transaction_report(
    trade_id: str,
    competent_authority: str = Query(..., description="Competent authority identifier"),
    db: AsyncSession = Depends(get_db)
):
    """
    Submit MiFID II transaction report to competent authority.
    
    Handles submission of transaction reports as required by
    MiFID II Article 26 reporting obligations.
    """
    try:
        # Log report submission start
        compliance_logger.info(
            f"Starting MiFID II transaction report submission - Trade ID: {trade_id}, "
            f"Authority: {competent_authority}"
        )
        
        # Submit transaction report
        report_result = await transaction_reporting.submit_transaction_report(
            trade_id=trade_id,
            competent_authority=competent_authority
        )
        
        # Log successful submission
        compliance_logger.info(
            f"MiFID II transaction report submitted - Trade ID: {trade_id}, "
            f"Report ID: {report_result['report_id']}, "
            f"Authority: {competent_authority}"
        )
        
        return {
            "trade_id": trade_id,
            "report_id": report_result['report_id'],
            "competent_authority": competent_authority,
            "submission_timestamp": report_result['timestamp'],
            "status": "SUBMITTED",
            "acknowledgment": report_result.get('acknowledgment')
        }
        
    except Exception as e:
        logger.error(f"MiFID II transaction report submission failed for trade {trade_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Transaction report submission failed: {str(e)}"
        )


@router.get("/best-execution/{client_id}")
async def get_best_execution_report(
    client_id: str,
    period_start: datetime = Query(...),
    period_end: datetime = Query(...),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate best execution report for a client.
    
    Provides detailed analysis of execution quality and venue
    performance as required by MiFID II RTS 28.
    """
    try:
        # Generate best execution report
        report = await best_execution.generate_best_execution_report(
            client_id=client_id,
            period_start=period_start,
            period_end=period_end
        )
        
        return {
            "client_id": client_id,
            "period_start": period_start,
            "period_end": period_end,
            "best_execution_report": report,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Best execution report generation failed for client {client_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Best execution report generation failed: {str(e)}"
        )


@router.get("/market-abuse-surveillance")
async def get_market_abuse_alerts(
    start_date: datetime = Query(...),
    end_date: datetime = Query(...),
    alert_type: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Get market abuse surveillance alerts.
    
    Retrieves suspicious transaction alerts generated by
    market abuse surveillance systems.
    """
    try:
        # Get market abuse alerts
        alerts = await mifid_compliance.get_market_abuse_alerts(
            start_date=start_date,
            end_date=end_date,
            alert_type=alert_type
        )
        
        return {
            "start_date": start_date,
            "end_date": end_date,
            "alert_type": alert_type,
            "alerts": alerts,
            "total_alerts": len(alerts),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Market abuse alerts retrieval failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Market abuse alerts retrieval failed: {str(e)}"
        )


@router.get("/health")
async def mifid_health_check():
    """MiFID II compliance module health check."""
    try:
        # Check MiFID II engine status
        engine_status = await mifid_compliance.health_check()
        
        # Check best execution engine status
        best_execution_status = await best_execution.health_check()
        
        # Check transaction reporting status
        reporting_status = await transaction_reporting.health_check()
        
        return {
            "status": "healthy",
            "module": "MiFID II Compliance",
            "engine_status": engine_status,
            "best_execution_status": best_execution_status,
            "reporting_status": reporting_status,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"MiFID II health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"MiFID II compliance module unhealthy: {str(e)}"
        )
