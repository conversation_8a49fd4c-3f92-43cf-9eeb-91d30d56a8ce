"""
AthenaTrader Live Execution Module

Real-time trading execution service with multi-broker connectivity, comprehensive risk management,
strategy deployment pipeline, execution quality analytics, and real-time XAI integration for
institutional-grade live trading operations.
"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from app.core.config import settings
from app.core.logging import setup_logging
from app.core.database import init_db
from app.routers import (
    health, orders, executions, positions, strategies,
    risk_management, brokers, analytics, monitoring, hft_management
)

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting AthenaTrader Live Execution Module...")
    await init_db()
    logger.info("Database initialized")

    # Initialize live execution components with Phase 10 HFT capabilities
    from app.engine.execution_gateway import ExecutionGateway
    from app.engine.risk_manager import RealTimeRiskManager
    from app.engine.advanced_risk_manager import AdvancedRiskManager
    from app.engine.strategy_deployer import StrategyDeployer
    from app.engine.execution_analytics import ExecutionAnalytics
    from app.engine.market_data import MarketDataManager
    from app.engine.order_manager import OrderManager
    from app.engine.position_manager import PositionManager
    from app.engine.xai_integration import XAIIntegration
    from app.engine.ultra_low_latency import (
        UltraLowLatencyEngine, DPDKConfiguration, FPGAConfiguration
    )
    from app.engine.smart_order_router import SmartOrderRouter

    # Initialize HFT configurations
    dpdk_config = DPDKConfiguration(
        enabled=False,  # Set to True in production with proper DPDK setup
        core_mask="0x3",
        memory_channels=4,
        huge_pages=1024
    )

    fpga_config = FPGAConfiguration(
        enabled=False,  # Set to True in production with FPGA hardware
        device_id="",
        bitstream_path="",
        clock_frequency_mhz=250
    )

    # Store engines in app state with HFT capabilities
    app.state.execution_gateway = ExecutionGateway()
    app.state.risk_manager = RealTimeRiskManager()
    app.state.advanced_risk_manager = AdvancedRiskManager(fpga_config)
    app.state.strategy_deployer = StrategyDeployer()
    app.state.execution_analytics = ExecutionAnalytics()
    app.state.market_data_manager = MarketDataManager()
    app.state.order_manager = OrderManager()
    app.state.position_manager = PositionManager()
    app.state.xai_integration = XAIIntegration()
    app.state.ultra_low_latency_engine = UltraLowLatencyEngine(dpdk_config, fpga_config)
    app.state.smart_order_router = SmartOrderRouter(app.state.market_data_manager)

    # Initialize connections and HFT components
    await app.state.execution_gateway.initialize()
    await app.state.market_data_manager.initialize()
    await app.state.risk_manager.initialize()
    await app.state.advanced_risk_manager.initialize()
    await app.state.ultra_low_latency_engine.initialize()
    await app.state.smart_order_router.initialize()

    logger.info("Live execution engines with HFT capabilities initialized successfully")

    yield

    # Shutdown
    logger.info("Shutting down AthenaTrader Live Execution Module with HFT capabilities...")

    # Cleanup HFT components
    if hasattr(app.state, 'ultra_low_latency_engine'):
        await app.state.ultra_low_latency_engine.cleanup()
    if hasattr(app.state, 'advanced_risk_manager'):
        await app.state.advanced_risk_manager.cleanup()
    if hasattr(app.state, 'smart_order_router'):
        await app.state.smart_order_router.cleanup()

    logger.info("HFT components shutdown completed")

    # Cleanup engines
    if hasattr(app.state, 'execution_gateway'):
        await app.state.execution_gateway.cleanup()
    if hasattr(app.state, 'risk_manager'):
        await app.state.risk_manager.cleanup()
    if hasattr(app.state, 'strategy_deployer'):
        await app.state.strategy_deployer.cleanup()
    if hasattr(app.state, 'execution_analytics'):
        await app.state.execution_analytics.cleanup()
    if hasattr(app.state, 'market_data_manager'):
        await app.state.market_data_manager.cleanup()
    if hasattr(app.state, 'order_manager'):
        await app.state.order_manager.cleanup()
    if hasattr(app.state, 'position_manager'):
        await app.state.position_manager.cleanup()
    if hasattr(app.state, 'xai_integration'):
        await app.state.xai_integration.cleanup()


# Create FastAPI application
app = FastAPI(
    title="AthenaTrader Live Execution Module",
    description="Real-time trading execution service with multi-broker connectivity and comprehensive risk management",
    version="0.1.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(orders.router, prefix="/orders", tags=["orders"])
app.include_router(executions.router, prefix="/executions", tags=["executions"])
app.include_router(positions.router, prefix="/positions", tags=["positions"])
app.include_router(strategies.router, prefix="/strategies", tags=["strategies"])
app.include_router(risk_management.router, prefix="/risk", tags=["risk_management"])
app.include_router(brokers.router, prefix="/brokers", tags=["brokers"])
app.include_router(analytics.router, prefix="/analytics", tags=["analytics"])
app.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])
app.include_router(hft_management.router, prefix="/hft", tags=["hft_management"])


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "AthenaTrader Live Execution Module",
        "version": "0.1.0",
        "status": "operational",
        "capabilities": [
            "multi_broker_execution",
            "fix_protocol_support",
            "real_time_risk_management",
            "strategy_deployment_pipeline",
            "execution_quality_analytics",
            "market_microstructure_analysis",
            "real_time_xai_integration",
            "automated_risk_controls",
            "canary_releases",
            "performance_monitoring",
            "regulatory_compliance",
            "multi_asset_support"
        ]
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )
