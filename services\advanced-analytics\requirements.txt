# AthenaTrader Phase 11 Advanced Analytics Engine Dependencies

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Machine Learning & Deep Learning
tensorflow==2.15.0
torch==2.1.0
torchvision==0.16.0
scikit-learn==1.3.2
numpy==1.24.3
pandas==2.1.4
scipy==1.11.4

# Time Series & Financial Analysis
arch==6.2.0  # GARCH models
hmmlearn==0.3.0  # Hidden Markov Models
statsmodels==0.14.0
ta-lib==0.4.28  # Technical analysis
yfinance==0.2.28
ccxt==4.1.64  # Cryptocurrency exchange integration

# Natural Language Processing & Sentiment Analysis
transformers==4.36.0
torch-audio==2.1.0
nltk==3.8.1
textblob==0.17.1
vaderSentiment==3.3.2
newspaper3k==0.2.8

# Real-time Data Processing
kafka-python==2.0.2
redis==5.0.1
celery==5.3.4
websockets==12.0

# Database & Storage
asyncpg==0.29.0
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9

# Blockchain & Compliance
web3==6.12.0
hyperledger-fabric-sdk-py==1.0.0
cryptography==41.0.8

# Data Visualization & Monitoring
plotly==5.17.0
matplotlib==3.8.2
seaborn==0.13.0
dash==2.16.1

# HTTP & API
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Utilities
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
loguru==0.7.2

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Monitoring & Observability
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0

# Alternative Data Sources
tweepy==4.14.0  # Twitter API
feedparser==6.0.10  # RSS feeds
beautifulsoup4==4.12.2  # Web scraping
