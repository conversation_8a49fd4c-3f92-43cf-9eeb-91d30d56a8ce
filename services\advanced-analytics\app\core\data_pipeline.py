"""
AthenaTrader Phase 11 Advanced Analytics Engine Data Pipeline

Real-time data processing pipeline for:
- Market data ingestion and normalization
- Alternative data integration (news, social media)
- Feature engineering for ML models
- Real-time streaming to models and analytics
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import aiohttp
import yfinance as yf
from .config import settings
from .redis_client import data_stream, model_cache
from .database import DatabaseManager

logger = logging.getLogger("data_pipeline")


class DataPipeline:
    """Real-time data pipeline for Advanced Analytics Engine."""
    
    def __init__(self):
        self.is_running = False
        self.market_data_tasks = []
        self.alternative_data_tasks = []
        self.supported_symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "NVDA", "META", "NFLX"]
        self.crypto_symbols = ["BTC-USD", "ETH-USD", "ADA-USD", "DOT-USD"]
        
        # Data buffers for real-time processing
        self.market_data_buffer = {}
        self.news_buffer = {}
        self.sentiment_buffer = {}
    
    async def initialize(self):
        """Initialize data pipeline."""
        try:
            logger.info("Initializing Data Pipeline...")
            
            # Initialize market data buffers
            for symbol in self.supported_symbols + self.crypto_symbols:
                self.market_data_buffer[symbol] = {
                    "prices": [],
                    "volumes": [],
                    "timestamps": [],
                    "features": {}
                }
            
            # Start data collection tasks
            await self._start_market_data_collection()
            await self._start_alternative_data_collection()
            
            self.is_running = True
            logger.info("✓ Data Pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize data pipeline: {e}")
            raise
    
    async def _start_market_data_collection(self):
        """Start real-time market data collection."""
        try:
            # Start market data collection for each symbol
            for symbol in self.supported_symbols:
                task = asyncio.create_task(self._collect_market_data(symbol))
                self.market_data_tasks.append(task)
            
            # Start crypto data collection
            for symbol in self.crypto_symbols:
                task = asyncio.create_task(self._collect_crypto_data(symbol))
                self.market_data_tasks.append(task)
            
            logger.info(f"Started market data collection for {len(self.supported_symbols + self.crypto_symbols)} symbols")
            
        except Exception as e:
            logger.error(f"Failed to start market data collection: {e}")
            raise
    
    async def _collect_market_data(self, symbol: str):
        """Collect real-time market data for a symbol."""
        while self.is_running:
            try:
                # Fetch latest market data (using yfinance for demonstration)
                ticker = yf.Ticker(symbol)
                
                # Get recent data
                data = ticker.history(period="1d", interval="1m")
                if not data.empty:
                    latest_data = data.iloc[-1]
                    
                    # Update buffer
                    buffer = self.market_data_buffer[symbol]
                    buffer["prices"].append({
                        "open": latest_data["Open"],
                        "high": latest_data["High"],
                        "low": latest_data["Low"],
                        "close": latest_data["Close"]
                    })
                    buffer["volumes"].append(latest_data["Volume"])
                    buffer["timestamps"].append(datetime.now())
                    
                    # Keep only recent data (last 1000 points)
                    if len(buffer["prices"]) > 1000:
                        buffer["prices"] = buffer["prices"][-1000:]
                        buffer["volumes"] = buffer["volumes"][-1000:]
                        buffer["timestamps"] = buffer["timestamps"][-1000:]
                    
                    # Calculate technical features
                    await self._calculate_technical_features(symbol)
                    
                    # Publish to stream
                    market_data = {
                        "symbol": symbol,
                        "price": latest_data["Close"],
                        "volume": latest_data["Volume"],
                        "timestamp": datetime.now().isoformat()
                    }
                    await data_stream.publish_market_data(symbol, market_data)
                
                # Wait before next update
                await asyncio.sleep(settings.MARKET_DATA_UPDATE_INTERVAL_MS / 1000)
                
            except Exception as e:
                logger.error(f"Error collecting market data for {symbol}: {e}")
                await asyncio.sleep(5)  # Wait before retry
    
    async def _collect_crypto_data(self, symbol: str):
        """Collect real-time cryptocurrency data."""
        while self.is_running:
            try:
                # For demonstration, use yfinance for crypto data
                # In production, use dedicated crypto APIs like Binance, Coinbase
                ticker = yf.Ticker(symbol)
                data = ticker.history(period="1d", interval="1m")
                
                if not data.empty:
                    latest_data = data.iloc[-1]
                    
                    # Update buffer
                    buffer = self.market_data_buffer[symbol]
                    buffer["prices"].append({
                        "open": latest_data["Open"],
                        "high": latest_data["High"],
                        "low": latest_data["Low"],
                        "close": latest_data["Close"]
                    })
                    buffer["volumes"].append(latest_data["Volume"])
                    buffer["timestamps"].append(datetime.now())
                    
                    # Keep only recent data
                    if len(buffer["prices"]) > 1000:
                        buffer["prices"] = buffer["prices"][-1000:]
                        buffer["volumes"] = buffer["volumes"][-1000:]
                        buffer["timestamps"] = buffer["timestamps"][-1000:]
                    
                    # Calculate crypto-specific features
                    await self._calculate_crypto_features(symbol)
                
                await asyncio.sleep(settings.MARKET_DATA_UPDATE_INTERVAL_MS / 1000)
                
            except Exception as e:
                logger.error(f"Error collecting crypto data for {symbol}: {e}")
                await asyncio.sleep(5)
    
    async def _start_alternative_data_collection(self):
        """Start alternative data collection (news, social media)."""
        try:
            # Start news collection
            news_task = asyncio.create_task(self._collect_news_data())
            self.alternative_data_tasks.append(news_task)
            
            # Start social media sentiment collection
            sentiment_task = asyncio.create_task(self._collect_social_sentiment())
            self.alternative_data_tasks.append(sentiment_task)
            
            logger.info("Started alternative data collection")
            
        except Exception as e:
            logger.error(f"Failed to start alternative data collection: {e}")
            raise
    
    async def _collect_news_data(self):
        """Collect financial news data."""
        while self.is_running:
            try:
                # For demonstration, simulate news collection
                # In production, integrate with NewsAPI, Bloomberg, Reuters
                
                for symbol in self.supported_symbols:
                    # Simulate news headlines
                    news_items = [
                        f"{symbol} reports strong quarterly earnings",
                        f"Analysts upgrade {symbol} price target",
                        f"{symbol} announces new product launch",
                        f"Market volatility affects {symbol} trading"
                    ]
                    
                    # Store in buffer
                    if symbol not in self.news_buffer:
                        self.news_buffer[symbol] = []
                    
                    for headline in news_items[:1]:  # Take one headline
                        news_item = {
                            "headline": headline,
                            "timestamp": datetime.now(),
                            "source": "demo_news",
                            "url": f"https://example.com/news/{symbol.lower()}"
                        }
                        self.news_buffer[symbol].append(news_item)
                        
                        # Keep only recent news (last 100 items)
                        if len(self.news_buffer[symbol]) > 100:
                            self.news_buffer[symbol] = self.news_buffer[symbol][-100:]
                
                # Wait before next collection
                await asyncio.sleep(settings.SENTIMENT_UPDATE_INTERVAL_MINUTES * 60)
                
            except Exception as e:
                logger.error(f"Error collecting news data: {e}")
                await asyncio.sleep(60)
    
    async def _collect_social_sentiment(self):
        """Collect social media sentiment data."""
        while self.is_running:
            try:
                # For demonstration, simulate social sentiment
                # In production, integrate with Twitter API, Reddit API
                
                for symbol in self.supported_symbols:
                    # Simulate sentiment scores
                    sentiment_score = np.random.uniform(-1, 1)
                    sentiment_label = "positive" if sentiment_score > 0.1 else "negative" if sentiment_score < -0.1 else "neutral"
                    
                    sentiment_data = {
                        "symbol": symbol,
                        "sentiment_score": sentiment_score,
                        "sentiment_label": sentiment_label,
                        "confidence": np.random.uniform(0.6, 0.9),
                        "source": "social_media",
                        "timestamp": datetime.now()
                    }
                    
                    # Store in buffer
                    if symbol not in self.sentiment_buffer:
                        self.sentiment_buffer[symbol] = []
                    
                    self.sentiment_buffer[symbol].append(sentiment_data)
                    
                    # Keep only recent sentiment (last 50 items)
                    if len(self.sentiment_buffer[symbol]) > 50:
                        self.sentiment_buffer[symbol] = self.sentiment_buffer[symbol][-50:]
                    
                    # Publish to stream
                    await data_stream.publish_sentiment(symbol, sentiment_data)
                
                await asyncio.sleep(settings.SENTIMENT_UPDATE_INTERVAL_MINUTES * 60)
                
            except Exception as e:
                logger.error(f"Error collecting social sentiment: {e}")
                await asyncio.sleep(60)
    
    async def _calculate_technical_features(self, symbol: str):
        """Calculate technical analysis features for a symbol."""
        try:
            buffer = self.market_data_buffer[symbol]
            if len(buffer["prices"]) < 20:
                return  # Need minimum data for calculations
            
            # Convert to pandas for easier calculations
            prices_df = pd.DataFrame(buffer["prices"])
            volumes = pd.Series(buffer["volumes"])
            
            # Calculate technical indicators
            features = {}
            
            # Moving averages
            features["sma_5"] = prices_df["close"].rolling(5).mean().iloc[-1] if len(prices_df) >= 5 else None
            features["sma_20"] = prices_df["close"].rolling(20).mean().iloc[-1] if len(prices_df) >= 20 else None
            features["ema_12"] = prices_df["close"].ewm(span=12).mean().iloc[-1] if len(prices_df) >= 12 else None
            
            # RSI (Relative Strength Index)
            delta = prices_df["close"].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            features["rsi"] = (100 - (100 / (1 + rs))).iloc[-1] if len(prices_df) >= 14 else None
            
            # MACD
            ema_12 = prices_df["close"].ewm(span=12).mean()
            ema_26 = prices_df["close"].ewm(span=26).mean()
            features["macd"] = (ema_12 - ema_26).iloc[-1] if len(prices_df) >= 26 else None
            
            # Bollinger Bands
            sma_20 = prices_df["close"].rolling(20).mean()
            std_20 = prices_df["close"].rolling(20).std()
            features["bb_upper"] = (sma_20 + 2 * std_20).iloc[-1] if len(prices_df) >= 20 else None
            features["bb_lower"] = (sma_20 - 2 * std_20).iloc[-1] if len(prices_df) >= 20 else None
            
            # Volume indicators
            features["volume_sma"] = volumes.rolling(20).mean().iloc[-1] if len(volumes) >= 20 else None
            features["volume_ratio"] = volumes.iloc[-1] / features["volume_sma"] if features["volume_sma"] else None
            
            # Volatility
            returns = prices_df["close"].pct_change()
            features["volatility"] = returns.rolling(20).std().iloc[-1] if len(returns) >= 20 else None
            
            # Update buffer
            buffer["features"] = features
            
        except Exception as e:
            logger.error(f"Error calculating technical features for {symbol}: {e}")
    
    async def _calculate_crypto_features(self, symbol: str):
        """Calculate cryptocurrency-specific features."""
        try:
            buffer = self.market_data_buffer[symbol]
            if len(buffer["prices"]) < 10:
                return
            
            # Convert to pandas
            prices_df = pd.DataFrame(buffer["prices"])
            
            # Crypto-specific features
            features = buffer.get("features", {})
            
            # Price momentum
            if len(prices_df) >= 5:
                features["momentum_5"] = (prices_df["close"].iloc[-1] / prices_df["close"].iloc[-5] - 1) * 100
            
            # Intraday volatility
            if len(prices_df) >= 1:
                features["intraday_volatility"] = ((prices_df["high"].iloc[-1] - prices_df["low"].iloc[-1]) / prices_df["open"].iloc[-1]) * 100
            
            # Update buffer
            buffer["features"] = features
            
        except Exception as e:
            logger.error(f"Error calculating crypto features for {symbol}: {e}")
    
    async def get_market_data(self, symbol: str, lookback_minutes: int = 100) -> Optional[Dict[str, Any]]:
        """Get recent market data for a symbol."""
        try:
            if symbol not in self.market_data_buffer:
                return None
            
            buffer = self.market_data_buffer[symbol]
            
            # Get recent data
            cutoff_time = datetime.now() - timedelta(minutes=lookback_minutes)
            recent_indices = [
                i for i, ts in enumerate(buffer["timestamps"])
                if ts >= cutoff_time
            ]
            
            if not recent_indices:
                return None
            
            recent_prices = [buffer["prices"][i] for i in recent_indices]
            recent_volumes = [buffer["volumes"][i] for i in recent_indices]
            recent_timestamps = [buffer["timestamps"][i] for i in recent_indices]
            
            return {
                "symbol": symbol,
                "prices": recent_prices,
                "volumes": recent_volumes,
                "timestamps": [ts.isoformat() for ts in recent_timestamps],
                "features": buffer.get("features", {}),
                "data_points": len(recent_prices)
            }
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return None
    
    async def get_features_array(self, symbol: str, lookback_periods: int = 100) -> Optional[np.ndarray]:
        """Get feature array for ML model input."""
        try:
            market_data = await self.get_market_data(symbol, lookback_periods)
            if not market_data or len(market_data["prices"]) < lookback_periods:
                return None
            
            # Convert to numpy array (OHLCV format)
            prices = market_data["prices"][-lookback_periods:]
            volumes = market_data["volumes"][-lookback_periods:]
            
            features = []
            for i, price in enumerate(prices):
                features.append([
                    price["open"],
                    price["high"],
                    price["low"],
                    price["close"],
                    volumes[i]
                ])
            
            return np.array(features)
            
        except Exception as e:
            logger.error(f"Error getting features array for {symbol}: {e}")
            return None
    
    async def get_news_sentiment(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get recent news and sentiment for a symbol."""
        try:
            news_data = self.news_buffer.get(symbol, [])
            sentiment_data = self.sentiment_buffer.get(symbol, [])
            
            # Get recent data (last 24 hours)
            cutoff_time = datetime.now() - timedelta(hours=24)
            
            recent_news = [
                item for item in news_data
                if item["timestamp"] >= cutoff_time
            ]
            
            recent_sentiment = [
                item for item in sentiment_data
                if item["timestamp"] >= cutoff_time
            ]
            
            # Calculate aggregate sentiment
            if recent_sentiment:
                avg_sentiment = np.mean([item["sentiment_score"] for item in recent_sentiment])
                sentiment_counts = {}
                for item in recent_sentiment:
                    label = item["sentiment_label"]
                    sentiment_counts[label] = sentiment_counts.get(label, 0) + 1
            else:
                avg_sentiment = 0.0
                sentiment_counts = {}
            
            return {
                "symbol": symbol,
                "news_count": len(recent_news),
                "recent_news": recent_news[-5:],  # Last 5 news items
                "sentiment_count": len(recent_sentiment),
                "average_sentiment": avg_sentiment,
                "sentiment_distribution": sentiment_counts,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting news sentiment for {symbol}: {e}")
            return None
    
    async def cleanup(self):
        """Cleanup data pipeline resources."""
        try:
            self.is_running = False
            
            # Cancel all tasks
            for task in self.market_data_tasks + self.alternative_data_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete
            if self.market_data_tasks:
                await asyncio.gather(*self.market_data_tasks, return_exceptions=True)
            
            if self.alternative_data_tasks:
                await asyncio.gather(*self.alternative_data_tasks, return_exceptions=True)
            
            logger.info("Data Pipeline cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during data pipeline cleanup: {e}")
