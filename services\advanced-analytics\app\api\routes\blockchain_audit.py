"""
AthenaTrader Phase 4: Blockchain Audit Trail Module

Hyperledger Fabric-based immutable audit trail system for comprehensive
regulatory compliance and transparent trading operations.

Key Features:
- Immutable transaction logging for all trading activities
- Smart contracts for automated compliance rule enforcement
- Multi-signature approval workflows for critical operations
- Integration with existing AthenaTrader services (ports 8002-8006)
"""

import asyncio
import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_db
from ...core.logging_config import get_logger
from ...compliance.blockchain_audit_engine import BlockchainAuditEngine, HyperledgerFabricConnector
from ...models.compliance import AuditRecord, BlockchainTransaction, ComplianceStatus

# Setup logging with Winston-style configuration
logger = get_logger(__name__)
compliance_logger = get_logger("compliance.blockchain_audit")

# Create router
router = APIRouter()

# Initialize blockchain audit engine
blockchain_audit = BlockchainAuditEngine()
fabric_connector = HyperledgerFabricConnector()


class AuditEventRequest(BaseModel):
    """Audit event recording request."""
    
    event_type: str = Field(..., description="Type of audit event")
    entity_id: str = Field(..., description="Entity identifier")
    user_id: str = Field(..., description="User identifier")
    event_data: Dict[str, Any] = Field(..., description="Event data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    compliance_framework: Optional[str] = Field(None, description="Applicable compliance framework")
    
    @validator('event_type')
    def validate_event_type(cls, v):
        """Validate event type."""
        allowed_types = [
            'TRADE_EXECUTION', 'STRATEGY_DEPLOYMENT', 'RISK_BREACH', 'COMPLIANCE_CHECK',
            'DATA_ACCESS', 'CONSENT_CHANGE', 'BREACH_NOTIFICATION', 'REGULATORY_REPORT'
        ]
        if v.upper() not in allowed_types:
            raise ValueError(f"Event type must be one of: {allowed_types}")
        return v.upper()


class SmartContractRequest(BaseModel):
    """Smart contract deployment request."""
    
    contract_name: str = Field(..., description="Smart contract name")
    contract_code: str = Field(..., description="Smart contract code")
    compliance_rules: List[Dict[str, Any]] = Field(..., description="Compliance rules to enforce")
    approval_workflow: Dict[str, Any] = Field(..., description="Approval workflow configuration")
    
    @validator('contract_name')
    def validate_contract_name(cls, v):
        """Validate contract name format."""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError("Contract name must be alphanumeric with underscores or hyphens")
        return v


class AuditTrailResponse(BaseModel):
    """Audit trail response."""
    
    record_id: str
    transaction_hash: str
    block_number: int
    event_type: str
    entity_id: str
    user_id: str
    timestamp: datetime
    verification_status: str
    compliance_status: str


@router.post("/record-event", response_model=AuditTrailResponse)
async def record_audit_event(
    event_request: AuditEventRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Record an audit event to the blockchain.
    
    Creates an immutable audit record on the Hyperledger Fabric
    blockchain with cryptographic verification and compliance validation.
    """
    try:
        # Log audit event recording start
        compliance_logger.info(
            f"Recording audit event - Type: {event_request.event_type}, "
            f"Entity: {event_request.entity_id}, "
            f"User: {event_request.user_id}"
        )
        
        # Record event to blockchain
        audit_result = await blockchain_audit.record_audit_event(
            event_type=event_request.event_type,
            entity_id=event_request.entity_id,
            user_id=event_request.user_id,
            event_data=event_request.event_data,
            metadata=event_request.metadata or {},
            compliance_framework=event_request.compliance_framework
        )
        
        # Validate compliance if framework specified
        compliance_status = "NOT_APPLICABLE"
        if event_request.compliance_framework:
            compliance_validation = await blockchain_audit.validate_compliance(
                audit_result['record_id'],
                event_request.compliance_framework
            )
            compliance_status = compliance_validation['status']
        
        # Log successful recording
        compliance_logger.info(
            f"Audit event recorded - Record ID: {audit_result['record_id']}, "
            f"Transaction Hash: {audit_result['transaction_hash']}, "
            f"Block: {audit_result['block_number']}"
        )
        
        return AuditTrailResponse(
            record_id=audit_result['record_id'],
            transaction_hash=audit_result['transaction_hash'],
            block_number=audit_result['block_number'],
            event_type=event_request.event_type,
            entity_id=event_request.entity_id,
            user_id=event_request.user_id,
            timestamp=audit_result['timestamp'],
            verification_status=audit_result['verification_status'],
            compliance_status=compliance_status
        )
        
    except Exception as e:
        logger.error(f"Audit event recording failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Audit event recording failed: {str(e)}"
        )


@router.post("/deploy-smart-contract")
async def deploy_smart_contract(
    contract_request: SmartContractRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Deploy a smart contract for automated compliance enforcement.
    
    Deploys smart contracts to the Hyperledger Fabric network
    for automated compliance rule enforcement and approval workflows.
    """
    try:
        # Log smart contract deployment start
        compliance_logger.info(
            f"Deploying smart contract - Name: {contract_request.contract_name}, "
            f"Rules: {len(contract_request.compliance_rules)}"
        )
        
        # Deploy smart contract
        deployment_result = await blockchain_audit.deploy_smart_contract(
            contract_name=contract_request.contract_name,
            contract_code=contract_request.contract_code,
            compliance_rules=contract_request.compliance_rules,
            approval_workflow=contract_request.approval_workflow
        )
        
        # Log successful deployment
        compliance_logger.info(
            f"Smart contract deployed - Name: {contract_request.contract_name}, "
            f"Contract ID: {deployment_result['contract_id']}, "
            f"Transaction Hash: {deployment_result['transaction_hash']}"
        )
        
        return {
            "contract_name": contract_request.contract_name,
            "contract_id": deployment_result['contract_id'],
            "transaction_hash": deployment_result['transaction_hash'],
            "deployment_status": deployment_result['status'],
            "compliance_rules_count": len(contract_request.compliance_rules),
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Smart contract deployment failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Smart contract deployment failed: {str(e)}"
        )


@router.get("/audit-trail/{entity_id}")
async def get_audit_trail(
    entity_id: str,
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    event_type: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    db: AsyncSession = Depends(get_db)
):
    """
    Retrieve audit trail for an entity.
    
    Fetches immutable audit records from the blockchain
    with optional filtering by date range and event type.
    """
    try:
        # Set default date range if not provided
        if end_date is None:
            end_date = datetime.now()
        if start_date is None:
            start_date = end_date - timedelta(days=30)
        
        # Retrieve audit trail
        audit_trail = await blockchain_audit.get_audit_trail(
            entity_id=entity_id,
            start_date=start_date,
            end_date=end_date,
            event_type=event_type,
            limit=limit
        )
        
        return {
            "entity_id": entity_id,
            "start_date": start_date,
            "end_date": end_date,
            "event_type": event_type,
            "audit_records": audit_trail['records'],
            "total_records": audit_trail['total_count'],
            "verification_status": audit_trail['verification_status'],
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Audit trail retrieval failed for entity {entity_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Audit trail retrieval failed: {str(e)}"
        )


@router.post("/verify-integrity")
async def verify_blockchain_integrity(
    start_block: int = Query(0),
    end_block: Optional[int] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Verify blockchain integrity and audit trail consistency.
    
    Performs cryptographic verification of blockchain integrity
    including hash chain validation and digital signature verification.
    """
    try:
        # Log integrity verification start
        compliance_logger.info(
            f"Starting blockchain integrity verification - Blocks: {start_block} to {end_block or 'latest'}"
        )
        
        # Verify blockchain integrity
        verification_result = await blockchain_audit.verify_blockchain_integrity(
            start_block=start_block,
            end_block=end_block
        )
        
        # Log verification completion
        compliance_logger.info(
            f"Blockchain integrity verification completed - Status: {verification_result['status']}, "
            f"Verified Blocks: {verification_result['verified_blocks']}, "
            f"Failed Blocks: {len(verification_result['failed_blocks'])}"
        )
        
        return {
            "verification_status": verification_result['status'],
            "start_block": start_block,
            "end_block": verification_result['end_block'],
            "total_blocks_verified": verification_result['verified_blocks'],
            "failed_blocks": verification_result['failed_blocks'],
            "hash_chain_valid": verification_result['hash_chain_valid'],
            "signature_verification": verification_result['signature_verification'],
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Blockchain integrity verification failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Blockchain integrity verification failed: {str(e)}"
        )


@router.get("/compliance-report/{framework}")
async def generate_compliance_report(
    framework: str,
    start_date: datetime = Query(...),
    end_date: datetime = Query(...),
    entity_id: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate compliance report from blockchain audit trail.
    
    Creates comprehensive compliance reports based on immutable
    blockchain records for regulatory submissions and audits.
    """
    try:
        # Log compliance report generation start
        compliance_logger.info(
            f"Generating compliance report - Framework: {framework}, "
            f"Period: {start_date} to {end_date}, "
            f"Entity: {entity_id or 'All'}"
        )
        
        # Generate compliance report
        report = await blockchain_audit.generate_compliance_report(
            framework=framework,
            start_date=start_date,
            end_date=end_date,
            entity_id=entity_id
        )
        
        # Log report generation completion
        compliance_logger.info(
            f"Compliance report generated - Framework: {framework}, "
            f"Records: {report['total_records']}, "
            f"Compliance Score: {report['compliance_score']}"
        )
        
        return {
            "framework": framework,
            "start_date": start_date,
            "end_date": end_date,
            "entity_id": entity_id,
            "compliance_report": report,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Compliance report generation failed for framework {framework}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Compliance report generation failed: {str(e)}"
        )


@router.get("/smart-contracts")
async def list_smart_contracts(
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    List deployed smart contracts.
    
    Retrieves information about deployed smart contracts
    including their status and compliance rules.
    """
    try:
        # List smart contracts
        contracts = await blockchain_audit.list_smart_contracts(status=status)
        
        return {
            "smart_contracts": contracts,
            "total_contracts": len(contracts),
            "status_filter": status,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Smart contracts listing failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Smart contracts listing failed: {str(e)}"
        )


@router.get("/network-status")
async def get_network_status(db: AsyncSession = Depends(get_db)):
    """
    Get Hyperledger Fabric network status.
    
    Provides information about the blockchain network health,
    peer connectivity, and consensus status.
    """
    try:
        # Get network status
        network_status = await fabric_connector.get_network_status()
        
        return {
            "network_status": network_status,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Network status retrieval failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Network status retrieval failed: {str(e)}"
        )


@router.get("/health")
async def blockchain_audit_health_check():
    """Blockchain audit module health check."""
    try:
        # Check blockchain audit engine status
        engine_status = await blockchain_audit.health_check()
        
        # Check Hyperledger Fabric connectivity
        fabric_status = await fabric_connector.health_check()
        
        return {
            "status": "healthy",
            "module": "Blockchain Audit Trail",
            "engine_status": engine_status,
            "fabric_status": fabric_status,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Blockchain audit health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Blockchain audit module unhealthy: {str(e)}"
        )
