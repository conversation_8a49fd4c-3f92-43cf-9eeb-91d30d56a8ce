#!/usr/bin/env python3
"""
AthenaTrader Phase 10 Performance Benchmarking Script

This script performs comprehensive latency and throughput benchmarking
to validate HFT performance targets:

- Order processing latency: <50μs P95
- Risk check latency: <5μs P95
- SOR routing latency: <25μs P95
- Throughput: 100,000+ orders/second
- System uptime: 99.99%

Usage:
    python performance_benchmarks.py --test-type [latency|throughput|stress|all]
"""

import asyncio
import time
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
from decimal import Decimal
import numpy as np
import sys
import os

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.engine.ultra_low_latency import UltraLowLatencyEngine, DPDKConfiguration, FPGAConfiguration
from app.engine.advanced_risk_manager import AdvancedRiskManager
from app.schemas.execution import OrderRequest, OrderSide, OrderType, AssetClass

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class LatencyMetrics:
    """Latency measurement results."""
    operation: str
    sample_count: int
    min_latency_ns: int
    max_latency_ns: int
    mean_latency_ns: float
    median_latency_ns: float
    p95_latency_ns: float
    p99_latency_ns: float
    p999_latency_ns: float
    std_dev_ns: float
    target_met: bool
    target_threshold_ns: int


@dataclass
class ThroughputMetrics:
    """Throughput measurement results."""
    operation: str
    duration_seconds: float
    total_operations: int
    operations_per_second: float
    peak_ops_per_second: float
    target_met: bool
    target_threshold: int


@dataclass
class BenchmarkResults:
    """Complete benchmark results."""
    timestamp: str
    test_duration_seconds: float
    system_info: Dict[str, Any]
    latency_results: List[LatencyMetrics]
    throughput_results: List[ThroughputMetrics]
    error_count: int
    success_rate: float
    overall_grade: str


class PerformanceBenchmark:
    """Performance benchmarking for HFT systems."""

    # Performance targets (in nanoseconds)
    LATENCY_TARGETS = {
        'order_processing': 50_000,    # 50μs
        'risk_check': 5_000,           # 5μs
        'sor_routing': 25_000,         # 25μs
        'market_data': 10_000,         # 10μs
        'total_roundtrip': 100_000     # 100μs
    }

    # Throughput targets (operations per second)
    THROUGHPUT_TARGETS = {
        'order_processing': 100_000,
        'risk_checks': 500_000,
        'market_data_updates': 1_000_000
    }

    def __init__(self):
        """Initialize performance benchmark."""
        self.ull_engine = None
        self.risk_manager = None
        self.benchmark_results = None
        self.error_count = 0

        logger.info("Performance benchmark initialized")

    async def setup_test_environment(self) -> bool:
        """Setup test environment with HFT components."""
        logger.info("Setting up test environment...")

        try:
            # Initialize DPDK and FPGA configurations (disabled for testing)
            dpdk_config = DPDKConfiguration(enabled=False)
            fpga_config = FPGAConfiguration(enabled=False)

            # Initialize components
            self.ull_engine = UltraLowLatencyEngine(dpdk_config, fpga_config)
            self.risk_manager = AdvancedRiskManager(fpga_config)

            # Initialize engines
            await self.ull_engine.initialize()
            await self.risk_manager.initialize()

            logger.info("Test environment setup completed")
            return True

        except Exception as e:
            logger.error(f"Test environment setup failed: {e}")
            return False

    async def cleanup_test_environment(self):
        """Cleanup test environment."""
        logger.info("Cleaning up test environment...")

        try:
            if self.ull_engine:
                await self.ull_engine.cleanup()
            if self.risk_manager:
                await self.risk_manager.cleanup()

            logger.info("Test environment cleanup completed")

        except Exception as e:
            logger.error(f"Test environment cleanup failed: {e}")

    async def run_latency_benchmarks(self, sample_count: int = 10000) -> List[LatencyMetrics]:
        """Run comprehensive latency benchmarks."""
        logger.info(f"Running latency benchmarks with {sample_count} samples...")

        latency_results = []

        # Test order processing latency
        order_latencies = await self._measure_order_processing_latency(sample_count)
        latency_results.append(self._calculate_latency_metrics(
            'order_processing', order_latencies, self.LATENCY_TARGETS['order_processing']
        ))

        # Test risk check latency
        risk_latencies = await self._measure_risk_check_latency(sample_count)
        latency_results.append(self._calculate_latency_metrics(
            'risk_check', risk_latencies, self.LATENCY_TARGETS['risk_check']
        ))

        # Test market data processing latency
        market_data_latencies = await self._measure_market_data_latency(sample_count)
        latency_results.append(self._calculate_latency_metrics(
            'market_data', market_data_latencies, self.LATENCY_TARGETS['market_data']
        ))

        # Test end-to-end roundtrip latency
        roundtrip_latencies = await self._measure_roundtrip_latency(sample_count // 10)  # Fewer samples for complex test
        latency_results.append(self._calculate_latency_metrics(
            'total_roundtrip', roundtrip_latencies, self.LATENCY_TARGETS['total_roundtrip']
        ))

        return latency_results

    async def _measure_order_processing_latency(self, sample_count: int) -> List[int]:
        """Measure order processing latency."""
        logger.info("Measuring order processing latency...")

        latencies = []

        for i in range(sample_count):
            order_data = {
                'order_id': f'bench_{i:06d}',
                'symbol': 'AAPL',
                'quantity': 100,
                'price': 150.0 + (i * 0.01),
                'side': 'BUY',
                'order_type': 'LIMIT'
            }

            start_time = time.time_ns()
            try:
                result = await self.ull_engine.process_order(order_data)
                end_time = time.time_ns()

                if result['status'] == 'PROCESSED':
                    latencies.append(end_time - start_time)
                else:
                    self.error_count += 1

            except Exception as e:
                self.error_count += 1
                logger.debug(f"Order processing error: {e}")

            # Small delay to prevent overwhelming the system
            if i % 1000 == 0:
                await asyncio.sleep(0.001)  # 1ms pause every 1000 operations

        return latencies

    async def _measure_risk_check_latency(self, sample_count: int) -> List[int]:
        """Measure risk check latency."""
        logger.info("Measuring risk check latency...")

        latencies = []

        for i in range(sample_count):
            order_request = OrderRequest(
                symbol="AAPL",
                quantity=Decimal("100"),
                price=Decimal(f"{150.0 + (i * 0.01):.2f}"),
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            start_time = time.time_ns()
            try:
                result = await self.risk_manager.validate_order_ultra_fast(order_request)
                end_time = time.time_ns()

                if 'approved' in result:
                    latencies.append(end_time - start_time)
                else:
                    self.error_count += 1

            except Exception as e:
                self.error_count += 1
                logger.debug(f"Risk check error: {e}")

            # Small delay to prevent overwhelming the system
            if i % 1000 == 0:
                await asyncio.sleep(0.001)

        return latencies

    async def _measure_market_data_latency(self, sample_count: int) -> List[int]:
        """Measure market data processing latency."""
        logger.info("Measuring market data processing latency...")

        latencies = []

        for i in range(sample_count):
            market_data = {
                'bid_price': 149.95 + (i * 0.001),
                'bid_quantity': 1000,
                'ask_price': 150.05 + (i * 0.001),
                'ask_quantity': 1000,
                'timestamp': time.time_ns()
            }

            start_time = time.time_ns()
            try:
                await self.ull_engine.update_market_data('AAPL', market_data)
                end_time = time.time_ns()

                latencies.append(end_time - start_time)

            except Exception as e:
                self.error_count += 1
                logger.debug(f"Market data processing error: {e}")

            # Small delay to prevent overwhelming the system
            if i % 1000 == 0:
                await asyncio.sleep(0.001)

        return latencies

    async def _measure_roundtrip_latency(self, sample_count: int) -> List[int]:
        """Measure end-to-end roundtrip latency."""
        logger.info("Measuring end-to-end roundtrip latency...")

        latencies = []

        for i in range(sample_count):
            # Create order request
            order_request = OrderRequest(
                symbol="AAPL",
                quantity=Decimal("100"),
                price=Decimal(f"{150.0 + (i * 0.01):.2f}"),
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            start_time = time.time_ns()
            try:
                # Risk validation
                risk_result = await self.risk_manager.validate_order_ultra_fast(order_request)

                if risk_result.get('approved', False):
                    # Order processing
                    order_data = {
                        'order_id': f'roundtrip_{i:06d}',
                        'symbol': order_request.symbol,
                        'quantity': float(order_request.quantity),
                        'price': float(order_request.price),
                        'side': order_request.side.value,
                        'order_type': order_request.order_type.value
                    }

                    result = await self.ull_engine.process_order(order_data)
                    end_time = time.time_ns()

                    if result['status'] == 'PROCESSED':
                        latencies.append(end_time - start_time)
                    else:
                        self.error_count += 1
                else:
                    self.error_count += 1

            except Exception as e:
                self.error_count += 1
                logger.debug(f"Roundtrip processing error: {e}")

            # Longer delay for complex roundtrip tests
            if i % 100 == 0:
                await asyncio.sleep(0.01)  # 10ms pause every 100 operations

        return latencies

    def _calculate_latency_metrics(self, operation: str, latencies: List[int], target_ns: int) -> LatencyMetrics:
        """Calculate latency statistics."""
        if not latencies:
            logger.warning(f"No latency data for {operation}")
            return LatencyMetrics(
                operation=operation,
                sample_count=0,
                min_latency_ns=0,
                max_latency_ns=0,
                mean_latency_ns=0,
                median_latency_ns=0,
                p95_latency_ns=0,
                p99_latency_ns=0,
                p999_latency_ns=0,
                std_dev_ns=0,
                target_met=False,
                target_threshold_ns=target_ns
            )

        # Convert to numpy array for efficient calculations
        latencies_array = np.array(latencies)

        # Calculate percentiles
        p95 = np.percentile(latencies_array, 95)
        p99 = np.percentile(latencies_array, 99)
        p999 = np.percentile(latencies_array, 99.9)

        # Check if target is met (P95 should be under target)
        target_met = p95 <= target_ns

        metrics = LatencyMetrics(
            operation=operation,
            sample_count=len(latencies),
            min_latency_ns=int(np.min(latencies_array)),
            max_latency_ns=int(np.max(latencies_array)),
            mean_latency_ns=float(np.mean(latencies_array)),
            median_latency_ns=float(np.median(latencies_array)),
            p95_latency_ns=float(p95),
            p99_latency_ns=float(p99),
            p999_latency_ns=float(p999),
            std_dev_ns=float(np.std(latencies_array)),
            target_met=target_met,
            target_threshold_ns=target_ns
        )

        # Log results
        logger.info(f"{operation} latency results:")
        logger.info(f"  P95: {p95/1000:.1f}μs (target: {target_ns/1000:.1f}μs) - {'✓' if target_met else '✗'}")
        logger.info(f"  P99: {p99/1000:.1f}μs")
        logger.info(f"  Mean: {metrics.mean_latency_ns/1000:.1f}μs")

        return metrics

    async def run_throughput_benchmarks(self, duration_seconds: int = 60) -> List[ThroughputMetrics]:
        """Run comprehensive throughput benchmarks."""
        logger.info(f"Running throughput benchmarks for {duration_seconds} seconds...")

        throughput_results = []

        # Test order processing throughput
        order_throughput = await self._measure_order_processing_throughput(duration_seconds)
        throughput_results.append(order_throughput)

        # Test risk check throughput
        risk_throughput = await self._measure_risk_check_throughput(duration_seconds)
        throughput_results.append(risk_throughput)

        # Test market data throughput
        market_data_throughput = await self._measure_market_data_throughput(duration_seconds)
        throughput_results.append(market_data_throughput)

        return throughput_results

    async def _measure_order_processing_throughput(self, duration_seconds: int) -> ThroughputMetrics:
        """Measure order processing throughput."""
        logger.info("Measuring order processing throughput...")

        start_time = time.time()
        end_time = start_time + duration_seconds
        operation_count = 0
        peak_ops_per_second = 0

        # Track operations per second in 1-second windows
        window_start = start_time
        window_operations = 0

        while time.time() < end_time:
            order_data = {
                'order_id': f'throughput_{operation_count:08d}',
                'symbol': 'AAPL',
                'quantity': 100,
                'price': 150.0 + (operation_count * 0.001),
                'side': 'BUY' if operation_count % 2 == 0 else 'SELL',
                'order_type': 'LIMIT'
            }

            try:
                result = await self.ull_engine.process_order(order_data)
                if result['status'] == 'PROCESSED':
                    operation_count += 1
                    window_operations += 1
                else:
                    self.error_count += 1

            except Exception as e:
                self.error_count += 1
                logger.debug(f"Throughput test error: {e}")

            # Check if 1-second window completed
            current_time = time.time()
            if current_time - window_start >= 1.0:
                window_ops_per_second = window_operations / (current_time - window_start)
                peak_ops_per_second = max(peak_ops_per_second, window_ops_per_second)

                # Reset window
                window_start = current_time
                window_operations = 0

        actual_duration = time.time() - start_time
        ops_per_second = operation_count / actual_duration
        target_met = ops_per_second >= self.THROUGHPUT_TARGETS['order_processing']

        logger.info(f"Order processing throughput: {ops_per_second:.0f} ops/sec (target: {self.THROUGHPUT_TARGETS['order_processing']} ops/sec) - {'✓' if target_met else '✗'}")

        return ThroughputMetrics(
            operation='order_processing',
            duration_seconds=actual_duration,
            total_operations=operation_count,
            operations_per_second=ops_per_second,
            peak_ops_per_second=peak_ops_per_second,
            target_met=target_met,
            target_threshold=self.THROUGHPUT_TARGETS['order_processing']
        )

    async def _measure_risk_check_throughput(self, duration_seconds: int) -> ThroughputMetrics:
        """Measure risk check throughput."""
        logger.info("Measuring risk check throughput...")

        start_time = time.time()
        end_time = start_time + duration_seconds
        operation_count = 0
        peak_ops_per_second = 0

        # Track operations per second in 1-second windows
        window_start = start_time
        window_operations = 0

        while time.time() < end_time:
            order_request = OrderRequest(
                symbol="AAPL",
                quantity=Decimal("100"),
                price=Decimal(f"{150.0 + (operation_count * 0.001):.3f}"),
                side=OrderSide.BUY if operation_count % 2 == 0 else OrderSide.SELL,
                order_type=OrderType.LIMIT,
                asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            try:
                result = await self.risk_manager.validate_order_ultra_fast(order_request)
                if 'approved' in result:
                    operation_count += 1
                    window_operations += 1
                else:
                    self.error_count += 1

            except Exception as e:
                self.error_count += 1
                logger.debug(f"Risk throughput test error: {e}")

            # Check if 1-second window completed
            current_time = time.time()
            if current_time - window_start >= 1.0:
                window_ops_per_second = window_operations / (current_time - window_start)
                peak_ops_per_second = max(peak_ops_per_second, window_ops_per_second)

                # Reset window
                window_start = current_time
                window_operations = 0

        actual_duration = time.time() - start_time
        ops_per_second = operation_count / actual_duration
        target_met = ops_per_second >= self.THROUGHPUT_TARGETS['risk_checks']

        logger.info(f"Risk check throughput: {ops_per_second:.0f} ops/sec (target: {self.THROUGHPUT_TARGETS['risk_checks']} ops/sec) - {'✓' if target_met else '✗'}")

        return ThroughputMetrics(
            operation='risk_checks',
            duration_seconds=actual_duration,
            total_operations=operation_count,
            operations_per_second=ops_per_second,
            peak_ops_per_second=peak_ops_per_second,
            target_met=target_met,
            target_threshold=self.THROUGHPUT_TARGETS['risk_checks']
        )

    async def _measure_market_data_throughput(self, duration_seconds: int) -> ThroughputMetrics:
        """Measure market data processing throughput."""
        logger.info("Measuring market data processing throughput...")

        start_time = time.time()
        end_time = start_time + duration_seconds
        operation_count = 0
        peak_ops_per_second = 0

        # Track operations per second in 1-second windows
        window_start = start_time
        window_operations = 0

        while time.time() < end_time:
            market_data = {
                'bid_price': 149.95 + (operation_count * 0.0001),
                'bid_quantity': 1000,
                'ask_price': 150.05 + (operation_count * 0.0001),
                'ask_quantity': 1000,
                'timestamp': time.time_ns()
            }

            try:
                await self.ull_engine.update_market_data('AAPL', market_data)
                operation_count += 1
                window_operations += 1

            except Exception as e:
                self.error_count += 1
                logger.debug(f"Market data throughput test error: {e}")

            # Check if 1-second window completed
            current_time = time.time()
            if current_time - window_start >= 1.0:
                window_ops_per_second = window_operations / (current_time - window_start)
                peak_ops_per_second = max(peak_ops_per_second, window_ops_per_second)

                # Reset window
                window_start = current_time
                window_operations = 0

        actual_duration = time.time() - start_time
        ops_per_second = operation_count / actual_duration
        target_met = ops_per_second >= self.THROUGHPUT_TARGETS['market_data_updates']

        logger.info(f"Market data throughput: {ops_per_second:.0f} ops/sec (target: {self.THROUGHPUT_TARGETS['market_data_updates']} ops/sec) - {'✓' if target_met else '✗'}")

        return ThroughputMetrics(
            operation='market_data_updates',
            duration_seconds=actual_duration,
            total_operations=operation_count,
            operations_per_second=ops_per_second,
            peak_ops_per_second=peak_ops_per_second,
            target_met=target_met,
            target_threshold=self.THROUGHPUT_TARGETS['market_data_updates']
        )

    async def run_comprehensive_benchmark(self,
                                        latency_samples: int = 10000,
                                        throughput_duration: int = 60) -> BenchmarkResults:
        """Run comprehensive performance benchmark."""
        logger.info("Starting comprehensive performance benchmark...")

        start_time = time.time()
        self.error_count = 0

        # Get system information
        system_info = self._get_system_info()

        # Run latency benchmarks
        logger.info("Phase 1: Latency benchmarks")
        latency_results = await self.run_latency_benchmarks(latency_samples)

        # Run throughput benchmarks
        logger.info("Phase 2: Throughput benchmarks")
        throughput_results = await self.run_throughput_benchmarks(throughput_duration)

        # Calculate overall metrics
        total_operations = sum(lr.sample_count for lr in latency_results) + \
                          sum(tr.total_operations for tr in throughput_results)
        success_rate = ((total_operations - self.error_count) / max(total_operations, 1)) * 100

        # Calculate overall grade
        overall_grade = self._calculate_overall_grade(latency_results, throughput_results, success_rate)

        # Create benchmark results
        self.benchmark_results = BenchmarkResults(
            timestamp=datetime.now().isoformat(),
            test_duration_seconds=time.time() - start_time,
            system_info=system_info,
            latency_results=latency_results,
            throughput_results=throughput_results,
            error_count=self.error_count,
            success_rate=success_rate,
            overall_grade=overall_grade
        )

        logger.info(f"Comprehensive benchmark completed in {self.benchmark_results.test_duration_seconds:.1f} seconds")
        logger.info(f"Overall grade: {overall_grade}")
        logger.info(f"Success rate: {success_rate:.2f}%")

        return self.benchmark_results

    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for benchmark context."""
        import platform
        import psutil

        return {
            'hostname': platform.node(),
            'platform': platform.platform(),
            'processor': platform.processor(),
            'cpu_count': psutil.cpu_count(),
            'memory_gb': psutil.virtual_memory().total / (1024**3),
            'python_version': platform.python_version(),
            'timestamp': datetime.now().isoformat()
        }

    def _calculate_overall_grade(self, latency_results: List[LatencyMetrics],
                               throughput_results: List[ThroughputMetrics],
                               success_rate: float) -> str:
        """Calculate overall performance grade."""

        # Grade latency performance
        latency_targets_met = sum(1 for lr in latency_results if lr.target_met)
        latency_score = (latency_targets_met / max(len(latency_results), 1)) * 100

        # Grade throughput performance
        throughput_targets_met = sum(1 for tr in throughput_results if tr.target_met)
        throughput_score = (throughput_targets_met / max(len(throughput_results), 1)) * 100

        # Overall score (weighted average)
        overall_score = (latency_score * 0.4 + throughput_score * 0.4 + success_rate * 0.2)

        # Assign grade
        if overall_score >= 95:
            return "A+ (Excellent HFT Performance)"
        elif overall_score >= 90:
            return "A (Good HFT Performance)"
        elif overall_score >= 80:
            return "B (Acceptable HFT Performance)"
        elif overall_score >= 70:
            return "C (Poor HFT Performance)"
        else:
            return "F (Unacceptable HFT Performance)"

    def save_results(self, output_file: str = "performance_benchmark_results.json"):
        """Save benchmark results to file."""
        if not self.benchmark_results:
            raise ValueError("No benchmark results available")

        results_dict = asdict(self.benchmark_results)

        with open(output_file, 'w') as f:
            json.dump(results_dict, f, indent=2, default=str)

        logger.info(f"Benchmark results saved to {output_file}")

    def generate_performance_report(self) -> str:
        """Generate comprehensive performance report."""
        if not self.benchmark_results:
            raise ValueError("No benchmark results available")

        report = []
        report.append("=" * 80)
        report.append("ATHENATRADER PHASE 10 HFT PERFORMANCE BENCHMARK REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {self.benchmark_results.timestamp}")
        report.append(f"Test Duration: {self.benchmark_results.test_duration_seconds:.1f} seconds")
        report.append(f"Overall Grade: {self.benchmark_results.overall_grade}")
        report.append(f"Success Rate: {self.benchmark_results.success_rate:.2f}%")
        report.append("")

        # System Information
        report.append("SYSTEM INFORMATION:")
        for key, value in self.benchmark_results.system_info.items():
            report.append(f"  {key}: {value}")
        report.append("")

        # Latency Results
        report.append("LATENCY BENCHMARK RESULTS:")
        for lr in self.benchmark_results.latency_results:
            status = "✓ PASS" if lr.target_met else "✗ FAIL"
            report.append(f"  {lr.operation}:")
            report.append(f"    Target: {lr.target_threshold_ns/1000:.1f}μs - {status}")
            report.append(f"    P95: {lr.p95_latency_ns/1000:.1f}μs")
            report.append(f"    P99: {lr.p99_latency_ns/1000:.1f}μs")
            report.append(f"    Mean: {lr.mean_latency_ns/1000:.1f}μs")
            report.append(f"    Samples: {lr.sample_count:,}")
            report.append("")

        # Throughput Results
        report.append("THROUGHPUT BENCHMARK RESULTS:")
        for tr in self.benchmark_results.throughput_results:
            status = "✓ PASS" if tr.target_met else "✗ FAIL"
            report.append(f"  {tr.operation}:")
            report.append(f"    Target: {tr.target_threshold:,} ops/sec - {status}")
            report.append(f"    Achieved: {tr.operations_per_second:,.0f} ops/sec")
            report.append(f"    Peak: {tr.peak_ops_per_second:,.0f} ops/sec")
            report.append(f"    Duration: {tr.duration_seconds:.1f}s")
            report.append(f"    Total Ops: {tr.total_operations:,}")
            report.append("")

        # Compliance Summary
        report.append("COMPLIANCE SUMMARY:")
        latency_compliance = [lr.target_met for lr in self.benchmark_results.latency_results]
        throughput_compliance = [tr.target_met for tr in self.benchmark_results.throughput_results]

        report.append(f"  Latency Targets Met: {sum(latency_compliance)}/{len(latency_compliance)}")
        report.append(f"  Throughput Targets Met: {sum(throughput_compliance)}/{len(throughput_compliance)}")
        report.append(f"  Error Count: {self.benchmark_results.error_count}")

        overall_compliant = all(latency_compliance + throughput_compliance) and self.benchmark_results.success_rate >= 99.0
        compliance_status = "✓ COMPLIANT" if overall_compliant else "✗ NON-COMPLIANT"
        report.append(f"  Overall Compliance: {compliance_status}")

        return "\n".join(report)


async def main():
    """Main benchmark execution function."""
    import argparse

    parser = argparse.ArgumentParser(description="Run AthenaTrader Phase 10 HFT performance benchmarks")
    parser.add_argument("--test-type", choices=["latency", "throughput", "stress", "all"], default="all",
                       help="Type of benchmark to run")
    parser.add_argument("--latency-samples", type=int, default=10000,
                       help="Number of samples for latency tests")
    parser.add_argument("--throughput-duration", type=int, default=60,
                       help="Duration in seconds for throughput tests")
    parser.add_argument("--output", default="performance_benchmark_results.json",
                       help="Output file for results")
    parser.add_argument("--report", action="store_true",
                       help="Generate performance report")
    parser.add_argument("--validate-targets", action="store_true",
                       help="Validate against HFT performance targets")

    args = parser.parse_args()

    # Create benchmark instance
    benchmark = PerformanceBenchmark()

    try:
        # Setup test environment
        if not await benchmark.setup_test_environment():
            logger.error("Failed to setup test environment")
            sys.exit(1)

        # Run benchmarks based on test type
        if args.test_type == "latency":
            logger.info("Running latency benchmarks only")
            latency_results = await benchmark.run_latency_benchmarks(args.latency_samples)

            # Create minimal results for latency-only test
            benchmark.benchmark_results = BenchmarkResults(
                timestamp=datetime.now().isoformat(),
                test_duration_seconds=0,
                system_info=benchmark._get_system_info(),
                latency_results=latency_results,
                throughput_results=[],
                error_count=benchmark.error_count,
                success_rate=100.0,
                overall_grade="Latency Test Only"
            )

        elif args.test_type == "throughput":
            logger.info("Running throughput benchmarks only")
            throughput_results = await benchmark.run_throughput_benchmarks(args.throughput_duration)

            # Create minimal results for throughput-only test
            benchmark.benchmark_results = BenchmarkResults(
                timestamp=datetime.now().isoformat(),
                test_duration_seconds=0,
                system_info=benchmark._get_system_info(),
                latency_results=[],
                throughput_results=throughput_results,
                error_count=benchmark.error_count,
                success_rate=100.0,
                overall_grade="Throughput Test Only"
            )

        elif args.test_type == "stress":
            logger.info("Running stress test (extended duration)")
            # Stress test with higher load
            await benchmark.run_comprehensive_benchmark(
                latency_samples=args.latency_samples * 2,
                throughput_duration=args.throughput_duration * 2
            )

        else:  # args.test_type == "all"
            logger.info("Running comprehensive benchmarks")
            await benchmark.run_comprehensive_benchmark(
                latency_samples=args.latency_samples,
                throughput_duration=args.throughput_duration
            )

        # Save results
        benchmark.save_results(args.output)

        # Generate and display report
        if args.report:
            report = benchmark.generate_performance_report()
            print(report)

            # Save report to file
            report_file = args.output.replace('.json', '_report.txt')
            with open(report_file, 'w') as f:
                f.write(report)
            logger.info(f"Performance report saved to {report_file}")

        # Validate against targets
        if args.validate_targets:
            validation_passed = validate_performance_targets(benchmark.benchmark_results)
            if validation_passed:
                logger.info("✓ All performance targets met - HFT deployment ready")
                sys.exit(0)
            else:
                logger.error("✗ Performance targets not met - HFT deployment not ready")
                sys.exit(1)

        logger.info("Performance benchmarks completed successfully")

    except Exception as e:
        logger.error(f"Benchmark execution failed: {e}")
        sys.exit(1)

    finally:
        # Cleanup test environment
        await benchmark.cleanup_test_environment()


def validate_performance_targets(results: BenchmarkResults) -> bool:
    """Validate benchmark results against HFT performance targets."""
    if not results:
        return False

    validation_passed = True

    logger.info("Validating performance targets...")

    # Validate latency targets
    for lr in results.latency_results:
        if not lr.target_met:
            logger.error(f"Latency target FAILED: {lr.operation} - P95: {lr.p95_latency_ns/1000:.1f}μs (target: {lr.target_threshold_ns/1000:.1f}μs)")
            validation_passed = False
        else:
            logger.info(f"Latency target PASSED: {lr.operation} - P95: {lr.p95_latency_ns/1000:.1f}μs")

    # Validate throughput targets
    for tr in results.throughput_results:
        if not tr.target_met:
            logger.error(f"Throughput target FAILED: {tr.operation} - {tr.operations_per_second:,.0f} ops/sec (target: {tr.target_threshold:,} ops/sec)")
            validation_passed = False
        else:
            logger.info(f"Throughput target PASSED: {tr.operation} - {tr.operations_per_second:,.0f} ops/sec")

    # Validate success rate
    if results.success_rate < 99.0:
        logger.error(f"Success rate FAILED: {results.success_rate:.2f}% (target: 99.0%)")
        validation_passed = False
    else:
        logger.info(f"Success rate PASSED: {results.success_rate:.2f}%")

    return validation_passed


if __name__ == "__main__":
    asyncio.run(main())
