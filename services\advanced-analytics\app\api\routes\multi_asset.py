"""
AthenaTrader Phase 11 Advanced Analytics Engine - Multi-Asset Trading API Routes

PRIORITY 3: Multi-Asset Trading Support
- Cryptocurrency trading capabilities
- Fixed income trading support
- Unified asset class data normalization
- Cross-asset risk management
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field
from ...multi_asset.asset_manager import MultiAssetManager, AssetClass, Position
from ...multi_asset.risk_manager import CrossAssetRiskManager
from ...core.logging_config import performance_logger
from ...core.database import DatabaseManager

logger = logging.getLogger("multi_asset")
router = APIRouter()


# Pydantic models for request/response
class AssetUniverseResponse(BaseModel):
    total_assets: int
    asset_classes: Dict[str, int]
    assets: Dict[str, Dict[str, Any]]


class MarketDataRequest(BaseModel):
    symbols: List[str] = Field(..., description="List of asset symbols")
    normalize: bool = Field(default=True, description="Apply data normalization")


class MarketDataResponse(BaseModel):
    timestamp: str
    data: Dict[str, Dict[str, Any]]
    failed_symbols: List[str]


class PortfolioRiskRequest(BaseModel):
    portfolio_id: str = Field(..., description="Portfolio identifier")
    positions: List[Dict[str, Any]] = Field(..., description="List of positions")


class PortfolioRiskResponse(BaseModel):
    portfolio_id: str
    total_value: float
    risk_metrics: Dict[str, float]
    risk_factors: Dict[str, float]
    risk_level: str
    timestamp: str


class PositionLimitCheckRequest(BaseModel):
    portfolio_id: str = Field(..., description="Portfolio identifier")
    symbol: str = Field(..., description="Asset symbol")
    quantity: float = Field(..., description="Position quantity")
    price: float = Field(..., description="Current price")


class CorrelationAnalysisRequest(BaseModel):
    symbols: List[str] = Field(..., description="List of symbols for correlation analysis")
    lookback_days: int = Field(default=30, description="Lookback period in days")


class CorrelationAnalysisResponse(BaseModel):
    symbols: List[str]
    correlation_matrix: List[List[float]]
    asset_class_correlations: Dict[str, Dict[str, float]]
    timestamp: str


class CrossAssetAllocationRequest(BaseModel):
    portfolio_value: float = Field(..., description="Total portfolio value")
    risk_tolerance: str = Field(..., description="Risk tolerance: conservative, moderate, aggressive")
    asset_classes: List[str] = Field(..., description="Allowed asset classes")
    constraints: Dict[str, Any] = Field(default_factory=dict, description="Allocation constraints")


# Global instances (would be dependency injected in production)
asset_manager = None
risk_manager = None


async def get_asset_manager() -> MultiAssetManager:
    """Get asset manager instance."""
    global asset_manager
    if asset_manager is None:
        asset_manager = MultiAssetManager()
        await asset_manager.initialize()
    return asset_manager


async def get_risk_manager() -> CrossAssetRiskManager:
    """Get risk manager instance."""
    global risk_manager
    if risk_manager is None:
        risk_manager = CrossAssetRiskManager()
        await risk_manager.initialize()
    return risk_manager


@router.get("/status")
async def get_multi_asset_status():
    """Get status of multi-asset trading system."""
    try:
        manager = await get_asset_manager()
        risk_mgr = await get_risk_manager()
        
        asset_universe = await manager.get_asset_universe()
        
        # Count assets by class
        asset_class_counts = {}
        for asset_info in asset_universe.values():
            asset_class = asset_info['asset_class']
            asset_class_counts[asset_class] = asset_class_counts.get(asset_class, 0) + 1
        
        return {
            "status": "operational",
            "capabilities": [
                "Cryptocurrency trading",
                "Fixed income trading",
                "Cross-asset risk management",
                "Unified data normalization",
                "Correlation analysis"
            ],
            "asset_universe": {
                "total_assets": len(asset_universe),
                "asset_classes": asset_class_counts
            },
            "supported_exchanges": [
                "Binance (Crypto)",
                "Coinbase Pro (Crypto)",
                "OTC (Fixed Income)",
                "Treasury Direct (Bonds)"
            ],
            "risk_management": {
                "var_calculation": "enabled",
                "correlation_analysis": "enabled",
                "position_limits": "enabled"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting multi-asset status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/universe", response_model=AssetUniverseResponse)
async def get_asset_universe():
    """
    Get complete asset universe with all supported instruments.
    
    Returns information about all tradeable assets across:
    - Cryptocurrencies (Bitcoin, Ethereum, top altcoins)
    - Fixed income (US Treasuries, corporate bonds)
    - Equities (when integrated)
    - Derivatives (when integrated)
    """
    try:
        start_time = datetime.now()
        
        manager = await get_asset_manager()
        asset_universe = await manager.get_asset_universe()
        
        # Count assets by class
        asset_class_counts = {}
        for asset_info in asset_universe.values():
            asset_class = asset_info['asset_class']
            asset_class_counts[asset_class] = asset_class_counts.get(asset_class, 0) + 1
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/multi-asset/universe",
            method="GET",
            response_time_ms=api_time,
            status_code=200
        )
        
        return AssetUniverseResponse(
            total_assets=len(asset_universe),
            asset_classes=asset_class_counts,
            assets=asset_universe
        )
        
    except Exception as e:
        logger.error(f"Error getting asset universe: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/market-data", response_model=MarketDataResponse)
async def get_multi_asset_market_data(request: MarketDataRequest):
    """
    Get real-time market data for multiple assets across different classes.
    
    Supports:
    - Cryptocurrency spot prices from major exchanges
    - Fixed income bond prices and yields
    - Unified data format with normalization
    - Real-time bid/ask spreads and volumes
    """
    try:
        start_time = datetime.now()
        
        if not request.symbols:
            raise HTTPException(status_code=400, detail="Symbols list cannot be empty")
        
        if len(request.symbols) > 100:
            raise HTTPException(status_code=400, detail="Maximum 100 symbols per request")
        
        manager = await get_asset_manager()
        
        # Fetch market data for all symbols
        market_data = {}
        failed_symbols = []
        
        for symbol in request.symbols:
            try:
                data = await manager.get_market_data(symbol)
                if data:
                    market_data[symbol] = data
                else:
                    failed_symbols.append(symbol)
            except Exception as e:
                logger.warning(f"Failed to get data for {symbol}: {e}")
                failed_symbols.append(symbol)
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/multi-asset/market-data",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return MarketDataResponse(
            timestamp=datetime.now().isoformat(),
            data=market_data,
            failed_symbols=failed_symbols
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting multi-asset market data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/risk/portfolio", response_model=PortfolioRiskResponse)
async def calculate_portfolio_risk(request: PortfolioRiskRequest):
    """
    Calculate comprehensive portfolio risk metrics across asset classes.
    
    Provides:
    - Value at Risk (VaR) at 95% and 99% confidence levels
    - Conditional Value at Risk (CVaR)
    - Correlation-based risk analysis
    - Concentration and liquidity risk metrics
    - Cross-asset diversification analysis
    """
    try:
        start_time = datetime.now()
        
        if not request.positions:
            raise HTTPException(status_code=400, detail="Positions list cannot be empty")
        
        risk_manager = await get_risk_manager()
        
        # Convert position data to Position objects
        positions = []
        for pos_data in request.positions:
            try:
                position = Position(
                    portfolio_id=request.portfolio_id,
                    symbol=pos_data['symbol'],
                    asset_class=AssetClass(pos_data['asset_class']),
                    quantity=pos_data['quantity'],
                    average_price=pos_data['average_price'],
                    current_price=pos_data['current_price'],
                    market_value=pos_data['quantity'] * pos_data['current_price'],
                    unrealized_pnl=pos_data['quantity'] * (pos_data['current_price'] - pos_data['average_price']),
                    realized_pnl=pos_data.get('realized_pnl', 0.0),
                    last_updated=datetime.now()
                )
                positions.append(position)
            except Exception as e:
                logger.warning(f"Invalid position data: {e}")
                continue
        
        if not positions:
            raise HTTPException(status_code=400, detail="No valid positions provided")
        
        # Calculate risk metrics
        risk_metrics = await risk_manager.calculate_portfolio_risk(request.portfolio_id, positions)
        
        # Get risk summary
        risk_summary = await risk_manager.get_risk_summary(request.portfolio_id)
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/multi-asset/risk/portfolio",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return PortfolioRiskResponse(
            portfolio_id=request.portfolio_id,
            total_value=risk_metrics.total_value,
            risk_metrics={
                "var_95": risk_metrics.var_95,
                "var_99": risk_metrics.var_99,
                "cvar_95": risk_metrics.cvar_95,
                "volatility": risk_metrics.volatility,
                "sharpe_ratio": risk_metrics.sharpe_ratio
            },
            risk_factors={
                "concentration_risk": risk_metrics.concentration_risk,
                "correlation_risk": risk_metrics.correlation_risk,
                "liquidity_risk": risk_metrics.liquidity_risk
            },
            risk_level=risk_summary.get("risk_level", "UNKNOWN"),
            timestamp=risk_metrics.timestamp.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating portfolio risk: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/risk/position-check")
async def check_position_limits(request: PositionLimitCheckRequest):
    """
    Check if a new position would violate risk management limits.
    
    Validates:
    - Maximum position size limits
    - Portfolio concentration limits
    - Asset class exposure limits
    - Leverage constraints
    """
    try:
        start_time = datetime.now()
        
        risk_manager = await get_risk_manager()
        
        # Check position limits
        limit_check = await risk_manager.check_position_limits(
            portfolio_id=request.portfolio_id,
            symbol=request.symbol,
            new_quantity=request.quantity,
            current_price=request.price
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/multi-asset/risk/position-check",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return {
            "portfolio_id": request.portfolio_id,
            "symbol": request.symbol,
            "position_value": request.quantity * request.price,
            "limit_check": limit_check,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error checking position limits: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analysis/correlation", response_model=CorrelationAnalysisResponse)
async def analyze_correlations(request: CorrelationAnalysisRequest):
    """
    Analyze correlations between assets and asset classes.
    
    Provides:
    - Pairwise correlation matrix for specified assets
    - Asset class correlation analysis
    - Time-varying correlation patterns
    - Diversification effectiveness metrics
    """
    try:
        start_time = datetime.now()
        
        if len(request.symbols) < 2:
            raise HTTPException(status_code=400, detail="At least 2 symbols required for correlation analysis")
        
        if len(request.symbols) > 50:
            raise HTTPException(status_code=400, detail="Maximum 50 symbols per correlation analysis")
        
        risk_manager = await get_risk_manager()
        
        # Calculate correlation matrix
        correlation_matrix = await risk_manager.correlation_analyzer.calculate_correlation_matrix(
            request.symbols, request.lookback_days
        )
        
        # Get asset class correlations
        asset_class_correlations = await risk_manager.correlation_analyzer.get_asset_class_correlations()
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/multi-asset/analysis/correlation",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return CorrelationAnalysisResponse(
            symbols=correlation_matrix.symbols,
            correlation_matrix=correlation_matrix.matrix.tolist(),
            asset_class_correlations=asset_class_correlations,
            timestamp=correlation_matrix.timestamp.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing correlations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/allocation/optimize")
async def optimize_cross_asset_allocation(request: CrossAssetAllocationRequest):
    """
    Optimize portfolio allocation across multiple asset classes.
    
    Uses modern portfolio theory to:
    - Maximize risk-adjusted returns
    - Minimize portfolio volatility
    - Respect allocation constraints
    - Consider correlation benefits
    """
    try:
        start_time = datetime.now()
        
        # Validate inputs
        if request.portfolio_value <= 0:
            raise HTTPException(status_code=400, detail="Portfolio value must be positive")
        
        if request.risk_tolerance not in ["conservative", "moderate", "aggressive"]:
            raise HTTPException(status_code=400, detail="Risk tolerance must be conservative, moderate, or aggressive")
        
        # Simulate portfolio optimization (simplified)
        # In production, this would use sophisticated optimization algorithms
        
        # Define target allocations based on risk tolerance
        allocation_templates = {
            "conservative": {
                "fixed_income": 0.60,
                "equity": 0.30,
                "cryptocurrency": 0.05,
                "cash": 0.05
            },
            "moderate": {
                "fixed_income": 0.40,
                "equity": 0.45,
                "cryptocurrency": 0.10,
                "cash": 0.05
            },
            "aggressive": {
                "fixed_income": 0.20,
                "equity": 0.55,
                "cryptocurrency": 0.20,
                "cash": 0.05
            }
        }
        
        base_allocation = allocation_templates[request.risk_tolerance]
        
        # Filter by allowed asset classes
        optimized_allocation = {}
        total_weight = 0
        
        for asset_class in request.asset_classes:
            if asset_class in base_allocation:
                optimized_allocation[asset_class] = base_allocation[asset_class]
                total_weight += base_allocation[asset_class]
        
        # Normalize weights
        if total_weight > 0:
            for asset_class in optimized_allocation:
                optimized_allocation[asset_class] /= total_weight
        
        # Apply constraints
        for constraint, value in request.constraints.items():
            if constraint.startswith("max_") and constraint.replace("max_", "") in optimized_allocation:
                asset_class = constraint.replace("max_", "")
                optimized_allocation[asset_class] = min(optimized_allocation[asset_class], value)
        
        # Calculate dollar allocations
        dollar_allocations = {}
        for asset_class, weight in optimized_allocation.items():
            dollar_allocations[asset_class] = request.portfolio_value * weight
        
        # Simulate expected returns and risk
        expected_returns = {
            "fixed_income": 0.03,
            "equity": 0.08,
            "cryptocurrency": 0.15,
            "cash": 0.01
        }
        
        expected_volatilities = {
            "fixed_income": 0.05,
            "equity": 0.16,
            "cryptocurrency": 0.60,
            "cash": 0.001
        }
        
        portfolio_return = sum(optimized_allocation.get(ac, 0) * expected_returns.get(ac, 0) for ac in expected_returns)
        portfolio_volatility = np.sqrt(sum((optimized_allocation.get(ac, 0) * expected_volatilities.get(ac, 0))**2 for ac in expected_volatilities))
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/multi-asset/allocation/optimize",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return {
            "portfolio_value": request.portfolio_value,
            "risk_tolerance": request.risk_tolerance,
            "optimized_allocation": {
                "weights": optimized_allocation,
                "dollar_amounts": dollar_allocations
            },
            "expected_performance": {
                "annual_return": portfolio_return,
                "annual_volatility": portfolio_volatility,
                "sharpe_ratio": portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
            },
            "rebalancing_frequency": "monthly",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error optimizing allocation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/crypto/supported")
async def get_supported_cryptocurrencies():
    """Get list of supported cryptocurrencies."""
    try:
        manager = await get_asset_manager()
        crypto_symbols = await manager.crypto_manager.get_supported_symbols()
        
        return {
            "supported_cryptocurrencies": crypto_symbols,
            "total_count": len(crypto_symbols),
            "exchanges": ["Binance", "Coinbase Pro"],
            "trading_pairs": ["USD", "USDT", "BTC", "ETH"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting supported cryptocurrencies: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/fixed-income/supported")
async def get_supported_fixed_income():
    """Get list of supported fixed income instruments."""
    try:
        manager = await get_asset_manager()
        fi_instruments = await manager.fixed_income_manager.get_supported_instruments()
        
        return {
            "supported_instruments": fi_instruments,
            "total_count": len(fi_instruments),
            "categories": [
                "US Treasuries",
                "Corporate Bonds",
                "Municipal Bonds",
                "TIPS (Treasury Inflation-Protected Securities)"
            ],
            "maturities": ["2Y", "5Y", "10Y", "30Y"],
            "credit_ratings": ["AAA", "AA", "A", "BBB"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting supported fixed income: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance/summary")
async def get_multi_asset_performance():
    """Get multi-asset system performance metrics."""
    try:
        from ...core.redis_client import performance_metrics
        
        # Get performance summary
        performance_summary = await performance_metrics.get_performance_summary(
            "multi_asset_performance", hours=24
        )
        
        return {
            "multi_asset_performance": performance_summary,
            "system_components": {
                "asset_manager": "operational",
                "risk_manager": "operational",
                "correlation_analyzer": "operational",
                "var_calculator": "operational"
            },
            "data_sources": {
                "cryptocurrency_exchanges": "connected",
                "fixed_income_providers": "connected",
                "market_data_feeds": "active"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting multi-asset performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))
