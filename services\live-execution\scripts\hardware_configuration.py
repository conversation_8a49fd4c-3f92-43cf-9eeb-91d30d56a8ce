#!/usr/bin/env python3
"""
AthenaTrader Phase 10 Hardware Configuration Script

This script configures DPDK-compatible NICs and FPGA devices for production
HFT deployment, including driver binding, device setup, and validation.

Configuration Steps:
1. Bind NICs to DPDK drivers (igb_uio, vfio-pci)
2. Configure FPGA devices and load bitstreams
3. Validate hardware configuration
4. Generate configuration documentation
"""

import os
import sys
import json
import subprocess
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from hardware_detection import HardwareDetector, NetworkInterface, FPGADevice

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class HardwareConfigurator:
    """Hardware configuration manager for HFT deployment."""
    
    def __init__(self, inventory_file: str = "hardware_inventory.json"):
        """Initialize hardware configurator."""
        self.inventory_file = inventory_file
        self.inventory = None
        self.configuration_log = []
        
        # Load hardware inventory
        self._load_inventory()
        
        logger.info("Hardware configurator initialized")
    
    def _load_inventory(self):
        """Load hardware inventory from file."""
        try:
            with open(self.inventory_file, 'r') as f:
                inventory_data = json.load(f)
            
            # Convert back to objects
            self.inventory = inventory_data
            logger.info(f"Loaded hardware inventory from {self.inventory_file}")
            
        except FileNotFoundError:
            logger.error(f"Hardware inventory file not found: {self.inventory_file}")
            logger.info("Run hardware_detection.py first to generate inventory")
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to load hardware inventory: {e}")
            sys.exit(1)
    
    def configure_dpdk_interfaces(self, driver: str = "vfio-pci") -> bool:
        """Configure DPDK-compatible network interfaces."""
        logger.info(f"Configuring DPDK interfaces with {driver} driver...")
        
        success = True
        dpdk_interfaces = [
            nic for nic in self.inventory['network_interfaces'] 
            if nic['dpdk_compatible']
        ]
        
        if not dpdk_interfaces:
            logger.warning("No DPDK-compatible interfaces found")
            return False
        
        # Check if DPDK tools are available
        if not self._check_dpdk_tools():
            logger.error("DPDK tools not available")
            return False
        
        # Load required kernel modules
        if not self._load_dpdk_modules(driver):
            logger.error("Failed to load DPDK kernel modules")
            return False
        
        # Configure each interface
        for nic in dpdk_interfaces:
            pci_address = nic['pci_address']
            device_name = nic['device_name']
            
            logger.info(f"Configuring {device_name} at {pci_address}")
            
            try:
                # Bring interface down if it's up
                self._bring_interface_down(pci_address)
                
                # Bind to DPDK driver
                if self._bind_interface_to_dpdk(pci_address, driver):
                    self.configuration_log.append({
                        'timestamp': datetime.now().isoformat(),
                        'action': 'dpdk_bind',
                        'device': pci_address,
                        'driver': driver,
                        'status': 'success'
                    })
                    logger.info(f"Successfully bound {pci_address} to {driver}")
                else:
                    success = False
                    self.configuration_log.append({
                        'timestamp': datetime.now().isoformat(),
                        'action': 'dpdk_bind',
                        'device': pci_address,
                        'driver': driver,
                        'status': 'failed'
                    })
                    logger.error(f"Failed to bind {pci_address} to {driver}")
                
            except Exception as e:
                logger.error(f"Configuration failed for {pci_address}: {e}")
                success = False
        
        return success
    
    def configure_fpga_devices(self, bitstream_path: str = None) -> bool:
        """Configure FPGA devices for HFT acceleration."""
        logger.info("Configuring FPGA devices...")
        
        success = True
        fpga_devices = [
            fpga for fpga in self.inventory['fpga_devices'] 
            if fpga['fpga_compatible']
        ]
        
        if not fpga_devices:
            logger.info("No FPGA devices found - skipping FPGA configuration")
            return True
        
        for fpga in fpga_devices:
            pci_address = fpga['pci_address']
            device_name = fpga['device_name']
            
            logger.info(f"Configuring {device_name} at {pci_address}")
            
            try:
                # Configure based on vendor
                if 'xilinx' in device_name.lower():
                    if self._configure_xilinx_fpga(fpga, bitstream_path):
                        self.configuration_log.append({
                            'timestamp': datetime.now().isoformat(),
                            'action': 'fpga_configure',
                            'device': pci_address,
                            'vendor': 'xilinx',
                            'status': 'success'
                        })
                    else:
                        success = False
                        
                elif 'intel' in device_name.lower():
                    if self._configure_intel_fpga(fpga, bitstream_path):
                        self.configuration_log.append({
                            'timestamp': datetime.now().isoformat(),
                            'action': 'fpga_configure',
                            'device': pci_address,
                            'vendor': 'intel',
                            'status': 'success'
                        })
                    else:
                        success = False
                
            except Exception as e:
                logger.error(f"FPGA configuration failed for {pci_address}: {e}")
                success = False
        
        return success
    
    def _check_dpdk_tools(self) -> bool:
        """Check if DPDK tools are available."""
        try:
            result = subprocess.run(['dpdk-devbind.py', '--help'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            logger.error("dpdk-devbind.py not found. Install DPDK tools.")
            return False
    
    def _load_dpdk_modules(self, driver: str) -> bool:
        """Load required DPDK kernel modules."""
        logger.info(f"Loading DPDK kernel modules for {driver}")
        
        modules_to_load = ['uio']
        
        if driver == "igb_uio":
            modules_to_load.append('igb_uio')
        elif driver == "vfio-pci":
            modules_to_load.extend(['vfio', 'vfio_pci'])
        
        for module in modules_to_load:
            try:
                result = subprocess.run(['sudo', 'modprobe', module], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"Loaded kernel module: {module}")
                else:
                    logger.error(f"Failed to load kernel module {module}: {result.stderr}")
                    return False
            except Exception as e:
                logger.error(f"Error loading module {module}: {e}")
                return False
        
        return True
    
    def _bring_interface_down(self, pci_address: str):
        """Bring network interface down before binding to DPDK."""
        try:
            # Find interface name
            net_path = f"/sys/bus/pci/devices/0000:{pci_address}/net"
            if os.path.exists(net_path):
                interfaces = os.listdir(net_path)
                if interfaces:
                    interface_name = interfaces[0]
                    subprocess.run(['sudo', 'ip', 'link', 'set', interface_name, 'down'],
                                 capture_output=True, text=True)
                    logger.info(f"Brought interface {interface_name} down")
        except Exception as e:
            logger.debug(f"Could not bring interface down for {pci_address}: {e}")
    
    def _bind_interface_to_dpdk(self, pci_address: str, driver: str) -> bool:
        """Bind network interface to DPDK driver."""
        try:
            # First unbind from current driver
            unbind_result = subprocess.run([
                'sudo', 'dpdk-devbind.py', '--unbind', pci_address
            ], capture_output=True, text=True)
            
            # Then bind to DPDK driver
            bind_result = subprocess.run([
                'sudo', 'dpdk-devbind.py', '--bind', driver, pci_address
            ], capture_output=True, text=True)
            
            if bind_result.returncode == 0:
                logger.info(f"Successfully bound {pci_address} to {driver}")
                return True
            else:
                logger.error(f"Failed to bind {pci_address}: {bind_result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error binding {pci_address} to {driver}: {e}")
            return False
    
    def _configure_xilinx_fpga(self, fpga: Dict, bitstream_path: str = None) -> bool:
        """Configure Xilinx FPGA device."""
        pci_address = fpga['pci_address']
        
        try:
            # Check if Xilinx tools are available
            result = subprocess.run(['which', 'xbutil'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.warning("Xilinx xbutil not found - FPGA configuration skipped")
                return True  # Not a failure if tools aren't installed
            
            # Get device information
            examine_result = subprocess.run([
                'sudo', 'xbutil', 'examine', '-d', pci_address
            ], capture_output=True, text=True)
            
            if examine_result.returncode == 0:
                logger.info(f"Xilinx FPGA {pci_address} detected and accessible")
                
                # Load bitstream if provided
                if bitstream_path and os.path.exists(bitstream_path):
                    program_result = subprocess.run([
                        'sudo', 'xbutil', 'program', '-d', pci_address, '-u', bitstream_path
                    ], capture_output=True, text=True)
                    
                    if program_result.returncode == 0:
                        logger.info(f"Successfully programmed FPGA {pci_address} with {bitstream_path}")
                    else:
                        logger.error(f"Failed to program FPGA {pci_address}: {program_result.stderr}")
                        return False
                
                return True
            else:
                logger.error(f"Cannot access Xilinx FPGA {pci_address}: {examine_result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Xilinx FPGA configuration failed: {e}")
            return False
    
    def _configure_intel_fpga(self, fpga: Dict, bitstream_path: str = None) -> bool:
        """Configure Intel FPGA device."""
        pci_address = fpga['pci_address']
        
        try:
            # Check if Intel FPGA tools are available
            result = subprocess.run(['which', 'fpgainfo'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.warning("Intel fpgainfo not found - FPGA configuration skipped")
                return True  # Not a failure if tools aren't installed
            
            # Get device information
            info_result = subprocess.run([
                'sudo', 'fpgainfo', 'fme'
            ], capture_output=True, text=True)
            
            if info_result.returncode == 0:
                logger.info(f"Intel FPGA {pci_address} detected and accessible")
                
                # Load bitstream if provided
                if bitstream_path and os.path.exists(bitstream_path):
                    program_result = subprocess.run([
                        'sudo', 'fpgasupdate', bitstream_path
                    ], capture_output=True, text=True)
                    
                    if program_result.returncode == 0:
                        logger.info(f"Successfully programmed Intel FPGA {pci_address}")
                    else:
                        logger.error(f"Failed to program Intel FPGA: {program_result.stderr}")
                        return False
                
                return True
            else:
                logger.error(f"Cannot access Intel FPGA {pci_address}: {info_result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Intel FPGA configuration failed: {e}")
            return False
    
    def validate_configuration(self) -> bool:
        """Validate hardware configuration."""
        logger.info("Validating hardware configuration...")
        
        validation_results = {
            'dpdk_interfaces': [],
            'fpga_devices': [],
            'overall_status': True
        }
        
        # Validate DPDK interfaces
        try:
            dpdk_status_result = subprocess.run([
                'dpdk-devbind.py', '--status'
            ], capture_output=True, text=True)
            
            if dpdk_status_result.returncode == 0:
                # Parse DPDK status output
                status_output = dpdk_status_result.stdout
                
                for nic in self.inventory['network_interfaces']:
                    if nic['dpdk_compatible']:
                        pci_address = nic['pci_address']
                        
                        # Check if interface is bound to DPDK driver
                        if pci_address in status_output and 'drv=' in status_output:
                            validation_results['dpdk_interfaces'].append({
                                'pci_address': pci_address,
                                'status': 'configured',
                                'driver': 'dpdk'
                            })
                            logger.info(f"DPDK interface {pci_address} validated")
                        else:
                            validation_results['dpdk_interfaces'].append({
                                'pci_address': pci_address,
                                'status': 'not_configured',
                                'driver': 'unknown'
                            })
                            validation_results['overall_status'] = False
                            logger.error(f"DPDK interface {pci_address} not properly configured")
            
        except Exception as e:
            logger.error(f"DPDK validation failed: {e}")
            validation_results['overall_status'] = False
        
        # Validate FPGA devices
        for fpga in self.inventory['fpga_devices']:
            if fpga['fpga_compatible']:
                pci_address = fpga['pci_address']
                device_name = fpga['device_name']
                
                # Basic validation - check if device is accessible
                device_path = f"/sys/bus/pci/devices/0000:{pci_address}"
                if os.path.exists(device_path):
                    validation_results['fpga_devices'].append({
                        'pci_address': pci_address,
                        'status': 'accessible',
                        'device_name': device_name
                    })
                    logger.info(f"FPGA device {pci_address} validated")
                else:
                    validation_results['fpga_devices'].append({
                        'pci_address': pci_address,
                        'status': 'not_accessible',
                        'device_name': device_name
                    })
                    validation_results['overall_status'] = False
                    logger.error(f"FPGA device {pci_address} not accessible")
        
        # Save validation results
        self._save_validation_results(validation_results)
        
        return validation_results['overall_status']
    
    def _save_validation_results(self, results: Dict):
        """Save validation results to file."""
        results['timestamp'] = datetime.now().isoformat()
        results['hostname'] = self.inventory['hostname']
        
        with open('hardware_validation_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info("Validation results saved to hardware_validation_results.json")
    
    def save_configuration_log(self, output_path: str = "hardware_configuration_log.json"):
        """Save configuration log for audit purposes."""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'hostname': self.inventory['hostname'],
            'configuration_actions': self.configuration_log
        }
        
        with open(output_path, 'w') as f:
            json.dump(log_data, f, indent=2)
        
        logger.info(f"Configuration log saved to {output_path}")
    
    def generate_configuration_report(self) -> str:
        """Generate configuration report for documentation."""
        report = []
        report.append("=" * 80)
        report.append("ATHENATRADER PHASE 10 HFT HARDWARE CONFIGURATION REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().isoformat()}")
        report.append(f"Hostname: {self.inventory['hostname']}")
        report.append("")
        
        # DPDK Configuration
        report.append("DPDK CONFIGURATION:")
        dpdk_interfaces = [nic for nic in self.inventory['network_interfaces'] if nic['dpdk_compatible']]
        
        if dpdk_interfaces:
            for nic in dpdk_interfaces:
                report.append(f"  Device: {nic['device_name']}")
                report.append(f"  PCI Address: {nic['pci_address']}")
                report.append(f"  Serial Number: {nic.get('serial_number', 'N/A')}")
                report.append(f"  MAC Address: {nic.get('mac_address', 'N/A')}")
                report.append(f"  NUMA Node: {nic['numa_node']}")
                report.append("")
        else:
            report.append("  No DPDK-compatible interfaces configured")
        
        # FPGA Configuration
        report.append("FPGA CONFIGURATION:")
        fpga_devices = [fpga for fpga in self.inventory['fpga_devices'] if fpga['fpga_compatible']]
        
        if fpga_devices:
            for fpga in fpga_devices:
                report.append(f"  Device: {fpga['device_name']}")
                report.append(f"  PCI Address: {fpga['pci_address']}")
                report.append(f"  Serial Number: {fpga.get('serial_number', 'N/A')}")
                report.append(f"  Board Name: {fpga.get('board_name', 'N/A')}")
                report.append(f"  NUMA Node: {fpga['numa_node']}")
                report.append("")
        else:
            report.append("  No FPGA devices configured")
        
        # Configuration Actions
        report.append("CONFIGURATION ACTIONS:")
        if self.configuration_log:
            for action in self.configuration_log:
                report.append(f"  {action['timestamp']}: {action['action']} - {action['device']} - {action['status']}")
        else:
            report.append("  No configuration actions performed")
        
        return "\n".join(report)


def main():
    """Main hardware configuration function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Configure HFT hardware")
    parser.add_argument("--inventory", default="hardware_inventory.json",
                       help="Hardware inventory file")
    parser.add_argument("--dpdk-driver", choices=["igb_uio", "vfio-pci"], default="vfio-pci",
                       help="DPDK driver to use")
    parser.add_argument("--fpga-bitstream", help="Path to FPGA bitstream file")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate existing configuration")
    parser.add_argument("--report", action="store_true",
                       help="Generate configuration report")
    
    args = parser.parse_args()
    
    configurator = HardwareConfigurator(args.inventory)
    
    if args.validate_only:
        # Only validate configuration
        if configurator.validate_configuration():
            logger.info("Hardware configuration validation passed")
            sys.exit(0)
        else:
            logger.error("Hardware configuration validation failed")
            sys.exit(1)
    else:
        # Configure hardware
        success = True
        
        # Configure DPDK interfaces
        if not configurator.configure_dpdk_interfaces(args.dpdk_driver):
            success = False
        
        # Configure FPGA devices
        if not configurator.configure_fpga_devices(args.fpga_bitstream):
            success = False
        
        # Validate configuration
        if not configurator.validate_configuration():
            success = False
        
        # Save configuration log
        configurator.save_configuration_log()
        
        # Generate report if requested
        if args.report:
            report = configurator.generate_configuration_report()
            print(report)
            
            report_file = "hardware_configuration_report.txt"
            with open(report_file, 'w') as f:
                f.write(report)
            logger.info(f"Configuration report saved to {report_file}")
        
        if success:
            logger.info("Hardware configuration completed successfully")
            sys.exit(0)
        else:
            logger.error("Hardware configuration failed")
            sys.exit(1)


if __name__ == "__main__":
    main()
