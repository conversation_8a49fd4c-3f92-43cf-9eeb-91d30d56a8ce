#!/usr/bin/env python3
"""
AthenaTrader Phase 10 HFT Deployment Script

This script automates the deployment and configuration of Phase 10 HFT capabilities
including DPDK setup, FPGA configuration, and system optimization.

Usage:
    python deploy_hft.py --environment [dev|staging|prod] --enable-dpdk --enable-fpga
"""

import os
import sys
import argparse
import subprocess
import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HFTDeployment:
    """HFT deployment and configuration manager."""
    
    def __init__(self, environment: str, enable_dpdk: bool = False, enable_fpga: bool = False):
        """Initialize HFT deployment."""
        self.environment = environment
        self.enable_dpdk = enable_dpdk
        self.enable_fpga = enable_fpga
        self.config = {}
        
        logger.info(f"Initializing HFT deployment for {environment} environment")
        logger.info(f"DPDK enabled: {enable_dpdk}, FPGA enabled: {enable_fpga}")
    
    def validate_system_requirements(self) -> bool:
        """Validate system requirements for HFT deployment."""
        logger.info("Validating system requirements...")
        
        requirements_met = True
        
        # Check Python version
        if sys.version_info < (3, 9):
            logger.error("Python 3.9+ required for HFT capabilities")
            requirements_met = False
        
        # Check available memory
        try:
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
                mem_total_kb = int([line for line in meminfo.split('\n') if 'MemTotal' in line][0].split()[1])
                mem_total_gb = mem_total_kb / 1024 / 1024
                
                if mem_total_gb < 32:
                    logger.warning(f"Recommended 64GB+ memory, found {mem_total_gb:.1f}GB")
                else:
                    logger.info(f"Memory check passed: {mem_total_gb:.1f}GB available")
        except Exception as e:
            logger.warning(f"Could not check memory: {e}")
        
        # Check CPU features
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
                if 'avx2' not in cpuinfo:
                    logger.warning("AVX2 CPU instructions not detected - performance may be limited")
                else:
                    logger.info("CPU features check passed")
        except Exception as e:
            logger.warning(f"Could not check CPU features: {e}")
        
        # Check huge pages if DPDK enabled
        if self.enable_dpdk:
            if not self._check_huge_pages():
                logger.error("Huge pages not configured - required for DPDK")
                requirements_met = False
        
        return requirements_met
    
    def _check_huge_pages(self) -> bool:
        """Check if huge pages are configured."""
        try:
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
                huge_pages_total = int([line for line in meminfo.split('\n') if 'HugePages_Total' in line][0].split()[1])
                
                if huge_pages_total < 512:
                    logger.error(f"Insufficient huge pages: {huge_pages_total} (need 512+)")
                    return False
                else:
                    logger.info(f"Huge pages check passed: {huge_pages_total} available")
                    return True
        except Exception as e:
            logger.error(f"Could not check huge pages: {e}")
            return False
    
    def setup_system_configuration(self) -> bool:
        """Setup system configuration for HFT."""
        logger.info("Setting up system configuration...")
        
        try:
            # Set CPU governor to performance
            self._run_command("echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor")
            
            # Disable CPU frequency scaling
            self._run_command("sudo cpupower frequency-set -g performance")
            
            # Set network interface optimizations
            self._optimize_network_interfaces()
            
            # Configure kernel parameters
            self._configure_kernel_parameters()
            
            logger.info("System configuration completed")
            return True
            
        except Exception as e:
            logger.error(f"System configuration failed: {e}")
            return False
    
    def _optimize_network_interfaces(self):
        """Optimize network interfaces for low latency."""
        logger.info("Optimizing network interfaces...")
        
        # Get network interfaces
        result = subprocess.run(['ip', 'link', 'show'], capture_output=True, text=True)
        interfaces = []
        
        for line in result.stdout.split('\n'):
            if 'state UP' in line and 'eth' in line:
                interface = line.split(':')[1].strip()
                interfaces.append(interface)
        
        for interface in interfaces:
            try:
                # Increase ring buffer sizes
                self._run_command(f"sudo ethtool -G {interface} rx 4096 tx 4096")
                
                # Disable interrupt coalescing
                self._run_command(f"sudo ethtool -C {interface} rx-usecs 0 tx-usecs 0")
                
                # Enable hardware timestamping if available
                self._run_command(f"sudo ethtool -T {interface}")
                
                logger.info(f"Optimized interface: {interface}")
                
            except Exception as e:
                logger.warning(f"Could not optimize interface {interface}: {e}")
    
    def _configure_kernel_parameters(self):
        """Configure kernel parameters for HFT."""
        logger.info("Configuring kernel parameters...")
        
        kernel_params = {
            'net.core.rmem_max': '134217728',
            'net.core.wmem_max': '134217728',
            'net.core.rmem_default': '65536',
            'net.core.wmem_default': '65536',
            'net.ipv4.tcp_rmem': '4096 65536 134217728',
            'net.ipv4.tcp_wmem': '4096 65536 134217728',
            'net.core.netdev_max_backlog': '30000',
            'net.ipv4.tcp_no_metrics_save': '1',
            'net.ipv4.tcp_congestion_control': 'bbr'
        }
        
        for param, value in kernel_params.items():
            try:
                self._run_command(f"echo '{param} = {value}' | sudo tee -a /etc/sysctl.conf")
            except Exception as e:
                logger.warning(f"Could not set kernel parameter {param}: {e}")
        
        # Apply sysctl changes
        self._run_command("sudo sysctl -p")
    
    def setup_dpdk(self) -> bool:
        """Setup DPDK configuration."""
        if not self.enable_dpdk:
            logger.info("DPDK disabled, skipping setup")
            return True
        
        logger.info("Setting up DPDK...")
        
        try:
            # Check if DPDK is installed
            result = subprocess.run(['dpdk-devbind.py', '--status'], capture_output=True, text=True)
            if result.returncode != 0:
                logger.error("DPDK not installed or not in PATH")
                return False
            
            # Configure huge pages
            self._run_command("echo 1024 | sudo tee /sys/kernel/mm/hugepages/hugepages-2048kB/nr_hugepages")
            
            # Mount huge pages
            self._run_command("sudo mkdir -p /mnt/huge")
            self._run_command("sudo mount -t hugetlbfs nodev /mnt/huge")
            
            # Load DPDK modules
            self._run_command("sudo modprobe uio")
            self._run_command("sudo modprobe igb_uio")
            
            logger.info("DPDK setup completed")
            return True
            
        except Exception as e:
            logger.error(f"DPDK setup failed: {e}")
            return False
    
    def setup_fpga(self) -> bool:
        """Setup FPGA configuration."""
        if not self.enable_fpga:
            logger.info("FPGA disabled, skipping setup")
            return True
        
        logger.info("Setting up FPGA...")
        
        try:
            # Check for FPGA devices
            result = subprocess.run(['lspci', '|', 'grep', 'Xilinx'], shell=True, capture_output=True, text=True)
            if not result.stdout:
                logger.warning("No Xilinx FPGA devices detected")
            
            # Check for Intel FPGA devices
            result = subprocess.run(['lspci', '|', 'grep', 'Intel.*FPGA'], shell=True, capture_output=True, text=True)
            if not result.stdout:
                logger.warning("No Intel FPGA devices detected")
            
            # Create FPGA configuration directory
            fpga_config_dir = Path("/opt/athena/fpga")
            fpga_config_dir.mkdir(parents=True, exist_ok=True)
            
            logger.info("FPGA setup completed (simulation mode)")
            return True
            
        except Exception as e:
            logger.error(f"FPGA setup failed: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """Install HFT dependencies."""
        logger.info("Installing HFT dependencies...")
        
        try:
            # Install Python dependencies
            self._run_command("pip install -r requirements.txt")
            
            # Install system dependencies
            if self.enable_dpdk:
                self._run_command("sudo apt-get update")
                self._run_command("sudo apt-get install -y dpdk dpdk-dev")
            
            if self.enable_fpga:
                # FPGA tools would be installed here in production
                logger.info("FPGA tools installation skipped (simulation mode)")
            
            logger.info("Dependencies installation completed")
            return True
            
        except Exception as e:
            logger.error(f"Dependencies installation failed: {e}")
            return False
    
    def generate_configuration(self) -> Dict[str, Any]:
        """Generate HFT configuration."""
        logger.info("Generating HFT configuration...")
        
        config = {
            "environment": self.environment,
            "hft_capabilities": {
                "dpdk": {
                    "enabled": self.enable_dpdk,
                    "core_mask": "0x3",
                    "memory_channels": 4,
                    "huge_pages": 1024
                },
                "fpga": {
                    "enabled": self.enable_fpga,
                    "device_id": "/dev/xdma0" if self.enable_fpga else "",
                    "clock_frequency_mhz": 250
                },
                "performance_targets": {
                    "order_processing_latency_us": 50,
                    "risk_check_latency_us": 5,
                    "sor_routing_latency_us": 25,
                    "throughput_orders_per_sec": 100000
                }
            },
            "monitoring": {
                "latency_alerting_threshold_us": 100,
                "throughput_alerting_threshold": 50000,
                "circuit_breaker_monitoring": True
            }
        }
        
        # Save configuration
        config_path = Path("config/hft_config.json")
        config_path.parent.mkdir(exist_ok=True)
        
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Configuration saved to {config_path}")
        return config
    
    def validate_deployment(self) -> bool:
        """Validate HFT deployment."""
        logger.info("Validating HFT deployment...")
        
        try:
            # Run basic tests
            result = subprocess.run([
                'python', '-m', 'pytest', 
                'tests/test_hft_capabilities.py::TestUltraLowLatencyEngine::test_engine_initialization',
                '-v'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("Basic HFT tests passed")
            else:
                logger.error(f"HFT tests failed: {result.stderr}")
                return False
            
            # Check service health
            # In production, this would check actual service endpoints
            logger.info("Service health check passed (simulation)")
            
            return True
            
        except Exception as e:
            logger.error(f"Deployment validation failed: {e}")
            return False
    
    def _run_command(self, command: str) -> str:
        """Run shell command and return output."""
        logger.debug(f"Running command: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception(f"Command failed: {command}\nError: {result.stderr}")
        
        return result.stdout
    
    def deploy(self) -> bool:
        """Execute complete HFT deployment."""
        logger.info("Starting HFT deployment...")
        
        steps = [
            ("Validate system requirements", self.validate_system_requirements),
            ("Setup system configuration", self.setup_system_configuration),
            ("Install dependencies", self.install_dependencies),
            ("Setup DPDK", self.setup_dpdk),
            ("Setup FPGA", self.setup_fpga),
            ("Generate configuration", lambda: self.generate_configuration() is not None),
            ("Validate deployment", self.validate_deployment)
        ]
        
        for step_name, step_func in steps:
            logger.info(f"Executing: {step_name}")
            
            try:
                if not step_func():
                    logger.error(f"Step failed: {step_name}")
                    return False
                logger.info(f"Step completed: {step_name}")
            except Exception as e:
                logger.error(f"Step failed with exception: {step_name} - {e}")
                return False
        
        logger.info("HFT deployment completed successfully!")
        return True


def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description="Deploy AthenaTrader Phase 10 HFT capabilities")
    parser.add_argument("--environment", choices=["dev", "staging", "prod"], default="dev",
                       help="Deployment environment")
    parser.add_argument("--enable-dpdk", action="store_true",
                       help="Enable DPDK kernel bypass")
    parser.add_argument("--enable-fpga", action="store_true",
                       help="Enable FPGA acceleration")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate system requirements")
    
    args = parser.parse_args()
    
    # Create deployment manager
    deployment = HFTDeployment(
        environment=args.environment,
        enable_dpdk=args.enable_dpdk,
        enable_fpga=args.enable_fpga
    )
    
    if args.validate_only:
        # Only validate system requirements
        if deployment.validate_system_requirements():
            logger.info("System requirements validation passed")
            sys.exit(0)
        else:
            logger.error("System requirements validation failed")
            sys.exit(1)
    else:
        # Execute full deployment
        if deployment.deploy():
            logger.info("Deployment successful")
            sys.exit(0)
        else:
            logger.error("Deployment failed")
            sys.exit(1)


if __name__ == "__main__":
    main()
