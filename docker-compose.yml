version: '3.8'

services:
  # Database Services
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: athena-postgres
    environment:
      POSTGRES_DB: athena_trader
      POSTGRES_USER: athena_user
      POSTGRES_PASSWORD: athena_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/db/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - athena-network

  redis:
    image: redis:7-alpine
    container_name: athena-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - athena-network

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: athena-api-gateway
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/api-gateway:/app
      - ./libs:/app/libs
    networks:
      - athena-network

  # Data Nexus Service
  data-nexus:
    build:
      context: .
      dockerfile: services/data-nexus/Dockerfile
    container_name: athena-data-nexus
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - ALPHA_VANTAGE_API_KEY=${ALPHA_VANTAGE_API_KEY}
    depends_on:
      - postgres
      - redis
    volumes:
      - ./services/data-nexus:/app
      - ./libs:/app/libs
    networks:
      - athena-network

  # Strategy Genesis Service
  strategy-genesis:
    build:
      context: ./services/strategy-genesis
      dockerfile: Dockerfile
    container_name: athena-strategy-genesis
    ports:
      - "8002:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - MODEL_STORAGE_PATH=/app/models
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
    volumes:
      - ./services/strategy-genesis:/app
      - strategy_models:/app/models
    networks:
      - athena-network

  # Backtesting Engine Service
  backtesting-engine:
    build:
      context: ./services/backtesting-engine
      dockerfile: Dockerfile
    container_name: athena-backtesting-engine
    ports:
      - "8003:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - RESULTS_STORAGE_PATH=/app/results
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
    volumes:
      - ./services/backtesting-engine:/app
      - backtest_results:/app/results
    networks:
      - athena-network

  # Portfolio Construction Service
  portfolio-construction:
    build:
      context: ./services/portfolio-construction
      dockerfile: Dockerfile
    container_name: athena-portfolio-construction
    ports:
      - "8004:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - BACKTESTING_ENGINE_URL=http://backtesting-engine:8000
      - RESULTS_STORAGE_PATH=/app/results
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
      - backtesting-engine
    volumes:
      - ./services/portfolio-construction:/app
      - portfolio_results:/app/results
    networks:
      - athena-network

  # XAI Module Service
  xai-module:
    build:
      context: ./services/xai-module
      dockerfile: Dockerfile
    container_name: athena-xai-module
    ports:
      - "8005:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - BACKTESTING_ENGINE_URL=http://backtesting-engine:8000
      - PORTFOLIO_CONSTRUCTION_URL=http://portfolio-construction:8000
      - EXPLANATIONS_STORAGE_PATH=/app/explanations
      - VISUALIZATIONS_STORAGE_PATH=/app/visualizations
      - AUDIT_LOGS_STORAGE_PATH=/app/audit_logs
      - MODEL_ARTIFACTS_STORAGE_PATH=/app/model_artifacts
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
      - backtesting-engine
      - portfolio-construction
    volumes:
      - ./services/xai-module:/app
      - xai_explanations:/app/explanations
      - xai_visualizations:/app/visualizations
      - xai_audit_logs:/app/audit_logs
      - xai_model_artifacts:/app/model_artifacts
    networks:
      - athena-network

  # Live Execution Module Service
  live-execution:
    build:
      context: ./services/live-execution
      dockerfile: Dockerfile
    container_name: athena-live-execution
    ports:
      - "8006:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - BACKTESTING_ENGINE_URL=http://backtesting-engine:8000
      - PORTFOLIO_CONSTRUCTION_URL=http://portfolio-construction:8000
      - XAI_MODULE_URL=http://xai-module:8000
      - EXECUTION_LOGS_PATH=/app/execution_logs
      - STRATEGY_ARTIFACTS_PATH=/app/strategy_artifacts
      - RISK_REPORTS_PATH=/app/risk_reports
      - EXECUTION_REPORTS_PATH=/app/execution_reports
      - ENABLE_PAPER_TRADING=true
      - ENABLE_CIRCUIT_BREAKERS=true
      - ENABLE_REAL_TIME_XAI=true
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
      - backtesting-engine
      - portfolio-construction
      - xai-module
    volumes:
      - ./services/live-execution:/app
      - live_execution_logs:/app/execution_logs
      - live_strategy_artifacts:/app/strategy_artifacts
      - live_risk_reports:/app/risk_reports
      - live_execution_reports:/app/execution_reports
    networks:
      - athena-network

  # Regulatory Certification Service
  regulatory-certification:
    build:
      context: ./services/advanced-analytics
      dockerfile: Dockerfile
    container_name: athena-regulatory-certification
    ports:
      - "8007:8000"
    environment:
      - DATABASE_URL=******************************************************/athena_trader
      - REDIS_URL=redis://redis:6379
      - DATA_NEXUS_URL=http://data-nexus:8000
      - STRATEGY_GENESIS_URL=http://strategy-genesis:8000
      - BACKTESTING_ENGINE_URL=http://backtesting-engine:8000
      - PORTFOLIO_CONSTRUCTION_URL=http://portfolio-construction:8000
      - XAI_MODULE_URL=http://xai-module:8000
      - LIVE_EXECUTION_URL=http://live-execution:8000
      - COMPLIANCE_SCORE_TARGET=0.95
      - EMIR_TRADE_REPOSITORY=DTCC_GTR
      - EMIR_REPORTING_THRESHOLD_EUR=1000000
      - DODD_FRANK_SWAP_THRESHOLD_USD=8000000000
      - MIFID_COMPETENT_AUTHORITY=FCA
      - FINRA_NET_CAPITAL_THRESHOLD_USD=250000
      - GDPR_ENABLED=true
      - CCPA_ENABLED=true
      - BLOCKCHAIN_NETWORK=hyperledger
      - AUDIT_RETENTION_YEARS=7
      - DEBUG=true
    depends_on:
      - postgres
      - redis
      - data-nexus
      - strategy-genesis
      - backtesting-engine
      - portfolio-construction
      - xai-module
      - live-execution
    volumes:
      - ./services/advanced-analytics:/app
      - regulatory_compliance_logs:/app/compliance_logs
      - regulatory_audit_trails:/app/audit_trails
      - regulatory_reports:/app/regulatory_reports
    networks:
      - athena-network



  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: athena-frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - athena-network

volumes:
  postgres_data:
  redis_data:
  strategy_models:
  backtest_results:
  portfolio_results:
  xai_explanations:
  xai_visualizations:
  xai_audit_logs:
  xai_model_artifacts:
  live_execution_logs:
  live_strategy_artifacts:
  live_risk_reports:
  live_execution_reports:
  regulatory_compliance_logs:
  regulatory_audit_trails:
  regulatory_reports:

networks:
  athena-network:
    driver: bridge
