{"timestamp": "2025-05-25T21:45:22.864231", "test_duration_seconds": 0, "total_tests": 5, "passed_tests": 2, "failed_tests": 3, "compliance_score": 40.0, "test_results": [{"test_name": "Position Limit Enforcement", "test_category": "Risk Management", "passed": true, "details": "Large order validation result: False - Correctly rejected large position", "execution_time_ms": 0.1494884490966797, "regulatory_requirement": "MiFID II Article 17"}, {"test_name": "Loss Limit Enforcement", "test_category": "Risk Management", "passed": true, "details": "Loss limit circuit breaker configured: True - Threshold: $1,000,000", "execution_time_ms": 0.10633468627929688, "regulatory_requirement": "FINRA Rule 15c3-5"}, {"test_name": "Risk Check Latency", "test_category": "Performance", "passed": false, "details": "P95 latency: 112.8μs (target: 5.0μs) - FAILED", "execution_time_ms": 1923.8052368164062, "regulatory_requirement": "Institutional Standards"}, {"test_name": "Order Validation Completeness", "test_category": "Risk Management", "passed": false, "details": "Test failed with exception: 1 validation error for OrderRequest\nquantity\n  Input should be greater than 0 [type=greater_than, input_value=Decimal('0'), input_type=Decimal]\n    For further information visit https://errors.pydantic.dev/2.10/v/greater_than", "execution_time_ms": 0.07009506225585938, "regulatory_requirement": "MiFID II Article 17"}, {"test_name": "Risk Scoring Accuracy", "test_category": "Risk Management", "passed": false, "details": "Low risk score: 0.190, High risk score: 0.000 - Risk scoring may be incorrect", "execution_time_ms": 0.08654594421386719, "regulatory_requirement": "Institutional Standards"}], "regulatory_compliance": {}, "overall_status": "Risk Tests Only"}