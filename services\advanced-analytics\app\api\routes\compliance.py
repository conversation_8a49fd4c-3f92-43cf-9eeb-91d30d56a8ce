"""
AthenaTrader Phase 11 Advanced Analytics Engine - Regulatory Compliance API Routes

PRIORITY 4: Advanced Regulatory Compliance Modules
- EMIR compliance engine
- Dodd-Frank compliance framework
- Real-time trade surveillance system
- Blockchain-based audit trail system
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field
from ...compliance.compliance_engine import (
    EMIRComplianceEngine, DoddFrankComplianceEngine, TradeSurveillanceEngine,
    TradeRecord, RegulationType, ComplianceStatus, SurveillancePattern
)
from ...compliance.blockchain_audit import BlockchainAuditTrail, AuditEventType
from ...core.logging_config import performance_logger, compliance_logger
from ...core.database import DatabaseManager

logger = logging.getLogger("compliance")
router = APIRouter()


# Pydantic models for request/response
class TradeComplianceRequest(BaseModel):
    trade_id: str = Field(..., description="Unique trade identifier")
    timestamp: str = Field(..., description="Trade timestamp (ISO format)")
    symbol: str = Field(..., description="Trading symbol")
    side: str = Field(..., description="Trade side (buy/sell)")
    quantity: float = Field(..., description="Trade quantity")
    price: float = Field(..., description="Trade price")
    counterparty: str = Field(..., description="Counterparty identifier")
    trader_id: str = Field(..., description="Trader identifier")
    strategy_id: str = Field(..., description="Strategy identifier")
    venue: str = Field(..., description="Trading venue")
    order_type: str = Field(..., description="Order type")
    execution_algo: Optional[str] = Field(default=None, description="Execution algorithm")


class ComplianceCheckResponse(BaseModel):
    check_id: str
    trade_id: str
    regulation_type: str
    check_type: str
    status: str
    risk_score: float
    details: Dict[str, Any]
    requires_reporting: bool
    remediation_required: bool
    timestamp: str


class SurveillanceRequest(BaseModel):
    trades: List[TradeComplianceRequest] = Field(..., description="List of trades to analyze")
    analysis_window_hours: int = Field(default=24, description="Analysis window in hours")
    patterns: List[str] = Field(default=None, description="Specific patterns to detect")


class SurveillanceAlertResponse(BaseModel):
    alert_id: str
    pattern_type: str
    trades_involved: List[str]
    risk_score: float
    confidence: float
    description: str
    investigation_required: bool
    regulatory_impact: List[str]
    timestamp: str


class AuditTrailRequest(BaseModel):
    event_type: str = Field(..., description="Type of audit event")
    user_id: str = Field(..., description="User identifier")
    entity_id: str = Field(..., description="Entity identifier")
    event_data: Dict[str, Any] = Field(..., description="Event data")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class ComplianceReportRequest(BaseModel):
    regulation_type: str = Field(..., description="Regulation type (emir, dodd_frank, etc.)")
    start_date: str = Field(..., description="Report start date (YYYY-MM-DD)")
    end_date: str = Field(..., description="Report end date (YYYY-MM-DD)")
    include_violations_only: bool = Field(default=False, description="Include only violations")


# Global instances (would be dependency injected in production)
emir_engine = None
dodd_frank_engine = None
surveillance_engine = None
blockchain_audit = None


async def get_compliance_engines():
    """Initialize and get compliance engines."""
    global emir_engine, dodd_frank_engine, surveillance_engine, blockchain_audit
    
    if emir_engine is None:
        emir_engine = EMIRComplianceEngine()
    
    if dodd_frank_engine is None:
        dodd_frank_engine = DoddFrankComplianceEngine()
    
    if surveillance_engine is None:
        surveillance_engine = TradeSurveillanceEngine()
    
    if blockchain_audit is None:
        blockchain_audit = BlockchainAuditTrail()
        await blockchain_audit.initialize()
    
    return emir_engine, dodd_frank_engine, surveillance_engine, blockchain_audit


@router.get("/status")
async def get_compliance_status():
    """Get status of compliance system."""
    try:
        emir, dodd_frank, surveillance, audit = await get_compliance_engines()
        
        return {
            "status": "operational",
            "compliance_engines": {
                "emir": "active",
                "dodd_frank": "active",
                "mifid2": "planned",
                "finra": "planned"
            },
            "surveillance_system": {
                "status": "monitoring",
                "patterns_detected": len(surveillance.surveillance_patterns),
                "supported_patterns": [pattern.value for pattern in SurveillancePattern]
            },
            "blockchain_audit": {
                "status": "operational",
                "network": "hyperledger_fabric",
                "blocks_created": len(audit.local_chain) if audit else 0
            },
            "regulatory_frameworks": [
                "EMIR (European Market Infrastructure Regulation)",
                "Dodd-Frank (US Derivatives Regulation)",
                "MiFID II (Markets in Financial Instruments Directive)",
                "FINRA (Financial Industry Regulatory Authority)",
                "SOX (Sarbanes-Oxley Act)"
            ],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting compliance status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/check/emir", response_model=ComplianceCheckResponse)
async def check_emir_compliance(request: TradeComplianceRequest):
    """
    Check EMIR compliance for a trade.
    
    EMIR (European Market Infrastructure Regulation) requires:
    - Reporting of derivative transactions
    - Central clearing for standardized derivatives
    - Risk mitigation for non-centrally cleared derivatives
    - Real-time reporting to trade repositories
    """
    try:
        start_time = datetime.now()
        
        emir, _, _, audit = await get_compliance_engines()
        
        # Convert request to TradeRecord
        trade_record = TradeRecord(
            trade_id=request.trade_id,
            timestamp=datetime.fromisoformat(request.timestamp.replace('Z', '+00:00')),
            symbol=request.symbol,
            side=request.side,
            quantity=request.quantity,
            price=request.price,
            counterparty=request.counterparty,
            trader_id=request.trader_id,
            strategy_id=request.strategy_id,
            venue=request.venue,
            order_type=request.order_type,
            execution_algo=request.execution_algo
        )
        
        # Perform EMIR compliance check
        compliance_result = await emir.check_emir_compliance(trade_record)
        
        # Record audit event
        await audit.record_audit_event(
            event_type=AuditEventType.COMPLIANCE_CHECK,
            user_id=request.trader_id,
            entity_id=request.trade_id,
            event_data={
                "regulation": "EMIR",
                "check_result": compliance_result.status.value,
                "risk_score": compliance_result.risk_score,
                "requires_reporting": compliance_result.requires_reporting
            }
        )
        
        # Save to database
        await DatabaseManager.save_compliance_record(
            trade_id=request.trade_id,
            regulation_type="emir",
            compliance_check="emir_reporting",
            status=compliance_result.status.value,
            risk_score=compliance_result.risk_score,
            details=compliance_result.details
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/compliance/check/emir",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return ComplianceCheckResponse(
            check_id=compliance_result.check_id,
            trade_id=compliance_result.trade_id,
            regulation_type=compliance_result.regulation_type.value,
            check_type=compliance_result.check_type,
            status=compliance_result.status.value,
            risk_score=compliance_result.risk_score,
            details=compliance_result.details,
            requires_reporting=compliance_result.requires_reporting,
            remediation_required=compliance_result.remediation_required,
            timestamp=compliance_result.timestamp.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in EMIR compliance check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/check/dodd-frank", response_model=ComplianceCheckResponse)
async def check_dodd_frank_compliance(request: TradeComplianceRequest):
    """
    Check Dodd-Frank compliance for a trade.
    
    Dodd-Frank Act requirements include:
    - Swap dealer registration for entities above threshold
    - Real-time reporting of swap transactions
    - Central clearing for standardized swaps
    - Margin requirements for non-cleared swaps
    """
    try:
        start_time = datetime.now()
        
        _, dodd_frank, _, audit = await get_compliance_engines()
        
        # Convert request to TradeRecord
        trade_record = TradeRecord(
            trade_id=request.trade_id,
            timestamp=datetime.fromisoformat(request.timestamp.replace('Z', '+00:00')),
            symbol=request.symbol,
            side=request.side,
            quantity=request.quantity,
            price=request.price,
            counterparty=request.counterparty,
            trader_id=request.trader_id,
            strategy_id=request.strategy_id,
            venue=request.venue,
            order_type=request.order_type,
            execution_algo=request.execution_algo
        )
        
        # Perform Dodd-Frank compliance check
        compliance_result = await dodd_frank.check_dodd_frank_compliance(trade_record)
        
        # Record audit event
        await audit.record_audit_event(
            event_type=AuditEventType.COMPLIANCE_CHECK,
            user_id=request.trader_id,
            entity_id=request.trade_id,
            event_data={
                "regulation": "Dodd-Frank",
                "check_result": compliance_result.status.value,
                "risk_score": compliance_result.risk_score,
                "requires_reporting": compliance_result.requires_reporting
            }
        )
        
        # Save to database
        await DatabaseManager.save_compliance_record(
            trade_id=request.trade_id,
            regulation_type="dodd_frank",
            compliance_check="swap_reporting",
            status=compliance_result.status.value,
            risk_score=compliance_result.risk_score,
            details=compliance_result.details
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/compliance/check/dodd-frank",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return ComplianceCheckResponse(
            check_id=compliance_result.check_id,
            trade_id=compliance_result.trade_id,
            regulation_type=compliance_result.regulation_type.value,
            check_type=compliance_result.check_type,
            status=compliance_result.status.value,
            risk_score=compliance_result.risk_score,
            details=compliance_result.details,
            requires_reporting=compliance_result.requires_reporting,
            remediation_required=compliance_result.remediation_required,
            timestamp=compliance_result.timestamp.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in Dodd-Frank compliance check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/surveillance/analyze")
async def analyze_trade_surveillance(request: SurveillanceRequest):
    """
    Analyze trades for suspicious patterns and market manipulation.
    
    Detects patterns including:
    - Layering: Placing and canceling orders to manipulate price
    - Spoofing: Placing orders with intent to cancel
    - Wash trading: Trading with yourself or related entities
    - Momentum ignition: Aggressive trading to trigger algorithms
    - Quote stuffing: Excessive order submissions to slow systems
    """
    try:
        start_time = datetime.now()
        
        if not request.trades:
            raise HTTPException(status_code=400, detail="No trades provided for analysis")
        
        if len(request.trades) > 10000:
            raise HTTPException(status_code=400, detail="Maximum 10,000 trades per analysis")
        
        _, _, surveillance, audit = await get_compliance_engines()
        
        # Convert requests to TradeRecords
        trade_records = []
        for trade_req in request.trades:
            trade_record = TradeRecord(
                trade_id=trade_req.trade_id,
                timestamp=datetime.fromisoformat(trade_req.timestamp.replace('Z', '+00:00')),
                symbol=trade_req.symbol,
                side=trade_req.side,
                quantity=trade_req.quantity,
                price=trade_req.price,
                counterparty=trade_req.counterparty,
                trader_id=trade_req.trader_id,
                strategy_id=trade_req.strategy_id,
                venue=trade_req.venue,
                order_type=trade_req.order_type,
                execution_algo=trade_req.execution_algo
            )
            trade_records.append(trade_record)
        
        # Analyze for suspicious patterns
        surveillance_alerts = await surveillance.analyze_trade_patterns(trade_records)
        
        # Record surveillance analysis
        await audit.record_audit_event(
            event_type=AuditEventType.SURVEILLANCE_ALERT,
            user_id="surveillance_system",
            entity_id="trade_analysis",
            event_data={
                "trades_analyzed": len(trade_records),
                "alerts_generated": len(surveillance_alerts),
                "analysis_window_hours": request.analysis_window_hours
            }
        )
        
        # Convert alerts to response format
        alert_responses = []
        for alert in surveillance_alerts:
            alert_response = SurveillanceAlertResponse(
                alert_id=alert.alert_id,
                pattern_type=alert.pattern_type.value,
                trades_involved=alert.trades_involved,
                risk_score=alert.risk_score,
                confidence=alert.confidence,
                description=alert.description,
                investigation_required=alert.investigation_required,
                regulatory_impact=[reg.value for reg in alert.regulatory_impact],
                timestamp=alert.timestamp.isoformat()
            )
            alert_responses.append(alert_response)
            
            # Log high-risk alerts
            if alert.risk_score > 0.7:
                compliance_logger.log_trade_surveillance(
                    trade_id=",".join(alert.trades_involved),
                    pattern_detected=alert.pattern_type.value,
                    risk_score=alert.risk_score
                )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/compliance/surveillance/analyze",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return {
            "analysis_summary": {
                "trades_analyzed": len(trade_records),
                "alerts_generated": len(surveillance_alerts),
                "high_risk_alerts": len([a for a in surveillance_alerts if a.risk_score > 0.7]),
                "investigation_required": len([a for a in surveillance_alerts if a.investigation_required])
            },
            "alerts": alert_responses,
            "analysis_timestamp": datetime.now().isoformat(),
            "processing_time_ms": api_time
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in trade surveillance analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/audit/record")
async def record_audit_event(request: AuditTrailRequest):
    """
    Record an audit event to the blockchain.
    
    Creates an immutable audit trail for:
    - Trade executions
    - Compliance checks
    - System access events
    - Data modifications
    - Policy changes
    """
    try:
        start_time = datetime.now()
        
        _, _, _, audit = await get_compliance_engines()
        
        # Validate event type
        try:
            event_type = AuditEventType(request.event_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid event type: {request.event_type}")
        
        # Record audit event
        record_id = await audit.record_audit_event(
            event_type=event_type,
            user_id=request.user_id,
            entity_id=request.entity_id,
            event_data=request.event_data,
            metadata=request.metadata
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/compliance/audit/record",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return {
            "record_id": record_id,
            "event_type": request.event_type,
            "blockchain_recorded": True,
            "timestamp": datetime.now().isoformat(),
            "processing_time_ms": api_time
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error recording audit event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/reports/generate")
async def generate_compliance_report(request: ComplianceReportRequest):
    """
    Generate comprehensive compliance report.
    
    Provides regulatory reporting for:
    - EMIR transaction reporting
    - Dodd-Frank swap reporting
    - Trade surveillance summaries
    - Audit trail verification
    """
    try:
        start_time = datetime.now()
        
        # Validate dates
        try:
            start_date = datetime.strptime(request.start_date, "%Y-%m-%d")
            end_date = datetime.strptime(request.end_date, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
        
        if start_date >= end_date:
            raise HTTPException(status_code=400, detail="Start date must be before end date")
        
        _, _, _, audit = await get_compliance_engines()
        
        # Generate compliance report from blockchain audit trail
        report = await audit.generate_compliance_report(
            regulation_type=request.regulation_type,
            start_date=start_date,
            end_date=end_date
        )
        
        # Add additional analysis
        report["report_metadata"] = {
            "generated_by": "athenatrader_compliance_system",
            "report_version": "1.0",
            "data_sources": ["blockchain_audit_trail", "compliance_database"],
            "verification_status": "verified" if report.get("audit_trail_verified") else "unverified"
        }
        
        # Record report generation
        await audit.record_audit_event(
            event_type=AuditEventType.REGULATORY_REPORT,
            user_id="compliance_system",
            entity_id=f"report_{request.regulation_type}",
            event_data={
                "regulation_type": request.regulation_type,
                "report_period": f"{request.start_date} to {request.end_date}",
                "total_events": report.get("total_events", 0),
                "violations_count": len(report.get("violations", []))
            }
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/compliance/reports/generate",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating compliance report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/audit/verify")
async def verify_audit_trail(start_block: int = Query(default=0, ge=0),
                           end_block: int = Query(default=None, ge=0)):
    """
    Verify integrity of blockchain audit trail.
    
    Checks:
    - Hash chain integrity
    - Digital signature verification
    - Merkle root validation
    - Block consistency
    """
    try:
        start_time = datetime.now()
        
        _, _, _, audit = await get_compliance_engines()
        
        # Verify audit trail
        verification_result = await audit.verify_audit_trail(start_block, end_block)
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/compliance/audit/verify",
            method="GET",
            response_time_ms=api_time,
            status_code=200
        )
        
        return {
            "verification_result": verification_result,
            "verification_timestamp": datetime.now().isoformat(),
            "processing_time_ms": api_time
        }
        
    except Exception as e:
        logger.error(f"Error verifying audit trail: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/blockchain/status")
async def get_blockchain_status():
    """Get current blockchain audit system status."""
    try:
        _, _, _, audit = await get_compliance_engines()
        
        status = await audit.get_blockchain_status()
        
        return {
            "blockchain_status": status,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting blockchain status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance/summary")
async def get_compliance_performance():
    """Get compliance system performance metrics."""
    try:
        from ...core.redis_client import performance_metrics
        
        # Get performance summary
        performance_summary = await performance_metrics.get_performance_summary(
            "compliance_performance", hours=24
        )
        
        return {
            "compliance_performance": performance_summary,
            "system_components": {
                "emir_engine": "operational",
                "dodd_frank_engine": "operational",
                "surveillance_engine": "operational",
                "blockchain_audit": "operational"
            },
            "regulatory_coverage": {
                "emir": "active",
                "dodd_frank": "active",
                "mifid2": "planned",
                "finra": "planned",
                "sox": "planned"
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting compliance performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))
