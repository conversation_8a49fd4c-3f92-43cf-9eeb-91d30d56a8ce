{"timestamp": "2025-05-25T21:42:39.079632", "test_duration_seconds": 0, "system_info": {"hostname": "DESKTOP-K6O9OV4", "platform": "Windows-11-10.0.22631-SP0", "processor": "AMD64 Family 25 Model 33 Stepping 0, AuthenticAMD", "cpu_count": 16, "memory_gb": 31.92517852783203, "python_version": "3.13.2", "timestamp": "2025-05-25T21:42:39.148871"}, "latency_results": [], "throughput_results": [{"operation": "order_processing", "duration_seconds": 5.00005578994751, "total_operations": 65535, "operations_per_second": 13106.85375386341, "peak_ops_per_second": 17328.619905225925, "target_met": false, "target_threshold": 100000}, {"operation": "risk_checks", "duration_seconds": 5.000098466873169, "total_operations": 34084, "operations_per_second": 6816.6657568474975, "peak_ops_per_second": 7174.842623347915, "target_met": false, "target_threshold": 500000}, {"operation": "market_data_updates", "duration_seconds": 5.0000083446502686, "total_operations": 471299, "operations_per_second": 94259.64268724946, "peak_ops_per_second": 100989.34990109269, "target_met": false, "target_threshold": 1000000}], "error_count": 5283, "success_rate": 100.0, "overall_grade": "Throughput Test Only"}