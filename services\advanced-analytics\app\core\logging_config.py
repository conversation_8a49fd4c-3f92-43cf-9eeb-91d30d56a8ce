"""
AthenaTrader Phase 11 Advanced Analytics Engine Logging Configuration
"""

import logging
import logging.config
import sys
from datetime import datetime
from typing import Dict, Any
import structlog
from .config import settings


def setup_logging():
    """Setup structured logging for the Advanced Analytics Engine."""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Logging configuration
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "standard": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(lineno)d - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "json": {
                "()": structlog.stdlib.ProcessorFormatter,
                "processor": structlog.dev.ConsoleRenderer(colors=False),
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "standard",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "detailed",
                "filename": "logs/advanced_analytics.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            },
            "ml_models": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/ml_models.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            },
            "backtesting": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/backtesting.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            },
            "multi_asset": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/multi_asset.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            },
            "compliance": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/compliance.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            },
            "performance": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": "INFO",
                "formatter": "json",
                "filename": "logs/performance.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            }
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["console", "file"],
                "level": settings.LOG_LEVEL,
                "propagate": False
            },
            "ml_models": {
                "handlers": ["console", "ml_models"],
                "level": "INFO",
                "propagate": False
            },
            "backtesting": {
                "handlers": ["console", "backtesting"],
                "level": "INFO",
                "propagate": False
            },
            "multi_asset": {
                "handlers": ["console", "multi_asset"],
                "level": "INFO",
                "propagate": False
            },
            "compliance": {
                "handlers": ["console", "compliance"],
                "level": "INFO",
                "propagate": False
            },
            "performance": {
                "handlers": ["console", "performance"],
                "level": "INFO",
                "propagate": False
            },
            "uvicorn": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            },
            "uvicorn.access": {
                "handlers": ["console"],
                "level": "INFO",
                "propagate": False
            }
        }
    }
    
    # Create logs directory if it doesn't exist
    import os
    os.makedirs("logs", exist_ok=True)
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)
    
    # Get the root logger
    logger = logging.getLogger(__name__)
    logger.info("Advanced Analytics Engine logging configured")


class PerformanceLogger:
    """Performance logging utility for tracking API response times and model inference."""
    
    def __init__(self):
        self.logger = logging.getLogger("performance")
    
    def log_api_performance(self, endpoint: str, method: str, response_time_ms: float, status_code: int):
        """Log API performance metrics."""
        self.logger.info(
            "API performance",
            extra={
                "endpoint": endpoint,
                "method": method,
                "response_time_ms": response_time_ms,
                "status_code": status_code,
                "timestamp": datetime.now().isoformat(),
                "target_ms": settings.API_RESPONSE_TIME_TARGET_MS,
                "target_met": response_time_ms <= settings.API_RESPONSE_TIME_TARGET_MS
            }
        )
    
    def log_model_performance(self, model_name: str, inference_time_ms: float, accuracy: float = None):
        """Log ML model performance metrics."""
        log_data = {
            "model_name": model_name,
            "inference_time_ms": inference_time_ms,
            "timestamp": datetime.now().isoformat(),
            "target_ms": settings.MODEL_INFERENCE_TIME_TARGET_MS,
            "latency_target_met": inference_time_ms <= settings.MODEL_INFERENCE_TIME_TARGET_MS
        }
        
        if accuracy is not None:
            log_data.update({
                "accuracy": accuracy,
                "accuracy_target": settings.PREDICTION_ACCURACY_TARGET,
                "accuracy_target_met": accuracy >= settings.PREDICTION_ACCURACY_TARGET
            })
        
        self.logger.info("Model performance", extra=log_data)
    
    def log_backtesting_performance(self, strategy_id: str, simulation_time_ms: float, scenarios: int):
        """Log backtesting performance metrics."""
        self.logger.info(
            "Backtesting performance",
            extra={
                "strategy_id": strategy_id,
                "simulation_time_ms": simulation_time_ms,
                "scenarios": scenarios,
                "timestamp": datetime.now().isoformat(),
                "scenarios_per_second": scenarios / (simulation_time_ms / 1000) if simulation_time_ms > 0 else 0
            }
        )
    
    def log_compliance_performance(self, check_type: str, processing_time_ms: float, records_processed: int):
        """Log compliance processing performance."""
        self.logger.info(
            "Compliance performance",
            extra={
                "check_type": check_type,
                "processing_time_ms": processing_time_ms,
                "records_processed": records_processed,
                "timestamp": datetime.now().isoformat(),
                "records_per_second": records_processed / (processing_time_ms / 1000) if processing_time_ms > 0 else 0
            }
        )


class MLModelLogger:
    """Specialized logger for ML model training and inference."""
    
    def __init__(self):
        self.logger = logging.getLogger("ml_models")
    
    def log_training_start(self, model_name: str, dataset_size: int, parameters: Dict[str, Any]):
        """Log model training start."""
        self.logger.info(
            "Model training started",
            extra={
                "model_name": model_name,
                "dataset_size": dataset_size,
                "parameters": parameters,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_training_complete(self, model_name: str, training_time_ms: float, final_loss: float, accuracy: float):
        """Log model training completion."""
        self.logger.info(
            "Model training completed",
            extra={
                "model_name": model_name,
                "training_time_ms": training_time_ms,
                "final_loss": final_loss,
                "accuracy": accuracy,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_prediction(self, model_name: str, input_features: int, prediction_confidence: float):
        """Log model prediction."""
        self.logger.info(
            "Model prediction",
            extra={
                "model_name": model_name,
                "input_features": input_features,
                "prediction_confidence": prediction_confidence,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_model_error(self, model_name: str, error_type: str, error_message: str):
        """Log model errors."""
        self.logger.error(
            "Model error",
            extra={
                "model_name": model_name,
                "error_type": error_type,
                "error_message": error_message,
                "timestamp": datetime.now().isoformat()
            }
        )


class ComplianceLogger:
    """Specialized logger for regulatory compliance activities."""
    
    def __init__(self):
        self.logger = logging.getLogger("compliance")
    
    def log_trade_surveillance(self, trade_id: str, pattern_detected: str, risk_score: float):
        """Log trade surveillance detection."""
        self.logger.info(
            "Trade surveillance",
            extra={
                "trade_id": trade_id,
                "pattern_detected": pattern_detected,
                "risk_score": risk_score,
                "timestamp": datetime.now().isoformat(),
                "requires_investigation": risk_score > 0.7
            }
        )
    
    def log_regulatory_report(self, report_type: str, regulation: str, records_count: int, status: str):
        """Log regulatory report submission."""
        self.logger.info(
            "Regulatory report",
            extra={
                "report_type": report_type,
                "regulation": regulation,
                "records_count": records_count,
                "status": status,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def log_blockchain_audit(self, transaction_hash: str, block_number: int, audit_data: Dict[str, Any]):
        """Log blockchain audit trail entry."""
        self.logger.info(
            "Blockchain audit",
            extra={
                "transaction_hash": transaction_hash,
                "block_number": block_number,
                "audit_data": audit_data,
                "timestamp": datetime.now().isoformat()
            }
        )


# Global logger instances
performance_logger = PerformanceLogger()
ml_model_logger = MLModelLogger()
compliance_logger = ComplianceLogger()
