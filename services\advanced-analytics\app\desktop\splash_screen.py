"""
AthenaTrader Desktop Application Splash Screen

Professional splash screen for application startup with progress indication.
"""

from PyQt6.QtWidgets import QSplashScreen, QProgressBar, QLabel, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QPainter, QFont, QColor
from pathlib import Path


class SplashScreen(QSplashScreen):
    """
    Professional splash screen for AthenaTrader desktop application.
    
    Displays application logo, version information, and loading progress
    during application initialization.
    """
    
    def __init__(self):
        """Initialize the splash screen."""
        # Create splash screen pixmap
        pixmap = self.create_splash_pixmap()
        super().__init__(pixmap, Qt.WindowType.WindowStaysOnTopHint)
        
        # Setup progress bar
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setGeometry(50, 350, 400, 20)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #0078d4;
                border-radius: 10px;
                text-align: center;
                background-color: #404040;
                color: white;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #0078d4;
                border-radius: 8px;
            }
        """)
        
        # Setup status label
        self.status_label = QLabel("Initializing...", self)
        self.status_label.setGeometry(50, 380, 400, 30)
        self.status_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                font-weight: bold;
                background: transparent;
            }
        """)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Set initial values
        self.progress_bar.setValue(0)
        
        # Auto-close timer (fallback)
        self.timer = QTimer()
        self.timer.timeout.connect(self.close)
        self.timer.setSingleShot(True)
        self.timer.start(10000)  # 10 seconds max
    
    def create_splash_pixmap(self) -> QPixmap:
        """Create the splash screen pixmap with AthenaTrader branding."""
        # Create a 500x400 pixmap
        pixmap = QPixmap(500, 400)
        pixmap.fill(QColor(43, 43, 43))  # Dark background
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw title
        title_font = QFont("Segoe UI", 24, QFont.Weight.Bold)
        painter.setFont(title_font)
        painter.setPen(QColor(255, 255, 255))
        painter.drawText(50, 80, "AthenaTrader")
        
        # Draw subtitle
        subtitle_font = QFont("Segoe UI", 16)
        painter.setFont(subtitle_font)
        painter.setPen(QColor(0, 120, 212))
        painter.drawText(50, 110, "Regulatory Certification")
        
        # Draw version
        version_font = QFont("Segoe UI", 12)
        painter.setFont(version_font)
        painter.setPen(QColor(200, 200, 200))
        painter.drawText(50, 140, "Version 4.0.0")
        
        # Draw description
        desc_font = QFont("Segoe UI", 10)
        painter.setFont(desc_font)
        painter.setPen(QColor(180, 180, 180))
        painter.drawText(50, 180, "Professional Desktop Application for")
        painter.drawText(50, 200, "Comprehensive Regulatory Compliance")
        
        # Draw features
        features = [
            "• EMIR & Dodd-Frank Compliance",
            "• MiFID II & FINRA Validation",
            "• GDPR/CCPA Data Protection",
            "• Blockchain Audit Trails",
            "• Real-time Compliance Scoring",
            "• Regulatory Report Generation"
        ]
        
        feature_font = QFont("Segoe UI", 9)
        painter.setFont(feature_font)
        painter.setPen(QColor(160, 160, 160))
        
        y_pos = 240
        for feature in features:
            painter.drawText(50, y_pos, feature)
            y_pos += 18
        
        # Draw footer
        footer_font = QFont("Segoe UI", 8)
        painter.setFont(footer_font)
        painter.setPen(QColor(120, 120, 120))
        painter.drawText(50, 390, "© 2024 AthenaTrader. All rights reserved.")
        
        painter.end()
        return pixmap
    
    def update_progress(self, value: int, message: str = ""):
        """Update progress bar and status message."""
        self.progress_bar.setValue(value)
        if message:
            self.status_label.setText(message)
        
        # Process events to update display
        from PyQt6.QtWidgets import QApplication
        QApplication.processEvents()
    
    def finish_loading(self):
        """Finish loading and close splash screen."""
        self.update_progress(100, "Ready!")
        self.timer.stop()
        
        # Close after a short delay
        QTimer.singleShot(500, self.close)
