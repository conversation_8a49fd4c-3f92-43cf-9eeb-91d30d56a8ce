#!/usr/bin/env python3
"""
AthenaTrader Phase 10 Windows-Compatible Deployment Validation Script

This script validates the deployment readiness for HFT capabilities in a
Windows development environment, simulating production validation checks.
"""

import os
import sys
import json
import logging
import platform
import psutil
from datetime import datetime
from typing import Dict, Any, Optional

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WindowsDeploymentValidator:
    """Windows-compatible deployment validation for HFT systems."""

    def __init__(self, enable_dpdk: bool = False, enable_fpga: bool = False):
        """Initialize Windows deployment validator."""
        self.enable_dpdk = enable_dpdk
        self.enable_fpga = enable_fpga
        self.validation_results = {}

        logger.info(f"Windows deployment validator initialized")
        logger.info(f"DPDK enabled: {enable_dpdk}, FPGA enabled: {enable_fpga}")

    def validate_system_requirements(self) -> bool:
        """Validate system requirements for HFT deployment (Windows simulation)."""
        logger.info("Validating system requirements (Windows simulation mode)...")

        validation_passed = True

        # Check Python version
        if sys.version_info < (3, 9):
            logger.error("Python 3.9+ required for HFT capabilities")
            validation_passed = False
            self.validation_results['python_version'] = False
        else:
            logger.info(f"Python version check passed: {platform.python_version()}")
            self.validation_results['python_version'] = True

        # Check available memory (Windows compatible)
        try:
            memory_info = psutil.virtual_memory()
            memory_gb = memory_info.total / (1024**3)

            if memory_gb < 16:  # Adjusted for development environment
                logger.warning(f"Recommended 64GB+ memory, found {memory_gb:.1f}GB")
                self.validation_results['memory_sufficient'] = False
            else:
                logger.info(f"Memory check passed: {memory_gb:.1f}GB available (simulating 128GB production)")
                self.validation_results['memory_sufficient'] = True
        except Exception as e:
            logger.warning(f"Could not check memory: {e}")
            self.validation_results['memory_sufficient'] = False

        # Check CPU features (Windows compatible)
        try:
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()

            if cpu_count < 8:
                logger.warning(f"Recommended 16+ CPU cores, found {cpu_count}")
                self.validation_results['cpu_sufficient'] = False
            else:
                logger.info(f"CPU check passed: {cpu_count} cores available")
                self.validation_results['cpu_sufficient'] = True

            if cpu_freq and cpu_freq.max < 2000:
                logger.warning(f"Low CPU frequency: {cpu_freq.max}MHz")
            else:
                logger.info(f"CPU frequency check passed: {cpu_freq.max if cpu_freq else 'Unknown'}MHz")

        except Exception as e:
            logger.warning(f"Could not check CPU features: {e}")
            self.validation_results['cpu_sufficient'] = False

        # Simulate DPDK validation
        if self.enable_dpdk:
            logger.info("Simulating DPDK validation...")
            # In Windows simulation, we assume DPDK requirements are met
            self.validation_results['dpdk_ready'] = True
            logger.info("DPDK validation passed (simulated)")
        else:
            self.validation_results['dpdk_ready'] = True
            logger.info("DPDK disabled - validation skipped")

        # Simulate FPGA validation
        if self.enable_fpga:
            logger.info("Simulating FPGA validation...")
            # In Windows simulation, we assume FPGA requirements are met
            self.validation_results['fpga_ready'] = True
            logger.info("FPGA validation passed (simulated)")
        else:
            self.validation_results['fpga_ready'] = True
            logger.info("FPGA disabled - validation skipped")

        # Check disk space
        try:
            disk_usage = psutil.disk_usage('.')
            disk_free_gb = disk_usage.free / (1024**3)

            if disk_free_gb < 10:
                logger.warning(f"Low disk space: {disk_free_gb:.1f}GB free")
                self.validation_results['disk_space'] = False
            else:
                logger.info(f"Disk space check passed: {disk_free_gb:.1f}GB free")
                self.validation_results['disk_space'] = True
        except Exception as e:
            logger.warning(f"Could not check disk space: {e}")
            self.validation_results['disk_space'] = False

        # Overall validation
        critical_checks = [
            self.validation_results.get('python_version', False),
            self.validation_results.get('memory_sufficient', False),
            self.validation_results.get('cpu_sufficient', False),
            self.validation_results.get('dpdk_ready', False),
            self.validation_results.get('fpga_ready', False),
            self.validation_results.get('disk_space', False)
        ]

        overall_passed = all(critical_checks)
        self.validation_results['overall_validation'] = overall_passed

        if overall_passed:
            logger.info("✓ System validation passed - HFT deployment ready")
        else:
            logger.error("✗ System validation failed - HFT deployment not ready")

        return overall_passed

    def generate_validation_report(self) -> str:
        """Generate validation report."""
        report = []
        report.append("=" * 80)
        report.append("ATHENATRADER PHASE 10 HFT DEPLOYMENT VALIDATION REPORT")
        report.append("(WINDOWS SIMULATION MODE)")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().isoformat()}")
        report.append(f"Platform: {platform.platform()}")
        report.append(f"Hostname: {platform.node()}")
        report.append("")

        # System Information
        report.append("SYSTEM INFORMATION:")
        report.append(f"  OS: {platform.system()} {platform.release()}")
        report.append(f"  Python: {platform.python_version()}")
        report.append(f"  CPU Cores: {psutil.cpu_count()}")

        memory_info = psutil.virtual_memory()
        report.append(f"  Memory: {memory_info.total / (1024**3):.1f}GB")

        disk_usage = psutil.disk_usage('.')
        report.append(f"  Disk Free: {disk_usage.free / (1024**3):.1f}GB")
        report.append("")

        # Validation Results
        report.append("VALIDATION RESULTS:")
        for check, passed in self.validation_results.items():
            if check != 'overall_validation':
                status = "✓ PASS" if passed else "✗ FAIL"
                check_name = check.replace('_', ' ').title()
                report.append(f"  {check_name:<20} {status}")

        report.append("")

        # Overall Status
        overall_status = "✓ READY" if self.validation_results.get('overall_validation', False) else "✗ NOT READY"
        report.append(f"OVERALL STATUS: {overall_status}")

        # Configuration Summary
        report.append("")
        report.append("CONFIGURATION:")
        report.append(f"  DPDK Enabled: {self.enable_dpdk}")
        report.append(f"  FPGA Enabled: {self.enable_fpga}")
        report.append(f"  Simulation Mode: True")

        return "\n".join(report)

    def save_validation_results(self, output_file: str = "deployment_validation_results.json"):
        """Save validation results to file."""
        results = {
            'timestamp': datetime.now().isoformat(),
            'platform': platform.platform(),
            'hostname': platform.node(),
            'dpdk_enabled': self.enable_dpdk,
            'fpga_enabled': self.enable_fpga,
            'simulation_mode': True,
            'validation_results': self.validation_results
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)

        logger.info(f"Validation results saved to {output_file}")


def main():
    """Main validation function."""
    import argparse

    parser = argparse.ArgumentParser(description="Validate AthenaTrader Phase 10 HFT deployment (Windows)")
    parser.add_argument("--enable-dpdk", action="store_true",
                       help="Enable DPDK validation")
    parser.add_argument("--enable-fpga", action="store_true",
                       help="Enable FPGA validation")
    parser.add_argument("--output", default="deployment_validation_results.json",
                       help="Output file for validation results")
    parser.add_argument("--report", action="store_true",
                       help="Generate validation report")

    args = parser.parse_args()

    # Create validator
    validator = WindowsDeploymentValidator(
        enable_dpdk=args.enable_dpdk,
        enable_fpga=args.enable_fpga
    )

    # Run validation
    validation_passed = validator.validate_system_requirements()

    # Save results
    validator.save_validation_results(args.output)

    # Generate report if requested
    if args.report:
        report = validator.generate_validation_report()
        print(report)

        # Save report to file
        report_file = args.output.replace('.json', '_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"Validation report saved to {report_file}")

    # Exit with appropriate code
    if validation_passed:
        logger.info("Deployment validation successful")
        sys.exit(0)
    else:
        logger.error("Deployment validation failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
