"""Create compliance tables for regulatory certification

Revision ID: 001_compliance_tables
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001_compliance_tables'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Create compliance tables for regulatory certification service."""
    
    # EMIR Transactions table
    op.create_table('emir_transactions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('trade_id', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('counterparty_id', sa.String(255), nullable=False),
        sa.Column('instrument_type', sa.String(50), nullable=False),
        sa.Column('notional_amount', sa.Numeric(20, 8), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False),
        sa.Column('execution_timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('maturity_date', sa.DateTime(timezone=True)),
        sa.Column('underlying_asset', sa.String(255), nullable=False),
        sa.Column('clearing_status', sa.String(50), nullable=False),
        sa.Column('compliance_status', sa.String(50), nullable=False, default='PENDING'),
        sa.Column('risk_score', sa.Numeric(5, 4), default=0.0),
        sa.Column('requires_reporting', sa.Boolean, default=False),
        sa.Column('reporting_deadline', sa.DateTime(timezone=True)),
        sa.Column('lei_validated', sa.Boolean, default=False),
        sa.Column('margin_requirements', postgresql.JSONB),
        sa.Column('violations', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('validated_by', sa.String(255)),
        sa.Column('validation_timestamp', sa.DateTime(timezone=True))
    )
    
    # EMIR Reports table
    op.create_table('emir_reports',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('report_id', sa.String(255), nullable=False, unique=True),
        sa.Column('trade_id', sa.String(255), sa.ForeignKey('emir_transactions.trade_id'), nullable=False),
        sa.Column('report_type', sa.String(50), nullable=False),
        sa.Column('trade_repository', sa.String(100), nullable=False),
        sa.Column('submission_timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('acknowledgment_received', sa.Boolean, default=False),
        sa.Column('acknowledgment_timestamp', sa.DateTime(timezone=True)),
        sa.Column('status', sa.String(50), default='SUBMITTED')
    )
    
    # Dodd-Frank Transactions table
    op.create_table('dodd_frank_transactions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('trade_id', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('entity_id', sa.String(255), nullable=False),
        sa.Column('instrument_type', sa.String(50), nullable=False),
        sa.Column('notional_amount', sa.Numeric(20, 8), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False),
        sa.Column('execution_timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('trading_desk', sa.String(255), nullable=False),
        sa.Column('customer_facing', sa.Boolean, default=False),
        sa.Column('hedging_purpose', sa.String(255)),
        sa.Column('compliance_status', sa.String(50), nullable=False, default='PENDING'),
        sa.Column('volcker_rule_status', sa.String(50)),
        sa.Column('position_limits_status', sa.String(50)),
        sa.Column('swap_dealer_status', sa.String(50)),
        sa.Column('risk_score', sa.Numeric(5, 4), default=0.0),
        sa.Column('violations', postgresql.JSONB),
        sa.Column('capital_requirements', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # Volcker Assessments table
    op.create_table('volcker_assessments',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('trade_id', sa.String(255), sa.ForeignKey('dodd_frank_transactions.trade_id'), nullable=False),
        sa.Column('entity_id', sa.String(255), nullable=False),
        sa.Column('assessment_status', sa.String(50), nullable=False),
        sa.Column('exemption_applied', sa.String(100)),
        sa.Column('exemption_justification', sa.Text),
        sa.Column('risk_metrics', postgresql.JSONB),
        sa.Column('documentation_requirements', postgresql.JSONB),
        sa.Column('assessed_at', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('assessed_by', sa.String(255))
    )
    
    # MiFID Transactions table
    op.create_table('mifid_transactions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('trade_id', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('client_id', sa.String(255), nullable=False),
        sa.Column('instrument_id', sa.String(255), nullable=False),
        sa.Column('instrument_type', sa.String(50), nullable=False),
        sa.Column('quantity', sa.Numeric(20, 8), nullable=False),
        sa.Column('price', sa.Numeric(20, 8), nullable=False),
        sa.Column('currency', sa.String(3), nullable=False),
        sa.Column('execution_timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('venue', sa.String(100), nullable=False),
        sa.Column('client_category', sa.String(50), nullable=False),
        sa.Column('order_type', sa.String(50), nullable=False),
        sa.Column('compliance_status', sa.String(50), nullable=False, default='PENDING'),
        sa.Column('transaction_reporting_status', sa.String(50)),
        sa.Column('best_execution_status', sa.String(50)),
        sa.Column('market_abuse_status', sa.String(50)),
        sa.Column('client_protection_status', sa.String(50)),
        sa.Column('risk_score', sa.Numeric(5, 4), default=0.0),
        sa.Column('reporting_deadline', sa.DateTime(timezone=True)),
        sa.Column('violations', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # FINRA Transactions table
    op.create_table('finra_transactions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('trade_id', sa.String(255), nullable=False, unique=True, index=True),
        sa.Column('customer_id', sa.String(255), nullable=False),
        sa.Column('security_id', sa.String(255), nullable=False),
        sa.Column('security_type', sa.String(50), nullable=False),
        sa.Column('quantity', sa.Numeric(20, 8), nullable=False),
        sa.Column('price', sa.Numeric(20, 8), nullable=False),
        sa.Column('side', sa.String(10), nullable=False),
        sa.Column('execution_timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('account_type', sa.String(50), nullable=False),
        sa.Column('broker_dealer_id', sa.String(255), nullable=False),
        sa.Column('compliance_status', sa.String(50), nullable=False, default='PENDING'),
        sa.Column('surveillance_status', sa.String(50)),
        sa.Column('net_capital_status', sa.String(50)),
        sa.Column('customer_protection_status', sa.String(50)),
        sa.Column('aml_status', sa.String(50)),
        sa.Column('risk_score', sa.Numeric(5, 4), default=0.0),
        sa.Column('surveillance_alerts', postgresql.JSONB),
        sa.Column('violations', postgresql.JSONB),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # Data Subject Requests table (GDPR/CCPA)
    op.create_table('data_subject_requests',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('request_id', sa.String(255), nullable=False, unique=True),
        sa.Column('data_subject_id', sa.String(255), nullable=False),
        sa.Column('request_type', sa.String(50), nullable=False),
        sa.Column('jurisdiction', sa.String(10), nullable=False),
        sa.Column('request_details', postgresql.JSONB),
        sa.Column('verification_method', sa.String(100), nullable=False),
        sa.Column('processing_status', sa.String(50), default='PENDING'),
        sa.Column('response_deadline', sa.DateTime(timezone=True), nullable=False),
        sa.Column('actions_taken', postgresql.JSONB),
        sa.Column('data_provided', postgresql.JSONB),
        sa.Column('completion_timestamp', sa.DateTime(timezone=True)),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('processed_by', sa.String(255))
    )
    
    # Consent Records table
    op.create_table('consent_records',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('consent_id', sa.String(255), nullable=False, unique=True),
        sa.Column('data_subject_id', sa.String(255), nullable=False),
        sa.Column('processing_purpose', sa.String(255), nullable=False),
        sa.Column('data_categories', postgresql.JSONB, nullable=False),
        sa.Column('consent_given', sa.Boolean, nullable=False),
        sa.Column('lawful_basis', sa.String(50), nullable=False),
        sa.Column('retention_period_days', sa.Integer),
        sa.Column('consent_timestamp', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('withdrawal_timestamp', sa.DateTime(timezone=True)),
        sa.Column('expiry_timestamp', sa.DateTime(timezone=True)),
        sa.Column('status', sa.String(50), default='ACTIVE'),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True), default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # Audit Records table (Blockchain)
    op.create_table('audit_records',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('record_id', sa.String(255), nullable=False, unique=True),
        sa.Column('event_type', sa.String(50), nullable=False),
        sa.Column('entity_id', sa.String(255), nullable=False),
        sa.Column('user_id', sa.String(255), nullable=False),
        sa.Column('event_data', postgresql.JSONB, nullable=False),
        sa.Column('metadata', postgresql.JSONB),
        sa.Column('compliance_framework', sa.String(50)),
        sa.Column('transaction_hash', sa.String(255), nullable=False),
        sa.Column('block_number', sa.Integer, nullable=False),
        sa.Column('block_hash', sa.String(255), nullable=False),
        sa.Column('previous_hash', sa.String(255)),
        sa.Column('digital_signature', sa.Text, nullable=False),
        sa.Column('verification_status', sa.String(50), default='VERIFIED'),
        sa.Column('timestamp', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now())
    )
    
    # Blockchain Transactions table
    op.create_table('blockchain_transactions',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('transaction_hash', sa.String(255), nullable=False, unique=True),
        sa.Column('block_number', sa.Integer, nullable=False),
        sa.Column('block_hash', sa.String(255), nullable=False),
        sa.Column('transaction_data', postgresql.JSONB, nullable=False),
        sa.Column('gas_used', sa.Integer),
        sa.Column('gas_price', sa.Numeric(20, 8)),
        sa.Column('network', sa.String(50), default='hyperledger'),
        sa.Column('consensus_algorithm', sa.String(50), default='PBFT'),
        sa.Column('confirmation_count', sa.Integer, default=0),
        sa.Column('finalized', sa.Boolean, default=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), default=sa.func.now()),
        sa.Column('created_at', sa.DateTime(timezone=True), default=sa.func.now())
    )
    
    # Create indexes for performance
    op.create_index('idx_emir_transactions_execution_timestamp', 'emir_transactions', ['execution_timestamp'])
    op.create_index('idx_emir_transactions_compliance_status', 'emir_transactions', ['compliance_status'])
    op.create_index('idx_dodd_frank_transactions_execution_timestamp', 'dodd_frank_transactions', ['execution_timestamp'])
    op.create_index('idx_mifid_transactions_execution_timestamp', 'mifid_transactions', ['execution_timestamp'])
    op.create_index('idx_finra_transactions_execution_timestamp', 'finra_transactions', ['execution_timestamp'])
    op.create_index('idx_data_subject_requests_processing_status', 'data_subject_requests', ['processing_status'])
    op.create_index('idx_audit_records_event_type', 'audit_records', ['event_type'])
    op.create_index('idx_audit_records_timestamp', 'audit_records', ['timestamp'])
    op.create_index('idx_blockchain_transactions_block_number', 'blockchain_transactions', ['block_number'])


def downgrade():
    """Drop compliance tables."""
    
    # Drop indexes
    op.drop_index('idx_blockchain_transactions_block_number')
    op.drop_index('idx_audit_records_timestamp')
    op.drop_index('idx_audit_records_event_type')
    op.drop_index('idx_data_subject_requests_processing_status')
    op.drop_index('idx_finra_transactions_execution_timestamp')
    op.drop_index('idx_mifid_transactions_execution_timestamp')
    op.drop_index('idx_dodd_frank_transactions_execution_timestamp')
    op.drop_index('idx_emir_transactions_compliance_status')
    op.drop_index('idx_emir_transactions_execution_timestamp')
    
    # Drop tables
    op.drop_table('blockchain_transactions')
    op.drop_table('audit_records')
    op.drop_table('consent_records')
    op.drop_table('data_subject_requests')
    op.drop_table('finra_transactions')
    op.drop_table('mifid_transactions')
    op.drop_table('volcker_assessments')
    op.drop_table('dodd_frank_transactions')
    op.drop_table('emir_reports')
    op.drop_table('emir_transactions')
