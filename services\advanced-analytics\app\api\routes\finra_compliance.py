"""
AthenaTrader Phase 4: FINRA Compliance Module

Financial Industry Regulatory Authority (FINRA) compliance implementation
for US securities trading and broker-dealer operations.

Key Features:
- Trade surveillance for market manipulation detection
- Net capital requirements monitoring for broker-dealers
- Customer protection rule compliance (15c3-3)
- Anti-money laundering (AML) and know your customer (KYC) frameworks
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_db
from ...core.logging_config import get_logger
from ...compliance.finra_engine import FINRAComplianceEngine, TradeSurveillanceEngine, AMLEngine
from ...models.compliance import FINRATransaction, SurveillanceAlert, ComplianceStatus

# Setup logging with Winston-style configuration
logger = get_logger(__name__)
compliance_logger = get_logger("compliance.finra")

# Create router
router = APIRouter()

# Initialize FINRA engines
finra_compliance = FINRAComplianceEngine()
trade_surveillance = TradeSurveillanceEngine()
aml_engine = AMLEngine()


class FINRATransactionRequest(BaseModel):
    """FINRA transaction validation request."""
    
    trade_id: str = Field(..., description="Unique trade identifier")
    customer_id: str = Field(..., description="Customer identifier")
    security_id: str = Field(..., description="Security identifier")
    security_type: str = Field(..., description="Security type")
    quantity: Decimal = Field(..., description="Trade quantity")
    price: Decimal = Field(..., description="Execution price")
    side: str = Field(..., description="Buy or Sell")
    execution_timestamp: datetime = Field(..., description="Execution timestamp")
    account_type: str = Field(..., description="Account type")
    broker_dealer_id: str = Field(..., description="Broker-dealer identifier")
    
    @validator('quantity')
    def validate_quantity(cls, v):
        """Validate quantity is positive."""
        if v <= 0:
            raise ValueError("Quantity must be positive")
        return v
    
    @validator('price')
    def validate_price(cls, v):
        """Validate price is positive."""
        if v <= 0:
            raise ValueError("Price must be positive")
        return v
    
    @validator('side')
    def validate_side(cls, v):
        """Validate trade side."""
        if v.upper() not in ['BUY', 'SELL']:
            raise ValueError("Side must be BUY or SELL")
        return v.upper()
    
    @validator('security_type')
    def validate_security_type(cls, v):
        """Validate security type."""
        allowed_types = ['EQUITY', 'BOND', 'OPTION', 'MUTUAL_FUND', 'ETF']
        if v.upper() not in allowed_types:
            raise ValueError(f"Security type must be one of: {allowed_types}")
        return v.upper()


class FINRAComplianceResponse(BaseModel):
    """FINRA compliance validation response."""
    
    trade_id: str
    compliance_status: str
    surveillance_status: str
    net_capital_status: str
    customer_protection_status: str
    aml_status: str
    risk_score: float
    violations: List[str]
    recommendations: List[str]
    surveillance_alerts: List[Dict[str, Any]]
    timestamp: datetime


@router.post("/validate", response_model=FINRAComplianceResponse)
async def validate_finra_compliance(
    transaction: FINRATransactionRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Validate FINRA compliance for a securities transaction.
    
    Performs comprehensive FINRA compliance checks including:
    - Trade surveillance for market manipulation
    - Net capital requirements monitoring
    - Customer protection rule compliance
    - AML/KYC validation
    """
    try:
        # Log compliance validation start
        compliance_logger.info(
            f"Starting FINRA compliance validation - Trade ID: {transaction.trade_id}, "
            f"Customer: {transaction.customer_id}, "
            f"Security: {transaction.security_id}, "
            f"Broker-Dealer: {transaction.broker_dealer_id}"
        )
        
        # Perform trade surveillance
        surveillance_result = await trade_surveillance.analyze_transaction(
            trade_id=transaction.trade_id,
            customer_id=transaction.customer_id,
            security_id=transaction.security_id,
            quantity=transaction.quantity,
            price=transaction.price,
            side=transaction.side,
            execution_timestamp=transaction.execution_timestamp
        )
        
        # Check net capital requirements
        net_capital_status = await finra_compliance.check_net_capital_requirements(
            broker_dealer_id=transaction.broker_dealer_id,
            trade_value=transaction.quantity * transaction.price
        )
        
        # Validate customer protection rule compliance
        customer_protection_status = await finra_compliance.validate_customer_protection(
            customer_id=transaction.customer_id,
            account_type=transaction.account_type,
            trade_value=transaction.quantity * transaction.price,
            security_type=transaction.security_type
        )
        
        # Perform AML screening
        aml_result = await aml_engine.screen_transaction(
            customer_id=transaction.customer_id,
            trade_value=transaction.quantity * transaction.price,
            counterparty_info={"broker_dealer_id": transaction.broker_dealer_id}
        )
        
        # Calculate overall risk score
        risk_score = await finra_compliance.calculate_risk_score(
            surveillance_result, net_capital_status, customer_protection_status, aml_result
        )
        
        # Compile violations and recommendations
        violations = []
        recommendations = []
        surveillance_alerts = []
        
        if surveillance_result['alerts']:
            surveillance_alerts = surveillance_result['alerts']
            for alert in surveillance_result['alerts']:
                if alert['severity'] == 'HIGH':
                    violations.append(f"Market manipulation pattern detected: {alert['pattern']}")
                    recommendations.append(f"Review trading pattern: {alert['recommendation']}")
        
        if net_capital_status['status'] == 'VIOLATION':
            violations.extend(net_capital_status['violations'])
            recommendations.extend(net_capital_status['recommendations'])
        
        if customer_protection_status['status'] == 'VIOLATION':
            violations.extend(customer_protection_status['violations'])
            recommendations.extend(customer_protection_status['recommendations'])
        
        if aml_result['risk_level'] == 'HIGH':
            violations.append("High AML risk detected")
            recommendations.append("Enhanced due diligence required")
        
        # Determine overall compliance status
        overall_status = "COMPLIANT"
        if violations:
            overall_status = "NON_COMPLIANT"
        elif risk_score > 0.7:
            overall_status = "HIGH_RISK"
        
        # Log compliance validation result
        compliance_logger.info(
            f"FINRA compliance validation completed - Trade ID: {transaction.trade_id}, "
            f"Status: {overall_status}, "
            f"Risk Score: {risk_score}, "
            f"Surveillance Alerts: {len(surveillance_alerts)}"
        )
        
        return FINRAComplianceResponse(
            trade_id=transaction.trade_id,
            compliance_status=overall_status,
            surveillance_status=surveillance_result['status'],
            net_capital_status=net_capital_status['status'],
            customer_protection_status=customer_protection_status['status'],
            aml_status=aml_result['status'],
            risk_score=risk_score,
            violations=violations,
            recommendations=recommendations,
            surveillance_alerts=surveillance_alerts,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"FINRA compliance validation failed for trade {transaction.trade_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"FINRA compliance validation failed: {str(e)}"
        )


@router.post("/surveillance-alert")
async def create_surveillance_alert(
    alert_data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """
    Create a trade surveillance alert.
    
    Generates alerts for potential market manipulation patterns
    detected by FINRA surveillance systems.
    """
    try:
        # Log alert creation start
        compliance_logger.info(
            f"Creating FINRA surveillance alert - Pattern: {alert_data.get('pattern')}, "
            f"Severity: {alert_data.get('severity')}"
        )
        
        # Create surveillance alert
        alert = await trade_surveillance.create_alert(alert_data)
        
        # Log alert creation
        compliance_logger.info(
            f"FINRA surveillance alert created - Alert ID: {alert['alert_id']}, "
            f"Pattern: {alert['pattern']}"
        )
        
        return {
            "alert_id": alert['alert_id'],
            "pattern": alert['pattern'],
            "severity": alert['severity'],
            "status": "CREATED",
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Surveillance alert creation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Surveillance alert creation failed: {str(e)}"
        )


@router.get("/net-capital/{broker_dealer_id}")
async def get_net_capital_status(
    broker_dealer_id: str,
    calculation_date: Optional[datetime] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Get net capital requirements status for a broker-dealer.
    
    Monitors compliance with FINRA Rule 15c3-1 net capital requirements.
    """
    try:
        if calculation_date is None:
            calculation_date = datetime.now()
        
        # Get net capital status
        net_capital = await finra_compliance.get_net_capital_status(
            broker_dealer_id=broker_dealer_id,
            calculation_date=calculation_date
        )
        
        return {
            "broker_dealer_id": broker_dealer_id,
            "calculation_date": calculation_date,
            "net_capital_status": net_capital,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"Net capital status retrieval failed for {broker_dealer_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Net capital status retrieval failed: {str(e)}"
        )


@router.get("/aml-screening/{customer_id}")
async def get_aml_screening_results(
    customer_id: str,
    screening_date: Optional[datetime] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Get AML screening results for a customer.
    
    Retrieves anti-money laundering screening results and
    risk assessments for customer due diligence.
    """
    try:
        if screening_date is None:
            screening_date = datetime.now()
        
        # Get AML screening results
        aml_results = await aml_engine.get_screening_results(
            customer_id=customer_id,
            screening_date=screening_date
        )
        
        return {
            "customer_id": customer_id,
            "screening_date": screening_date,
            "aml_results": aml_results,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"AML screening results retrieval failed for {customer_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"AML screening results retrieval failed: {str(e)}"
        )


@router.get("/health")
async def finra_health_check():
    """FINRA compliance module health check."""
    try:
        # Check FINRA engine status
        engine_status = await finra_compliance.health_check()
        
        # Check trade surveillance status
        surveillance_status = await trade_surveillance.health_check()
        
        # Check AML engine status
        aml_status = await aml_engine.health_check()
        
        return {
            "status": "healthy",
            "module": "FINRA Compliance",
            "engine_status": engine_status,
            "surveillance_status": surveillance_status,
            "aml_status": aml_status,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"FINRA health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"FINRA compliance module unhealthy: {str(e)}"
        )
