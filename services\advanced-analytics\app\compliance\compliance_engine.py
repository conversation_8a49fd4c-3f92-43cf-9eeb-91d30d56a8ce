"""
AthenaTrader Phase 11 Advanced Analytics Engine - Advanced Regulatory Compliance

PRIORITY 4: Advanced Regulatory Compliance Modules
- EMIR (European Market Infrastructure Regulation) compliance engine
- Dodd-Frank compliance framework
- Real-time trade surveillance system
- Blockchain-based audit trail system
"""

import asyncio
import logging
import hashlib
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from ..core.config import settings, ComplianceConfig
from ..core.database import DatabaseManager
from ..core.logging_config import compliance_logger

logger = logging.getLogger("compliance")


class RegulationType(Enum):
    """Types of regulatory frameworks."""
    EMIR = "emir"
    DODD_FRANK = "dodd_frank"
    MIFID2 = "mifid2"
    FINRA = "finra"
    SOX = "sox"
    GDPR = "gdpr"


class ComplianceStatus(Enum):
    """Compliance check status."""
    COMPLIANT = "compliant"
    VIOLATION = "violation"
    PENDING = "pending"
    EXEMPT = "exempt"
    UNKNOWN = "unknown"


class SurveillancePattern(Enum):
    """Trade surveillance patterns."""
    LAYERING = "layering"
    SPOOFING = "spoofing"
    WASH_TRADING = "wash_trading"
    MOMENTUM_IGNITION = "momentum_ignition"
    QUOTE_STUFFING = "quote_stuffing"
    PINGING = "pinging"
    SMOKING = "smoking"


@dataclass
class TradeRecord:
    """Trade record for compliance analysis."""
    trade_id: str
    timestamp: datetime
    symbol: str
    side: str  # buy/sell
    quantity: float
    price: float
    counterparty: str
    trader_id: str
    strategy_id: str
    venue: str
    order_type: str
    execution_algo: Optional[str] = None
    parent_order_id: Optional[str] = None


@dataclass
class ComplianceCheck:
    """Compliance check result."""
    check_id: str
    trade_id: str
    regulation_type: RegulationType
    check_type: str
    status: ComplianceStatus
    risk_score: float
    details: Dict[str, Any]
    timestamp: datetime
    requires_reporting: bool
    remediation_required: bool


@dataclass
class SurveillanceAlert:
    """Trade surveillance alert."""
    alert_id: str
    pattern_type: SurveillancePattern
    trades_involved: List[str]
    risk_score: float
    confidence: float
    description: str
    timestamp: datetime
    investigation_required: bool
    regulatory_impact: List[RegulationType]


class EMIRComplianceEngine:
    """EMIR (European Market Infrastructure Regulation) compliance engine."""
    
    def __init__(self):
        self.reporting_threshold = 1000000  # €1M threshold for derivatives
        self.required_fields = ComplianceConfig.EMIR_REQUIRED_FIELDS
    
    async def check_emir_compliance(self, trade: TradeRecord) -> ComplianceCheck:
        """Check EMIR compliance for a trade."""
        try:
            check_id = f"emir_{trade.trade_id}_{datetime.now().timestamp()}"
            
            # EMIR applies to derivatives trades
            is_derivative = await self._is_derivative_trade(trade)
            
            if not is_derivative:
                return ComplianceCheck(
                    check_id=check_id,
                    trade_id=trade.trade_id,
                    regulation_type=RegulationType.EMIR,
                    check_type="derivative_classification",
                    status=ComplianceStatus.EXEMPT,
                    risk_score=0.0,
                    details={"reason": "Not a derivative trade"},
                    timestamp=datetime.now(),
                    requires_reporting=False,
                    remediation_required=False
                )
            
            # Check reporting requirements
            notional_value = trade.quantity * trade.price
            requires_reporting = notional_value >= self.reporting_threshold
            
            # Validate required fields
            missing_fields = await self._validate_emir_fields(trade)
            
            # Calculate risk score
            risk_score = 0.0
            details = {}
            
            if missing_fields:
                risk_score += 0.5
                details["missing_fields"] = missing_fields
            
            if requires_reporting and not await self._has_lei_identifier(trade.counterparty):
                risk_score += 0.3
                details["lei_missing"] = True
            
            # Check timing requirements (T+1 reporting)
            reporting_deadline = trade.timestamp + timedelta(days=1)
            if datetime.now() > reporting_deadline and requires_reporting:
                risk_score += 0.4
                details["late_reporting"] = True
            
            # Determine compliance status
            if risk_score == 0.0:
                status = ComplianceStatus.COMPLIANT
            elif risk_score < 0.5:
                status = ComplianceStatus.PENDING
            else:
                status = ComplianceStatus.VIOLATION
            
            return ComplianceCheck(
                check_id=check_id,
                trade_id=trade.trade_id,
                regulation_type=RegulationType.EMIR,
                check_type="emir_reporting",
                status=status,
                risk_score=risk_score,
                details=details,
                timestamp=datetime.now(),
                requires_reporting=requires_reporting,
                remediation_required=status == ComplianceStatus.VIOLATION
            )
            
        except Exception as e:
            logger.error(f"Error in EMIR compliance check: {e}")
            return self._get_error_compliance_check(trade.trade_id, RegulationType.EMIR, str(e))
    
    async def _is_derivative_trade(self, trade: TradeRecord) -> bool:
        """Check if trade is a derivative."""
        # Simplified check - in production, use comprehensive instrument classification
        derivative_keywords = ['swap', 'option', 'future', 'forward', 'cfd']
        return any(keyword in trade.symbol.lower() for keyword in derivative_keywords)
    
    async def _validate_emir_fields(self, trade: TradeRecord) -> List[str]:
        """Validate required EMIR fields."""
        missing_fields = []
        
        # Check required fields from trade record
        if not trade.counterparty:
            missing_fields.append("counterparty")
        
        if not trade.venue:
            missing_fields.append("venue")
        
        # Additional EMIR-specific fields would be checked here
        # For demo, simulate some missing fields
        if np.random.random() < 0.1:  # 10% chance of missing underlying asset
            missing_fields.append("underlying_asset")
        
        return missing_fields
    
    async def _has_lei_identifier(self, counterparty: str) -> bool:
        """Check if counterparty has valid LEI identifier."""
        # Simplified check - in production, validate against LEI database
        return len(counterparty) == 20 and counterparty.isalnum()


class DoddFrankComplianceEngine:
    """Dodd-Frank compliance framework."""
    
    def __init__(self):
        self.swap_dealer_threshold = ComplianceConfig.DODD_FRANK_SWAP_THRESHOLD_USD
        self.reporting_delay_hours = ComplianceConfig.DODD_FRANK_REPORTING_DELAY_HOURS
    
    async def check_dodd_frank_compliance(self, trade: TradeRecord) -> ComplianceCheck:
        """Check Dodd-Frank compliance for a trade."""
        try:
            check_id = f"dodd_frank_{trade.trade_id}_{datetime.now().timestamp()}"
            
            # Check if trade falls under Dodd-Frank
            is_swap = await self._is_swap_trade(trade)
            
            if not is_swap:
                return ComplianceCheck(
                    check_id=check_id,
                    trade_id=trade.trade_id,
                    regulation_type=RegulationType.DODD_FRANK,
                    check_type="swap_classification",
                    status=ComplianceStatus.EXEMPT,
                    risk_score=0.0,
                    details={"reason": "Not a swap trade"},
                    timestamp=datetime.now(),
                    requires_reporting=False,
                    remediation_required=False
                )
            
            # Check swap dealer registration requirements
            annual_volume = await self._get_annual_swap_volume(trade.trader_id)
            requires_sd_registration = annual_volume >= self.swap_dealer_threshold
            
            # Check reporting timing
            hours_since_trade = (datetime.now() - trade.timestamp).total_seconds() / 3600
            late_reporting = hours_since_trade > self.reporting_delay_hours
            
            # Check margin requirements
            margin_compliant = await self._check_margin_requirements(trade)
            
            # Calculate risk score
            risk_score = 0.0
            details = {}
            
            if requires_sd_registration and not await self._is_registered_swap_dealer(trade.trader_id):
                risk_score += 0.6
                details["unregistered_swap_dealer"] = True
            
            if late_reporting:
                risk_score += 0.3
                details["late_reporting"] = hours_since_trade
            
            if not margin_compliant:
                risk_score += 0.4
                details["margin_violation"] = True
            
            # Determine compliance status
            if risk_score == 0.0:
                status = ComplianceStatus.COMPLIANT
            elif risk_score < 0.5:
                status = ComplianceStatus.PENDING
            else:
                status = ComplianceStatus.VIOLATION
            
            return ComplianceCheck(
                check_id=check_id,
                trade_id=trade.trade_id,
                regulation_type=RegulationType.DODD_FRANK,
                check_type="dodd_frank_swap",
                status=status,
                risk_score=risk_score,
                details=details,
                timestamp=datetime.now(),
                requires_reporting=True,
                remediation_required=status == ComplianceStatus.VIOLATION
            )
            
        except Exception as e:
            logger.error(f"Error in Dodd-Frank compliance check: {e}")
            return self._get_error_compliance_check(trade.trade_id, RegulationType.DODD_FRANK, str(e))
    
    async def _is_swap_trade(self, trade: TradeRecord) -> bool:
        """Check if trade is a swap."""
        swap_keywords = ['swap', 'cds', 'irs', 'fx_swap']
        return any(keyword in trade.symbol.lower() for keyword in swap_keywords)
    
    async def _get_annual_swap_volume(self, trader_id: str) -> float:
        """Get annual swap volume for trader."""
        # Simulate annual volume calculation
        return np.random.uniform(5_000_000_000, 15_000_000_000)  # $5B-$15B
    
    async def _is_registered_swap_dealer(self, trader_id: str) -> bool:
        """Check if trader is registered as swap dealer."""
        # Simulate registration check
        return np.random.random() > 0.1  # 90% are registered
    
    async def _check_margin_requirements(self, trade: TradeRecord) -> bool:
        """Check margin requirements compliance."""
        # Simulate margin check
        return np.random.random() > 0.05  # 95% compliant


class TradeSurveillanceEngine:
    """Real-time trade surveillance system for market manipulation detection."""
    
    def __init__(self):
        self.surveillance_patterns = ComplianceConfig.SURVEILLANCE_PATTERNS
        self.detection_algorithms = {
            SurveillancePattern.LAYERING: self._detect_layering,
            SurveillancePattern.SPOOFING: self._detect_spoofing,
            SurveillancePattern.WASH_TRADING: self._detect_wash_trading,
            SurveillancePattern.MOMENTUM_IGNITION: self._detect_momentum_ignition,
            SurveillancePattern.QUOTE_STUFFING: self._detect_quote_stuffing
        }
        
        # Trade history for pattern detection
        self.trade_history = {}
        self.order_history = {}
    
    async def analyze_trade_patterns(self, trades: List[TradeRecord]) -> List[SurveillanceAlert]:
        """Analyze trades for suspicious patterns."""
        try:
            alerts = []
            
            # Update trade history
            for trade in trades:
                symbol = trade.symbol
                if symbol not in self.trade_history:
                    self.trade_history[symbol] = []
                self.trade_history[symbol].append(trade)
                
                # Keep only recent trades (last 24 hours)
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.trade_history[symbol] = [
                    t for t in self.trade_history[symbol] 
                    if t.timestamp >= cutoff_time
                ]
            
            # Run pattern detection algorithms
            for pattern, detector in self.detection_algorithms.items():
                try:
                    pattern_alerts = await detector(trades)
                    alerts.extend(pattern_alerts)
                except Exception as e:
                    logger.error(f"Error in {pattern.value} detection: {e}")
            
            # Sort alerts by risk score
            alerts.sort(key=lambda x: x.risk_score, reverse=True)
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error in trade surveillance: {e}")
            return []
    
    async def _detect_layering(self, trades: List[TradeRecord]) -> List[SurveillanceAlert]:
        """Detect layering pattern (placing and canceling orders to manipulate price)."""
        alerts = []
        
        # Group trades by symbol and trader
        trader_symbols = {}
        for trade in trades:
            key = (trade.trader_id, trade.symbol)
            if key not in trader_symbols:
                trader_symbols[key] = []
            trader_symbols[key].append(trade)
        
        for (trader_id, symbol), trader_trades in trader_symbols.items():
            if len(trader_trades) < 5:  # Need minimum trades for pattern
                continue
            
            # Analyze order patterns
            # Simplified detection: high order-to-trade ratio with price movement
            order_count = len(trader_trades) * 3  # Simulate orders (3:1 ratio)
            execution_rate = len(trader_trades) / order_count
            
            # Check for price movement correlation
            prices = [t.price for t in trader_trades]
            price_volatility = np.std(prices) / np.mean(prices) if prices else 0
            
            # Layering indicators
            if execution_rate < 0.2 and price_volatility > 0.02:  # Low execution, high volatility
                risk_score = min(0.8, (1 - execution_rate) * price_volatility * 10)
                
                if risk_score > 0.5:
                    alert = SurveillanceAlert(
                        alert_id=f"layering_{trader_id}_{symbol}_{datetime.now().timestamp()}",
                        pattern_type=SurveillancePattern.LAYERING,
                        trades_involved=[t.trade_id for t in trader_trades],
                        risk_score=risk_score,
                        confidence=0.7,
                        description=f"Potential layering detected: {execution_rate:.1%} execution rate with {price_volatility:.2%} volatility",
                        timestamp=datetime.now(),
                        investigation_required=risk_score > 0.7,
                        regulatory_impact=[RegulationType.FINRA, RegulationType.MIFID2]
                    )
                    alerts.append(alert)
        
        return alerts
    
    async def _detect_spoofing(self, trades: List[TradeRecord]) -> List[SurveillanceAlert]:
        """Detect spoofing pattern (placing orders with intent to cancel)."""
        alerts = []
        
        # Simplified spoofing detection
        for trade in trades:
            # Check for rapid order placement and cancellation patterns
            # This would analyze order book data in production
            
            # Simulate spoofing detection
            if np.random.random() < 0.02:  # 2% chance of spoofing alert
                alert = SurveillanceAlert(
                    alert_id=f"spoofing_{trade.trade_id}_{datetime.now().timestamp()}",
                    pattern_type=SurveillancePattern.SPOOFING,
                    trades_involved=[trade.trade_id],
                    risk_score=np.random.uniform(0.6, 0.9),
                    confidence=0.8,
                    description="Potential spoofing: Large orders placed and quickly cancelled",
                    timestamp=datetime.now(),
                    investigation_required=True,
                    regulatory_impact=[RegulationType.FINRA, RegulationType.MIFID2]
                )
                alerts.append(alert)
        
        return alerts
    
    async def _detect_wash_trading(self, trades: List[TradeRecord]) -> List[SurveillanceAlert]:
        """Detect wash trading (trading with yourself or related entities)."""
        alerts = []
        
        # Group trades by symbol and time
        symbol_trades = {}
        for trade in trades:
            if trade.symbol not in symbol_trades:
                symbol_trades[trade.symbol] = []
            symbol_trades[trade.symbol].append(trade)
        
        for symbol, symbol_trade_list in symbol_trades.items():
            # Look for matching trades (same size, opposite sides, similar timing)
            for i, trade1 in enumerate(symbol_trade_list):
                for trade2 in symbol_trade_list[i+1:]:
                    # Check for wash trading indicators
                    time_diff = abs((trade1.timestamp - trade2.timestamp).total_seconds())
                    size_match = abs(trade1.quantity - trade2.quantity) / max(trade1.quantity, trade2.quantity) < 0.05
                    opposite_sides = trade1.side != trade2.side
                    similar_timing = time_diff < 300  # 5 minutes
                    
                    if size_match and opposite_sides and similar_timing:
                        # Additional checks for related entities
                        potentially_related = await self._check_entity_relationship(trade1.trader_id, trade2.trader_id)
                        
                        if potentially_related:
                            risk_score = 0.8
                            alert = SurveillanceAlert(
                                alert_id=f"wash_trading_{trade1.trade_id}_{trade2.trade_id}_{datetime.now().timestamp()}",
                                pattern_type=SurveillancePattern.WASH_TRADING,
                                trades_involved=[trade1.trade_id, trade2.trade_id],
                                risk_score=risk_score,
                                confidence=0.75,
                                description=f"Potential wash trading: Matching trades between related entities",
                                timestamp=datetime.now(),
                                investigation_required=True,
                                regulatory_impact=[RegulationType.FINRA, RegulationType.MIFID2]
                            )
                            alerts.append(alert)
        
        return alerts
    
    async def _detect_momentum_ignition(self, trades: List[TradeRecord]) -> List[SurveillanceAlert]:
        """Detect momentum ignition (aggressive trading to trigger algorithmic responses)."""
        alerts = []
        
        # Analyze for sudden bursts of aggressive trading
        for symbol in set(trade.symbol for trade in trades):
            symbol_trades = [t for t in trades if t.symbol == symbol]
            
            if len(symbol_trades) < 3:
                continue
            
            # Check for rapid succession of trades in same direction
            symbol_trades.sort(key=lambda x: x.timestamp)
            
            consecutive_same_side = 1
            max_consecutive = 1
            
            for i in range(1, len(symbol_trades)):
                if symbol_trades[i].side == symbol_trades[i-1].side:
                    consecutive_same_side += 1
                    max_consecutive = max(max_consecutive, consecutive_same_side)
                else:
                    consecutive_same_side = 1
            
            # Check timing and volume
            if max_consecutive >= 5:  # 5+ consecutive trades same side
                time_span = (symbol_trades[-1].timestamp - symbol_trades[0].timestamp).total_seconds()
                if time_span < 60:  # Within 1 minute
                    risk_score = min(0.9, max_consecutive / 10 + (60 - time_span) / 60 * 0.3)
                    
                    if risk_score > 0.6:
                        alert = SurveillanceAlert(
                            alert_id=f"momentum_ignition_{symbol}_{datetime.now().timestamp()}",
                            pattern_type=SurveillancePattern.MOMENTUM_IGNITION,
                            trades_involved=[t.trade_id for t in symbol_trades],
                            risk_score=risk_score,
                            confidence=0.6,
                            description=f"Potential momentum ignition: {max_consecutive} consecutive trades in {time_span:.0f}s",
                            timestamp=datetime.now(),
                            investigation_required=risk_score > 0.8,
                            regulatory_impact=[RegulationType.FINRA]
                        )
                        alerts.append(alert)
        
        return alerts
    
    async def _detect_quote_stuffing(self, trades: List[TradeRecord]) -> List[SurveillanceAlert]:
        """Detect quote stuffing (excessive order submissions to slow down systems)."""
        alerts = []
        
        # This would analyze order submission rates in production
        # For demo, simulate detection based on trade frequency
        
        trader_activity = {}
        for trade in trades:
            if trade.trader_id not in trader_activity:
                trader_activity[trade.trader_id] = []
            trader_activity[trade.trader_id].append(trade.timestamp)
        
        for trader_id, timestamps in trader_activity.items():
            if len(timestamps) < 10:  # Need significant activity
                continue
            
            # Calculate submission rate
            timestamps.sort()
            time_span = (timestamps[-1] - timestamps[0]).total_seconds()
            submission_rate = len(timestamps) / time_span if time_span > 0 else 0
            
            # Quote stuffing threshold: >100 submissions per second
            if submission_rate > 100:
                risk_score = min(0.9, submission_rate / 1000)
                
                alert = SurveillanceAlert(
                    alert_id=f"quote_stuffing_{trader_id}_{datetime.now().timestamp()}",
                    pattern_type=SurveillancePattern.QUOTE_STUFFING,
                    trades_involved=[],  # Would include order IDs
                    risk_score=risk_score,
                    confidence=0.8,
                    description=f"Potential quote stuffing: {submission_rate:.0f} submissions/second",
                    timestamp=datetime.now(),
                    investigation_required=True,
                    regulatory_impact=[RegulationType.FINRA, RegulationType.MIFID2]
                )
                alerts.append(alert)
        
        return alerts
    
    async def _check_entity_relationship(self, trader1: str, trader2: str) -> bool:
        """Check if two traders are related entities."""
        # Simplified relationship check
        # In production, this would check against entity relationship databases
        
        # Same firm indicator
        if trader1.split('_')[0] == trader2.split('_')[0]:
            return True
        
        # Random relationship for demo
        return np.random.random() < 0.1  # 10% chance of relationship
    
    def _get_error_compliance_check(self, trade_id: str, regulation_type: RegulationType, error: str) -> ComplianceCheck:
        """Return error compliance check."""
        return ComplianceCheck(
            check_id=f"error_{trade_id}_{datetime.now().timestamp()}",
            trade_id=trade_id,
            regulation_type=regulation_type,
            check_type="error",
            status=ComplianceStatus.UNKNOWN,
            risk_score=1.0,
            details={"error": error},
            timestamp=datetime.now(),
            requires_reporting=False,
            remediation_required=True
        )
