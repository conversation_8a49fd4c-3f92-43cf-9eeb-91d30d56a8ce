#!/usr/bin/env python3
"""
AthenaTrader Phase 10 Hardware Detection and Validation Script

This script detects and validates DPDK-compatible NICs and FPGA cards for
production HFT deployment, ensuring hardware compatibility and documenting
all components for audit compliance.

Supported Hardware:
- NICs: Intel X710, Mellanox ConnectX-5, Intel E810
- FPGAs: Xilinx Alveo U250/U280, Intel Stratix 10
"""

import os
import sys
import json
import subprocess
import logging
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class NetworkInterface:
    """Network interface hardware information."""
    pci_address: str
    device_id: str
    vendor_id: str
    device_name: str
    driver: str
    driver_version: str
    firmware_version: str
    link_speed: str
    numa_node: int
    dpdk_compatible: bool
    serial_number: str = ""
    mac_address: str = ""


@dataclass
class FPGADevice:
    """FPGA device hardware information."""
    pci_address: str
    device_id: str
    vendor_id: str
    device_name: str
    driver: str
    driver_version: str
    firmware_version: str
    numa_node: int
    fpga_compatible: bool
    serial_number: str = ""
    board_name: str = ""
    shell_version: str = ""


@dataclass
class HardwareInventory:
    """Complete hardware inventory for HFT deployment."""
    timestamp: str
    hostname: str
    cpu_info: Dict[str, Any]
    memory_info: Dict[str, Any]
    network_interfaces: List[NetworkInterface]
    fpga_devices: List[FPGADevice]
    system_info: Dict[str, Any]
    compliance_status: Dict[str, bool]


class HardwareDetector:
    """Hardware detection and validation for HFT deployment."""
    
    # Supported DPDK-compatible NICs
    SUPPORTED_NICS = {
        "8086:1572": "Intel X710 10GbE",
        "8086:1583": "Intel X710 40GbE", 
        "8086:1589": "Intel X710 10GbE SFP+",
        "8086:1591": "Intel E810 100GbE",
        "8086:1592": "Intel E810 25GbE",
        "15b3:1017": "Mellanox ConnectX-5 25GbE",
        "15b3:1019": "Mellanox ConnectX-5 50GbE",
        "15b3:101b": "Mellanox ConnectX-6 100GbE"
    }
    
    # Supported FPGA devices
    SUPPORTED_FPGAS = {
        "10ee:5000": "Xilinx Alveo U200",
        "10ee:5001": "Xilinx Alveo U250", 
        "10ee:5002": "Xilinx Alveo U280",
        "8086:0b30": "Intel Stratix 10 GX",
        "8086:0b31": "Intel Stratix 10 SX"
    }
    
    def __init__(self):
        """Initialize hardware detector."""
        self.inventory = None
        logger.info("Hardware detector initialized")
    
    def detect_all_hardware(self) -> HardwareInventory:
        """Detect and validate all hardware components."""
        logger.info("Starting comprehensive hardware detection...")
        
        # Get system information
        hostname = subprocess.run(['hostname'], capture_output=True, text=True).stdout.strip()
        timestamp = datetime.now().isoformat()
        
        # Detect components
        cpu_info = self._detect_cpu_info()
        memory_info = self._detect_memory_info()
        network_interfaces = self._detect_network_interfaces()
        fpga_devices = self._detect_fpga_devices()
        system_info = self._detect_system_info()
        
        # Validate compliance
        compliance_status = self._validate_compliance(
            cpu_info, memory_info, network_interfaces, fpga_devices
        )
        
        self.inventory = HardwareInventory(
            timestamp=timestamp,
            hostname=hostname,
            cpu_info=cpu_info,
            memory_info=memory_info,
            network_interfaces=network_interfaces,
            fpga_devices=fpga_devices,
            system_info=system_info,
            compliance_status=compliance_status
        )
        
        logger.info("Hardware detection completed")
        return self.inventory
    
    def _detect_cpu_info(self) -> Dict[str, Any]:
        """Detect CPU information and capabilities."""
        logger.info("Detecting CPU information...")
        
        cpu_info = {
            "model": "",
            "cores": 0,
            "threads": 0,
            "frequency_mhz": 0,
            "cache_sizes": {},
            "features": [],
            "numa_nodes": 0,
            "isolated_cores": [],
            "governor": ""
        }
        
        try:
            # Get CPU model and basic info
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
            
            # Extract CPU model
            model_match = re.search(r'model name\s*:\s*(.+)', cpuinfo)
            if model_match:
                cpu_info["model"] = model_match.group(1).strip()
            
            # Count cores and threads
            cpu_info["cores"] = len(re.findall(r'^processor\s*:', cpuinfo, re.MULTILINE))
            
            # Get CPU frequency
            try:
                freq_result = subprocess.run(['lscpu'], capture_output=True, text=True)
                freq_match = re.search(r'CPU MHz:\s*(\d+\.?\d*)', freq_result.stdout)
                if freq_match:
                    cpu_info["frequency_mhz"] = float(freq_match.group(1))
            except Exception:
                pass
            
            # Check for required features
            required_features = ['avx2', 'sse4_2', 'rdtscp']
            for feature in required_features:
                if feature in cpuinfo:
                    cpu_info["features"].append(feature)
            
            # Get NUMA information
            try:
                numa_result = subprocess.run(['numactl', '--hardware'], capture_output=True, text=True)
                numa_match = re.search(r'available: (\d+) nodes', numa_result.stdout)
                if numa_match:
                    cpu_info["numa_nodes"] = int(numa_match.group(1))
            except Exception:
                cpu_info["numa_nodes"] = 1
            
            # Check CPU governor
            try:
                with open('/sys/devices/system/cpu/cpu0/cpufreq/scaling_governor', 'r') as f:
                    cpu_info["governor"] = f.read().strip()
            except Exception:
                cpu_info["governor"] = "unknown"
            
            # Check for isolated cores
            try:
                with open('/proc/cmdline', 'r') as f:
                    cmdline = f.read()
                    isolcpus_match = re.search(r'isolcpus=([0-9,-]+)', cmdline)
                    if isolcpus_match:
                        cpu_info["isolated_cores"] = isolcpus_match.group(1).split(',')
            except Exception:
                pass
            
        except Exception as e:
            logger.error(f"CPU detection failed: {e}")
        
        return cpu_info
    
    def _detect_memory_info(self) -> Dict[str, Any]:
        """Detect memory information and huge pages configuration."""
        logger.info("Detecting memory information...")
        
        memory_info = {
            "total_gb": 0,
            "available_gb": 0,
            "huge_pages_2mb": 0,
            "huge_pages_1gb": 0,
            "huge_pages_free": 0,
            "numa_memory": {}
        }
        
        try:
            # Get memory information from /proc/meminfo
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
            
            # Extract total memory
            mem_total_match = re.search(r'MemTotal:\s*(\d+)\s*kB', meminfo)
            if mem_total_match:
                memory_info["total_gb"] = int(mem_total_match.group(1)) / 1024 / 1024
            
            # Extract available memory
            mem_available_match = re.search(r'MemAvailable:\s*(\d+)\s*kB', meminfo)
            if mem_available_match:
                memory_info["available_gb"] = int(mem_available_match.group(1)) / 1024 / 1024
            
            # Extract huge pages information
            hugepages_total_match = re.search(r'HugePages_Total:\s*(\d+)', meminfo)
            if hugepages_total_match:
                memory_info["huge_pages_2mb"] = int(hugepages_total_match.group(1))
            
            hugepages_free_match = re.search(r'HugePages_Free:\s*(\d+)', meminfo)
            if hugepages_free_match:
                memory_info["huge_pages_free"] = int(hugepages_free_match.group(1))
            
            # Check for 1GB huge pages
            try:
                with open('/sys/kernel/mm/hugepages/hugepages-1048576kB/nr_hugepages', 'r') as f:
                    memory_info["huge_pages_1gb"] = int(f.read().strip())
            except Exception:
                memory_info["huge_pages_1gb"] = 0
            
            # Get NUMA memory information
            try:
                numa_result = subprocess.run(['numactl', '--hardware'], capture_output=True, text=True)
                for line in numa_result.stdout.split('\n'):
                    if 'node' in line and 'size:' in line:
                        node_match = re.search(r'node (\d+) size: (\d+) MB', line)
                        if node_match:
                            node_id = int(node_match.group(1))
                            size_mb = int(node_match.group(2))
                            memory_info["numa_memory"][node_id] = size_mb
            except Exception:
                pass
            
        except Exception as e:
            logger.error(f"Memory detection failed: {e}")
        
        return memory_info
    
    def _detect_network_interfaces(self) -> List[NetworkInterface]:
        """Detect and validate network interfaces."""
        logger.info("Detecting network interfaces...")
        
        interfaces = []
        
        try:
            # Get PCI device information
            lspci_result = subprocess.run(['lspci', '-nn', '-v'], capture_output=True, text=True)
            
            # Parse network devices
            current_device = {}
            for line in lspci_result.stdout.split('\n'):
                if 'Ethernet controller' in line or 'Network controller' in line:
                    # Extract PCI address and device info
                    pci_match = re.match(r'^([0-9a-f:\.]+)\s+.*\[([0-9a-f]{4}):([0-9a-f]{4})\]', line)
                    if pci_match:
                        pci_address = pci_match.group(1)
                        vendor_id = pci_match.group(2)
                        device_id = pci_match.group(3)
                        device_key = f"{vendor_id}:{device_id}"
                        
                        # Check if this is a supported NIC
                        dpdk_compatible = device_key in self.SUPPORTED_NICS
                        device_name = self.SUPPORTED_NICS.get(device_key, "Unknown NIC")
                        
                        # Get additional information
                        driver_info = self._get_interface_driver_info(pci_address)
                        numa_node = self._get_device_numa_node(pci_address)
                        
                        interface = NetworkInterface(
                            pci_address=pci_address,
                            device_id=device_id,
                            vendor_id=vendor_id,
                            device_name=device_name,
                            driver=driver_info.get('driver', 'unknown'),
                            driver_version=driver_info.get('version', 'unknown'),
                            firmware_version=driver_info.get('firmware', 'unknown'),
                            link_speed=driver_info.get('speed', 'unknown'),
                            numa_node=numa_node,
                            dpdk_compatible=dpdk_compatible,
                            serial_number=driver_info.get('serial', ''),
                            mac_address=driver_info.get('mac', '')
                        )
                        
                        interfaces.append(interface)
                        logger.info(f"Found NIC: {device_name} at {pci_address} (DPDK: {dpdk_compatible})")
        
        except Exception as e:
            logger.error(f"Network interface detection failed: {e}")
        
        return interfaces
    
    def _detect_fpga_devices(self) -> List[FPGADevice]:
        """Detect and validate FPGA devices."""
        logger.info("Detecting FPGA devices...")
        
        devices = []
        
        try:
            # Get PCI device information
            lspci_result = subprocess.run(['lspci', '-nn', '-v'], capture_output=True, text=True)
            
            # Parse FPGA devices
            for line in lspci_result.stdout.split('\n'):
                if any(keyword in line.lower() for keyword in ['fpga', 'xilinx', 'altera', 'intel']):
                    # Extract PCI address and device info
                    pci_match = re.match(r'^([0-9a-f:\.]+)\s+.*\[([0-9a-f]{4}):([0-9a-f]{4})\]', line)
                    if pci_match:
                        pci_address = pci_match.group(1)
                        vendor_id = pci_match.group(2)
                        device_id = pci_match.group(3)
                        device_key = f"{vendor_id}:{device_id}"
                        
                        # Check if this is a supported FPGA
                        fpga_compatible = device_key in self.SUPPORTED_FPGAS
                        device_name = self.SUPPORTED_FPGAS.get(device_key, "Unknown FPGA")
                        
                        # Get additional information
                        driver_info = self._get_fpga_driver_info(pci_address)
                        numa_node = self._get_device_numa_node(pci_address)
                        
                        device = FPGADevice(
                            pci_address=pci_address,
                            device_id=device_id,
                            vendor_id=vendor_id,
                            device_name=device_name,
                            driver=driver_info.get('driver', 'unknown'),
                            driver_version=driver_info.get('version', 'unknown'),
                            firmware_version=driver_info.get('firmware', 'unknown'),
                            numa_node=numa_node,
                            fpga_compatible=fpga_compatible,
                            serial_number=driver_info.get('serial', ''),
                            board_name=driver_info.get('board', ''),
                            shell_version=driver_info.get('shell', '')
                        )
                        
                        devices.append(device)
                        logger.info(f"Found FPGA: {device_name} at {pci_address} (Compatible: {fpga_compatible})")
        
        except Exception as e:
            logger.error(f"FPGA detection failed: {e}")
        
        return devices
    
    def _get_interface_driver_info(self, pci_address: str) -> Dict[str, str]:
        """Get detailed driver information for network interface."""
        info = {}
        
        try:
            # Get driver name
            driver_path = f"/sys/bus/pci/devices/0000:{pci_address}/driver"
            if os.path.exists(driver_path):
                driver_link = os.readlink(driver_path)
                info['driver'] = os.path.basename(driver_link)
            
            # Try to get interface name
            net_path = f"/sys/bus/pci/devices/0000:{pci_address}/net"
            if os.path.exists(net_path):
                interfaces = os.listdir(net_path)
                if interfaces:
                    interface_name = interfaces[0]
                    
                    # Get MAC address
                    try:
                        with open(f"{net_path}/{interface_name}/address", 'r') as f:
                            info['mac'] = f.read().strip()
                    except Exception:
                        pass
                    
                    # Get link speed using ethtool
                    try:
                        ethtool_result = subprocess.run(
                            ['ethtool', interface_name], 
                            capture_output=True, text=True
                        )
                        speed_match = re.search(r'Speed: (.+)', ethtool_result.stdout)
                        if speed_match:
                            info['speed'] = speed_match.group(1)
                    except Exception:
                        pass
        
        except Exception as e:
            logger.debug(f"Could not get driver info for {pci_address}: {e}")
        
        return info
    
    def _get_fpga_driver_info(self, pci_address: str) -> Dict[str, str]:
        """Get detailed driver information for FPGA device."""
        info = {}
        
        try:
            # Get driver name
            driver_path = f"/sys/bus/pci/devices/0000:{pci_address}/driver"
            if os.path.exists(driver_path):
                driver_link = os.readlink(driver_path)
                info['driver'] = os.path.basename(driver_link)
            
            # Try to get Xilinx-specific information
            if 'xilinx' in info.get('driver', '').lower():
                try:
                    # Check for xbutil (Xilinx Board Utility)
                    xbutil_result = subprocess.run(
                        ['xbutil', 'examine'], 
                        capture_output=True, text=True
                    )
                    # Parse xbutil output for device information
                    # This would contain shell version, board name, etc.
                except Exception:
                    pass
        
        except Exception as e:
            logger.debug(f"Could not get FPGA driver info for {pci_address}: {e}")
        
        return info
    
    def _get_device_numa_node(self, pci_address: str) -> int:
        """Get NUMA node for PCI device."""
        try:
            numa_path = f"/sys/bus/pci/devices/0000:{pci_address}/numa_node"
            if os.path.exists(numa_path):
                with open(numa_path, 'r') as f:
                    return int(f.read().strip())
        except Exception:
            pass
        return 0
    
    def _detect_system_info(self) -> Dict[str, Any]:
        """Detect general system information."""
        system_info = {}
        
        try:
            # OS information
            with open('/etc/os-release', 'r') as f:
                os_release = f.read()
                name_match = re.search(r'NAME="([^"]+)"', os_release)
                version_match = re.search(r'VERSION="([^"]+)"', os_release)
                
                if name_match:
                    system_info['os_name'] = name_match.group(1)
                if version_match:
                    system_info['os_version'] = version_match.group(1)
            
            # Kernel version
            kernel_result = subprocess.run(['uname', '-r'], capture_output=True, text=True)
            system_info['kernel_version'] = kernel_result.stdout.strip()
            
            # IOMMU status
            try:
                with open('/proc/cmdline', 'r') as f:
                    cmdline = f.read()
                    system_info['iommu_enabled'] = 'intel_iommu=on' in cmdline or 'amd_iommu=on' in cmdline
            except Exception:
                system_info['iommu_enabled'] = False
        
        except Exception as e:
            logger.error(f"System info detection failed: {e}")
        
        return system_info
    
    def _validate_compliance(self, cpu_info: Dict, memory_info: Dict, 
                           network_interfaces: List[NetworkInterface], 
                           fpga_devices: List[FPGADevice]) -> Dict[str, bool]:
        """Validate hardware compliance for HFT deployment."""
        compliance = {}
        
        # CPU compliance
        compliance['cpu_performance_governor'] = cpu_info.get('governor') == 'performance'
        compliance['cpu_isolated_cores'] = len(cpu_info.get('isolated_cores', [])) >= 2
        compliance['cpu_avx2_support'] = 'avx2' in cpu_info.get('features', [])
        compliance['cpu_numa_nodes'] = cpu_info.get('numa_nodes', 0) >= 1
        
        # Memory compliance
        compliance['memory_sufficient'] = memory_info.get('total_gb', 0) >= 32
        compliance['huge_pages_configured'] = memory_info.get('huge_pages_2mb', 0) >= 512
        
        # Network compliance
        dpdk_nics = [nic for nic in network_interfaces if nic.dpdk_compatible]
        compliance['dpdk_nics_available'] = len(dpdk_nics) >= 1
        compliance['network_numa_distributed'] = len(set(nic.numa_node for nic in dpdk_nics)) >= 1
        
        # FPGA compliance (optional)
        compatible_fpgas = [fpga for fpga in fpga_devices if fpga.fpga_compatible]
        compliance['fpga_devices_available'] = len(compatible_fpgas) >= 0  # Optional
        
        return compliance
    
    def save_inventory(self, output_path: str = "hardware_inventory.json"):
        """Save hardware inventory to JSON file."""
        if not self.inventory:
            raise ValueError("No inventory data available. Run detect_all_hardware() first.")
        
        inventory_dict = asdict(self.inventory)
        
        with open(output_path, 'w') as f:
            json.dump(inventory_dict, f, indent=2, default=str)
        
        logger.info(f"Hardware inventory saved to {output_path}")
    
    def generate_compliance_report(self) -> str:
        """Generate compliance report for audit purposes."""
        if not self.inventory:
            raise ValueError("No inventory data available. Run detect_all_hardware() first.")
        
        report = []
        report.append("=" * 80)
        report.append("ATHENATRADER PHASE 10 HFT HARDWARE COMPLIANCE REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {self.inventory.timestamp}")
        report.append(f"Hostname: {self.inventory.hostname}")
        report.append("")
        
        # CPU compliance
        report.append("CPU COMPLIANCE:")
        cpu_compliance = [
            ("Performance Governor", self.inventory.compliance_status['cpu_performance_governor']),
            ("Isolated Cores (≥2)", self.inventory.compliance_status['cpu_isolated_cores']),
            ("AVX2 Support", self.inventory.compliance_status['cpu_avx2_support']),
            ("NUMA Nodes", self.inventory.compliance_status['cpu_numa_nodes'])
        ]
        
        for check, status in cpu_compliance:
            status_str = "✓ PASS" if status else "✗ FAIL"
            report.append(f"  {check:<25} {status_str}")
        
        # Memory compliance
        report.append("\nMEMORY COMPLIANCE:")
        memory_compliance = [
            ("Sufficient Memory (≥32GB)", self.inventory.compliance_status['memory_sufficient']),
            ("Huge Pages (≥512)", self.inventory.compliance_status['huge_pages_configured'])
        ]
        
        for check, status in memory_compliance:
            status_str = "✓ PASS" if status else "✗ FAIL"
            report.append(f"  {check:<25} {status_str}")
        
        # Network compliance
        report.append("\nNETWORK COMPLIANCE:")
        network_compliance = [
            ("DPDK NICs Available", self.inventory.compliance_status['dpdk_nics_available']),
            ("NUMA Distribution", self.inventory.compliance_status['network_numa_distributed'])
        ]
        
        for check, status in network_compliance:
            status_str = "✓ PASS" if status else "✗ FAIL"
            report.append(f"  {check:<25} {status_str}")
        
        # Hardware inventory
        report.append("\nHARDWARE INVENTORY:")
        report.append(f"  DPDK-Compatible NICs: {len([nic for nic in self.inventory.network_interfaces if nic.dpdk_compatible])}")
        report.append(f"  FPGA Devices: {len([fpga for fpga in self.inventory.fpga_devices if fpga.fpga_compatible])}")
        
        # Overall compliance
        all_critical_passed = all([
            self.inventory.compliance_status['cpu_performance_governor'],
            self.inventory.compliance_status['memory_sufficient'],
            self.inventory.compliance_status['huge_pages_configured'],
            self.inventory.compliance_status['dpdk_nics_available']
        ])
        
        report.append("\nOVERALL COMPLIANCE:")
        overall_status = "✓ COMPLIANT" if all_critical_passed else "✗ NON-COMPLIANT"
        report.append(f"  HFT Deployment Ready: {overall_status}")
        
        return "\n".join(report)


def main():
    """Main hardware detection function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Detect and validate HFT hardware")
    parser.add_argument("--output", default="hardware_inventory.json", 
                       help="Output file for hardware inventory")
    parser.add_argument("--report", action="store_true",
                       help="Generate compliance report")
    
    args = parser.parse_args()
    
    detector = HardwareDetector()
    inventory = detector.detect_all_hardware()
    
    # Save inventory
    detector.save_inventory(args.output)
    
    # Generate compliance report
    if args.report:
        report = detector.generate_compliance_report()
        print(report)
        
        # Save report to file
        report_file = args.output.replace('.json', '_compliance_report.txt')
        with open(report_file, 'w') as f:
            f.write(report)
        logger.info(f"Compliance report saved to {report_file}")


if __name__ == "__main__":
    main()
