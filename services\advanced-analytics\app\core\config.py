"""
AthenaTrader Phase 4: Regulatory Certification Service Configuration

Comprehensive configuration for EMI<PERSON>, Dodd<PERSON><PERSON>, MiFID II, FINRA compliance,
GDPR/CCPA data protection, and blockchain-based audit trails.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings."""

    # Service Configuration
    SERVICE_NAME: str = "AthenaTrader Regulatory Certification Service"
    SERVICE_VERSION: str = "4.0.0"
    SERVICE_PORT: int = 8007
    DEBUG: bool = Field(default=False, env="DEBUG")
    COMPLIANCE_SCORE_TARGET: float = Field(default=0.95, env="COMPLIANCE_SCORE_TARGET")  # 95% target

    # Database Configuration
    DATABASE_URL: str = Field(
        default="postgresql://athenatrader:password@localhost:5432/athenatrader",
        env="DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")

    # Redis Configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_POOL_SIZE: int = Field(default=20, env="REDIS_POOL_SIZE")

    # Kafka Configuration (for real-time data streaming)
    KAFKA_BOOTSTRAP_SERVERS: List[str] = Field(
        default=["localhost:9092"],
        env="KAFKA_BOOTSTRAP_SERVERS"
    )
    KAFKA_CONSUMER_GROUP: str = Field(
        default="athenatrader-analytics",
        env="KAFKA_CONSUMER_GROUP"
    )

    # ML Model Configuration
    MODEL_CACHE_TTL: int = Field(default=3600, env="MODEL_CACHE_TTL")  # 1 hour
    MODEL_BATCH_SIZE: int = Field(default=32, env="MODEL_BATCH_SIZE")
    MODEL_MAX_SEQUENCE_LENGTH: int = Field(default=100, env="MODEL_MAX_SEQUENCE_LENGTH")

    # Performance Targets
    API_RESPONSE_TIME_TARGET_MS: int = Field(default=100, env="API_RESPONSE_TIME_TARGET_MS")
    MODEL_INFERENCE_TIME_TARGET_MS: int = Field(default=50, env="MODEL_INFERENCE_TIME_TARGET_MS")
    PREDICTION_ACCURACY_TARGET: float = Field(default=0.95, env="PREDICTION_ACCURACY_TARGET")

    # Market Data Configuration
    MARKET_DATA_UPDATE_INTERVAL_MS: int = Field(default=100, env="MARKET_DATA_UPDATE_INTERVAL_MS")
    HISTORICAL_DATA_YEARS: int = Field(default=5, env="HISTORICAL_DATA_YEARS")

    # Sentiment Analysis Configuration
    NEWS_API_KEY: Optional[str] = Field(default=None, env="NEWS_API_KEY")
    TWITTER_BEARER_TOKEN: Optional[str] = Field(default=None, env="TWITTER_BEARER_TOKEN")
    SENTIMENT_UPDATE_INTERVAL_MINUTES: int = Field(default=5, env="SENTIMENT_UPDATE_INTERVAL_MINUTES")

    # Cryptocurrency Configuration
    BINANCE_API_KEY: Optional[str] = Field(default=None, env="BINANCE_API_KEY")
    BINANCE_SECRET_KEY: Optional[str] = Field(default=None, env="BINANCE_SECRET_KEY")
    COINBASE_API_KEY: Optional[str] = Field(default=None, env="COINBASE_API_KEY")
    COINBASE_SECRET_KEY: Optional[str] = Field(default=None, env="COINBASE_SECRET_KEY")

    # Fixed Income Configuration
    FRED_API_KEY: Optional[str] = Field(default=None, env="FRED_API_KEY")  # Federal Reserve Economic Data
    TREASURY_DIRECT_API: str = Field(
        default="https://api.fiscaldata.treasury.gov/services/api/v1/accounting/od/rates_of_exchange",
        env="TREASURY_DIRECT_API"
    )

    # Regulatory Compliance Configuration
    # EMIR Configuration
    EMIR_REPORTING_ENDPOINT: Optional[str] = Field(default=None, env="EMIR_REPORTING_ENDPOINT")
    EMIR_TRADE_REPOSITORY: str = Field(default="DTCC_GTR", env="EMIR_TRADE_REPOSITORY")
    EMIR_REPORTING_THRESHOLD_EUR: int = Field(default=1000000, env="EMIR_REPORTING_THRESHOLD_EUR")  # €1M

    # Dodd-Frank Configuration
    DODD_FRANK_REPORTING_ENDPOINT: Optional[str] = Field(default=None, env="DODD_FRANK_REPORTING_ENDPOINT")
    DODD_FRANK_SWAP_THRESHOLD_USD: int = Field(default=**********, env="DODD_FRANK_SWAP_THRESHOLD_USD")  # $8B
    DODD_FRANK_REPORTING_DELAY_HOURS: int = Field(default=24, env="DODD_FRANK_REPORTING_DELAY_HOURS")

    # MiFID II Configuration
    MIFID_TRANSACTION_REPORTING_ENDPOINT: Optional[str] = Field(default=None, env="MIFID_TRANSACTION_REPORTING_ENDPOINT")
    MIFID_COMPETENT_AUTHORITY: str = Field(default="FCA", env="MIFID_COMPETENT_AUTHORITY")
    MIFID_REPORTING_DEADLINE_HOURS: int = Field(default=24, env="MIFID_REPORTING_DEADLINE_HOURS")  # T+1

    # FINRA Configuration
    FINRA_SURVEILLANCE_ENDPOINT: Optional[str] = Field(default=None, env="FINRA_SURVEILLANCE_ENDPOINT")
    FINRA_NET_CAPITAL_THRESHOLD_USD: int = Field(default=250000, env="FINRA_NET_CAPITAL_THRESHOLD_USD")
    FINRA_AML_SCREENING_ENABLED: bool = Field(default=True, env="FINRA_AML_SCREENING_ENABLED")

    # Data Protection Configuration
    GDPR_ENABLED: bool = Field(default=True, env="GDPR_ENABLED")
    CCPA_ENABLED: bool = Field(default=True, env="CCPA_ENABLED")
    DATA_RETENTION_DAYS: int = Field(default=2555, env="DATA_RETENTION_DAYS")  # 7 years
    BREACH_NOTIFICATION_HOURS: int = Field(default=72, env="BREACH_NOTIFICATION_HOURS")  # GDPR requirement

    # Blockchain Audit Configuration
    BLOCKCHAIN_NETWORK: str = Field(default="hyperledger", env="BLOCKCHAIN_NETWORK")
    HYPERLEDGER_FABRIC_ENDPOINT: Optional[str] = Field(default=None, env="HYPERLEDGER_FABRIC_ENDPOINT")
    BLOCKCHAIN_BLOCK_TIME_SECONDS: int = Field(default=15, env="BLOCKCHAIN_BLOCK_TIME_SECONDS")
    AUDIT_RETENTION_YEARS: int = Field(default=7, env="AUDIT_RETENTION_YEARS")

    # Backtesting Configuration
    MONTE_CARLO_SIMULATIONS: int = Field(default=10000, env="MONTE_CARLO_SIMULATIONS")
    AGENT_SIMULATION_AGENTS: int = Field(default=1000, env="AGENT_SIMULATION_AGENTS")
    WALK_FORWARD_WINDOW_DAYS: int = Field(default=252, env="WALK_FORWARD_WINDOW_DAYS")  # 1 trading year

    # Risk Management Configuration
    MAX_POSITION_SIZE_USD: float = Field(default=10_000_000, env="MAX_POSITION_SIZE_USD")  # $10M
    MAX_PORTFOLIO_VAR_PERCENT: float = Field(default=0.02, env="MAX_PORTFOLIO_VAR_PERCENT")  # 2%
    CORRELATION_THRESHOLD: float = Field(default=0.8, env="CORRELATION_THRESHOLD")

    # Integration Endpoints
    STRATEGY_GENESIS_URL: str = Field(default="http://localhost:8002", env="STRATEGY_GENESIS_URL")
    BACKTESTING_ENGINE_URL: str = Field(default="http://localhost:8003", env="BACKTESTING_ENGINE_URL")
    XAI_MODULE_URL: str = Field(default="http://localhost:8005", env="XAI_MODULE_URL")
    LIVE_EXECUTION_URL: str = Field(default="http://localhost:8006", env="LIVE_EXECUTION_URL")

    # Monitoring & Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=9007, env="METRICS_PORT")

    # Security Configuration
    SECRET_KEY: str = Field(
        default="athenatrader-phase11-advanced-analytics-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")

    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()


# Model-specific configurations
class MLModelConfig:
    """ML Model specific configurations."""

    # LSTM Configuration
    LSTM_HIDDEN_SIZE = 128
    LSTM_NUM_LAYERS = 3
    LSTM_DROPOUT = 0.2
    LSTM_LEARNING_RATE = 0.001

    # Transformer Configuration
    TRANSFORMER_D_MODEL = 256
    TRANSFORMER_NHEAD = 8
    TRANSFORMER_NUM_LAYERS = 6
    TRANSFORMER_DROPOUT = 0.1
    TRANSFORMER_LEARNING_RATE = 0.0001

    # Sentiment Analysis Configuration
    SENTIMENT_MODEL_NAME = "finbert-sentiment"
    SENTIMENT_MAX_LENGTH = 512
    SENTIMENT_BATCH_SIZE = 16

    # Regime Detection Configuration
    HMM_N_COMPONENTS = 3  # Bull, Bear, Sideways
    HMM_COVARIANCE_TYPE = "full"
    HMM_N_ITER = 1000

    # GARCH Configuration
    GARCH_P = 1  # GARCH(1,1)
    GARCH_Q = 1
    GARCH_MEAN_MODEL = "Constant"
    GARCH_VOL_MODEL = "GARCH"
    GARCH_DIST = "normal"


class BacktestingConfig:
    """Backtesting specific configurations."""

    # Agent Types and Proportions
    MARKET_MAKER_RATIO = 0.1
    INSTITUTIONAL_RATIO = 0.3
    RETAIL_RATIO = 0.6

    # Market Microstructure Parameters
    TICK_SIZE = 0.01
    MIN_SPREAD_BPS = 1  # 1 basis point
    MAX_SPREAD_BPS = 50  # 50 basis points
    MARKET_IMPACT_COEFFICIENT = 0.1

    # Simulation Parameters
    SIMULATION_FREQUENCY = "1min"  # 1-minute bars
    COMMISSION_RATE = 0.001  # 0.1%
    SLIPPAGE_BPS = 2  # 2 basis points


class MultiAssetConfig:
    """Multi-asset trading configurations."""

    # Supported Asset Classes
    SUPPORTED_CRYPTO = [
        "BTC", "ETH", "ADA", "DOT", "LINK", "UNI", "AAVE", "SUSHI",
        "COMP", "MKR", "SNX", "YFI", "1INCH", "CRV", "BAL", "ALPHA",
        "BNB", "SOL", "AVAX", "MATIC"
    ]

    SUPPORTED_FIXED_INCOME = [
        "US_TREASURY_2Y", "US_TREASURY_5Y", "US_TREASURY_10Y", "US_TREASURY_30Y",
        "CORPORATE_AAA", "CORPORATE_BBB", "MUNICIPAL_BONDS", "TIPS"
    ]

    # Data Normalization Parameters
    PRICE_DECIMAL_PLACES = 8
    VOLUME_DECIMAL_PLACES = 4
    CORRELATION_WINDOW_DAYS = 30


class RegulatoryComplianceConfig:
    """Comprehensive regulatory compliance configurations for Phase 4."""

    # EMIR Configuration
    EMIR_REPORTING_FREQUENCY = "daily"
    EMIR_REQUIRED_FIELDS = [
        "trade_id", "execution_timestamp", "counterparty", "notional_amount",
        "currency", "maturity_date", "underlying_asset", "clearing_status"
    ]
    EMIR_TRADE_REPOSITORIES = [
        "DTCC_GTR", "REGIS_TR", "UPI_TR", "KDPW_TR"
    ]
    EMIR_MARGIN_THRESHOLD_EUR = 50_000_000  # €50M

    # Dodd-Frank Configuration
    DODD_FRANK_SWAP_THRESHOLD_USD = 8_000_000_000  # $8B
    DODD_FRANK_REPORTING_DELAY_HOURS = 24
    DODD_FRANK_VOLCKER_EXEMPTIONS = [
        "CUSTOMER_DRIVEN", "MARKET_MAKING", "HEDGING", "GOVERNMENT_SECURITIES"
    ]
    DODD_FRANK_POSITION_LIMITS = {
        "WHEAT": {"spot_month": 5000, "non_spot": 25000},
        "CORN": {"spot_month": 5000, "non_spot": 25000},
        "CRUDE_OIL": {"spot_month": 3000, "non_spot": 15000}
    }

    # MiFID II Configuration
    MIFID_TRANSACTION_REPORTING_DEADLINE_HOURS = 24  # T+1
    MIFID_BEST_EXECUTION_VENUES = [
        "LSE", "XETRA", "EURONEXT", "BATS", "TURQUOISE"
    ]
    MIFID_CLIENT_CATEGORIES = [
        "RETAIL", "PROFESSIONAL", "ELIGIBLE_COUNTERPARTY"
    ]
    MIFID_SURVEILLANCE_PATTERNS = [
        "LAYERING", "SPOOFING", "WASH_TRADING", "MOMENTUM_IGNITION"
    ]

    # FINRA Configuration
    FINRA_NET_CAPITAL_MINIMUM_USD = 250_000  # $250K
    FINRA_SURVEILLANCE_PATTERNS = [
        "LAYERING", "SPOOFING", "WASH_TRADING", "MOMENTUM_IGNITION",
        "QUOTE_STUFFING", "PINGING", "SMOKING"
    ]
    FINRA_AML_RISK_THRESHOLDS = {
        "HIGH_RISK_AMOUNT_USD": 10_000,
        "SUSPICIOUS_PATTERN_COUNT": 5,
        "VELOCITY_THRESHOLD": 100  # transactions per day
    }

    # Data Protection Configuration
    GDPR_DATA_SUBJECT_RIGHTS = [
        "ACCESS", "RECTIFICATION", "ERASURE", "PORTABILITY",
        "RESTRICTION", "OBJECTION"
    ]
    GDPR_LAWFUL_BASES = [
        "CONSENT", "CONTRACT", "LEGAL_OBLIGATION", "VITAL_INTERESTS",
        "PUBLIC_TASK", "LEGITIMATE_INTERESTS"
    ]
    GDPR_BREACH_NOTIFICATION_HOURS = 72

    CCPA_CONSUMER_RIGHTS = [
        "KNOW", "DELETE", "OPT_OUT", "NON_DISCRIMINATION"
    ]
    CCPA_RESPONSE_DEADLINE_DAYS = 45

    # Blockchain Audit Configuration
    BLOCKCHAIN_BLOCK_TIME_SECONDS = 15
    BLOCKCHAIN_CONSENSUS_ALGORITHM = "PBFT"  # Practical Byzantine Fault Tolerance
    AUDIT_RETENTION_YEARS = 7
    AUDIT_EVENT_TYPES = [
        "TRADE_EXECUTION", "STRATEGY_DEPLOYMENT", "RISK_BREACH",
        "COMPLIANCE_CHECK", "DATA_ACCESS", "CONSENT_CHANGE"
    ]

    # Performance Targets
    COMPLIANCE_SCORE_TARGET = 0.95  # 95%
    API_RESPONSE_TIME_TARGET_MS = 100
    AUDIT_TRAIL_INTEGRITY_TARGET = 0.999  # 99.9%

    # Integration Endpoints
    REGULATORY_AUTHORITIES = {
        "EMIR": ["ESMA", "FCA", "BAFIN", "AMF"],
        "MIFID": ["FCA", "BAFIN", "AMF", "CONSOB"],
        "DODD_FRANK": ["CFTC", "SEC", "FDIC"],
        "FINRA": ["FINRA", "SEC"]
    }
