"""
AthenaTrader Phase 11 Advanced Analytics Engine - Monte Carlo Stress Testing

Monte Carlo simulation framework for comprehensive strategy stress testing
with 10,000+ scenario simulations and statistical analysis.
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor
import multiprocessing as mp
from scipy import stats
from ..core.config import settings

logger = logging.getLogger("backtesting")


@dataclass
class MarketScenario:
    """Market scenario for Monte Carlo simulation."""
    scenario_id: str
    returns: np.ndarray
    volatility: float
    trend: float
    regime: str  # bull, bear, sideways
    correlation_matrix: Optional[np.ndarray] = None
    external_shocks: Optional[List[Dict[str, Any]]] = None


@dataclass
class StrategyParameters:
    """Strategy parameters for backtesting."""
    strategy_id: str
    parameters: Dict[str, Any]
    entry_rules: List[str]
    exit_rules: List[str]
    risk_management: Dict[str, Any]


@dataclass
class SimulationResult:
    """Result of a single Monte Carlo simulation."""
    scenario_id: str
    strategy_id: str
    total_return: float
    sharpe_ratio: float
    max_drawdown: float
    volatility: float
    win_rate: float
    profit_factor: float
    total_trades: int
    avg_trade_duration: float
    var_95: float
    cvar_95: float
    calmar_ratio: float
    sortino_ratio: float


class MarketDataGenerator:
    """Generate synthetic market data for Monte Carlo simulations."""
    
    def __init__(self):
        self.base_volatility = 0.20  # 20% annual volatility
        self.base_return = 0.08  # 8% annual return
        self.trading_days = 252
    
    def generate_geometric_brownian_motion(self, days: int, dt: float = 1/252, 
                                         mu: float = None, sigma: float = None) -> np.ndarray:
        """Generate price series using Geometric Brownian Motion."""
        if mu is None:
            mu = self.base_return
        if sigma is None:
            sigma = self.base_volatility
        
        # Number of time steps
        n_steps = int(days / dt)
        
        # Generate random shocks
        dW = np.random.normal(0, np.sqrt(dt), n_steps)
        
        # Calculate returns
        returns = (mu - 0.5 * sigma**2) * dt + sigma * dW
        
        # Convert to price series
        log_prices = np.cumsum(returns)
        prices = 100 * np.exp(log_prices)  # Start at $100
        
        return prices
    
    def generate_regime_switching_returns(self, days: int) -> Tuple[np.ndarray, List[str]]:
        """Generate returns with regime switching (bull/bear/sideways)."""
        n_steps = days
        returns = np.zeros(n_steps)
        regimes = []
        
        # Regime parameters
        regime_params = {
            'bull': {'mu': 0.15/252, 'sigma': 0.15/np.sqrt(252)},
            'bear': {'mu': -0.10/252, 'sigma': 0.25/np.sqrt(252)},
            'sideways': {'mu': 0.02/252, 'sigma': 0.12/np.sqrt(252)}
        }
        
        # Transition probabilities
        transition_matrix = {
            'bull': {'bull': 0.95, 'bear': 0.03, 'sideways': 0.02},
            'bear': {'bull': 0.05, 'bear': 0.90, 'sideways': 0.05},
            'sideways': {'bull': 0.10, 'bear': 0.05, 'sideways': 0.85}
        }
        
        # Start in bull regime
        current_regime = 'bull'
        
        for i in range(n_steps):
            # Generate return for current regime
            params = regime_params[current_regime]
            returns[i] = np.random.normal(params['mu'], params['sigma'])
            regimes.append(current_regime)
            
            # Transition to next regime
            rand = np.random.random()
            cumulative_prob = 0
            for next_regime, prob in transition_matrix[current_regime].items():
                cumulative_prob += prob
                if rand <= cumulative_prob:
                    current_regime = next_regime
                    break
        
        return returns, regimes
    
    def generate_jump_diffusion_returns(self, days: int, jump_intensity: float = 0.1) -> np.ndarray:
        """Generate returns with jump diffusion (Merton model)."""
        dt = 1/252
        n_steps = int(days / dt)
        
        # Continuous component (Geometric Brownian Motion)
        mu = self.base_return
        sigma = self.base_volatility
        
        dW = np.random.normal(0, np.sqrt(dt), n_steps)
        continuous_returns = (mu - 0.5 * sigma**2) * dt + sigma * dW
        
        # Jump component
        jump_times = np.random.poisson(jump_intensity * dt, n_steps)
        jump_sizes = np.random.normal(-0.02, 0.05, n_steps)  # Average -2% jump with 5% std
        jump_returns = jump_times * jump_sizes
        
        # Total returns
        total_returns = continuous_returns + jump_returns
        
        return total_returns
    
    def generate_correlated_assets(self, n_assets: int, days: int, 
                                 correlation_matrix: np.ndarray = None) -> np.ndarray:
        """Generate correlated asset returns."""
        if correlation_matrix is None:
            # Generate random correlation matrix
            correlation_matrix = self._generate_random_correlation_matrix(n_assets)
        
        # Generate independent returns
        independent_returns = np.random.multivariate_normal(
            mean=np.full(n_assets, self.base_return/252),
            cov=np.eye(n_assets) * (self.base_volatility/np.sqrt(252))**2,
            size=days
        )
        
        # Apply correlation
        L = np.linalg.cholesky(correlation_matrix)
        correlated_returns = independent_returns @ L.T
        
        return correlated_returns
    
    def _generate_random_correlation_matrix(self, n: int) -> np.ndarray:
        """Generate a random positive definite correlation matrix."""
        # Generate random matrix
        A = np.random.randn(n, n)
        
        # Make it positive definite
        correlation_matrix = A @ A.T
        
        # Normalize to correlation matrix
        d = np.sqrt(np.diag(correlation_matrix))
        correlation_matrix = correlation_matrix / np.outer(d, d)
        
        return correlation_matrix


class StrategyBacktester:
    """Backtest trading strategies on generated market data."""
    
    def __init__(self):
        self.commission_rate = 0.001  # 0.1% commission
        self.slippage_bps = 2  # 2 basis points slippage
    
    def backtest_strategy(self, prices: np.ndarray, strategy_params: StrategyParameters) -> Dict[str, Any]:
        """Backtest a strategy on price data."""
        try:
            # Convert prices to returns
            returns = np.diff(np.log(prices))
            
            # Generate trading signals based on strategy
            signals = self._generate_signals(prices, returns, strategy_params)
            
            # Calculate strategy returns
            strategy_returns = self._calculate_strategy_returns(returns, signals, strategy_params)
            
            # Calculate performance metrics
            performance = self._calculate_performance_metrics(strategy_returns, signals)
            
            return performance
            
        except Exception as e:
            logger.error(f"Error in strategy backtesting: {e}")
            return self._get_default_performance()
    
    def _generate_signals(self, prices: np.ndarray, returns: np.ndarray, 
                         strategy_params: StrategyParameters) -> np.ndarray:
        """Generate trading signals based on strategy parameters."""
        signals = np.zeros(len(prices))
        
        # Simple moving average crossover strategy (example)
        if 'sma_short' in strategy_params.parameters and 'sma_long' in strategy_params.parameters:
            short_window = strategy_params.parameters['sma_short']
            long_window = strategy_params.parameters['sma_long']
            
            if len(prices) > long_window:
                sma_short = pd.Series(prices).rolling(short_window).mean()
                sma_long = pd.Series(prices).rolling(long_window).mean()
                
                # Generate signals
                signals[short_window:] = np.where(
                    sma_short[short_window:] > sma_long[short_window:], 1, -1
                )
        
        # Mean reversion strategy (example)
        elif 'lookback_period' in strategy_params.parameters:
            lookback = strategy_params.parameters['lookback_period']
            threshold = strategy_params.parameters.get('threshold', 2.0)
            
            if len(prices) > lookback:
                rolling_mean = pd.Series(prices).rolling(lookback).mean()
                rolling_std = pd.Series(prices).rolling(lookback).std()
                
                z_score = (prices - rolling_mean) / rolling_std
                
                # Generate mean reversion signals
                signals[lookback:] = np.where(
                    z_score[lookback:] > threshold, -1,  # Sell when too high
                    np.where(z_score[lookback:] < -threshold, 1, 0)  # Buy when too low
                )
        
        # Momentum strategy (example)
        elif 'momentum_period' in strategy_params.parameters:
            momentum_period = strategy_params.parameters['momentum_period']
            
            if len(returns) > momentum_period:
                momentum = pd.Series(returns).rolling(momentum_period).sum()
                
                # Generate momentum signals
                signals[momentum_period:] = np.where(momentum[momentum_period:] > 0, 1, -1)
        
        else:
            # Default: buy and hold
            signals[:] = 1
        
        return signals
    
    def _calculate_strategy_returns(self, market_returns: np.ndarray, signals: np.ndarray,
                                  strategy_params: StrategyParameters) -> np.ndarray:
        """Calculate strategy returns including transaction costs."""
        # Shift signals to avoid look-ahead bias
        shifted_signals = np.roll(signals, 1)
        shifted_signals[0] = 0
        
        # Calculate position changes
        position_changes = np.diff(np.concatenate([[0], shifted_signals]))
        
        # Calculate transaction costs
        transaction_costs = np.abs(position_changes) * (self.commission_rate + self.slippage_bps / 10000)
        
        # Calculate gross returns
        gross_returns = shifted_signals[1:] * market_returns
        
        # Subtract transaction costs
        net_returns = gross_returns - transaction_costs[1:]
        
        # Apply risk management
        if 'max_position_size' in strategy_params.risk_management:
            max_position = strategy_params.risk_management['max_position_size']
            net_returns = np.clip(net_returns, -max_position, max_position)
        
        if 'stop_loss' in strategy_params.risk_management:
            stop_loss = strategy_params.risk_management['stop_loss']
            # Apply stop loss (simplified)
            net_returns = np.where(net_returns < -stop_loss, -stop_loss, net_returns)
        
        return net_returns
    
    def _calculate_performance_metrics(self, returns: np.ndarray, signals: np.ndarray) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics."""
        if len(returns) == 0:
            return self._get_default_performance()
        
        # Basic metrics
        total_return = np.prod(1 + returns) - 1
        volatility = np.std(returns) * np.sqrt(252)
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Drawdown analysis
        cumulative_returns = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        # Trade analysis
        position_changes = np.diff(np.concatenate([[0], signals]))
        trades = np.where(np.abs(position_changes) > 0)[0]
        total_trades = len(trades)
        
        # Win rate
        if total_trades > 0:
            trade_returns = []
            for i in range(len(trades) - 1):
                start_idx = trades[i]
                end_idx = trades[i + 1]
                trade_return = np.sum(returns[start_idx:end_idx])
                trade_returns.append(trade_return)
            
            if trade_returns:
                win_rate = np.sum(np.array(trade_returns) > 0) / len(trade_returns)
                profit_factor = np.sum(np.array(trade_returns)[np.array(trade_returns) > 0]) / \
                              abs(np.sum(np.array(trade_returns)[np.array(trade_returns) < 0])) \
                              if np.sum(np.array(trade_returns) < 0) != 0 else np.inf
                avg_trade_duration = np.mean(np.diff(trades))
            else:
                win_rate = 0.0
                profit_factor = 0.0
                avg_trade_duration = 0.0
        else:
            win_rate = 0.0
            profit_factor = 0.0
            avg_trade_duration = 0.0
        
        # Risk metrics
        var_95 = np.percentile(returns, 5)
        cvar_95 = np.mean(returns[returns <= var_95])
        
        # Additional ratios
        calmar_ratio = total_return / abs(max_drawdown) if max_drawdown != 0 else 0
        downside_returns = returns[returns < 0]
        downside_deviation = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = np.mean(returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_trades': total_trades,
            'avg_trade_duration': avg_trade_duration,
            'var_95': var_95,
            'cvar_95': cvar_95,
            'calmar_ratio': calmar_ratio,
            'sortino_ratio': sortino_ratio
        }
    
    def _get_default_performance(self) -> Dict[str, Any]:
        """Return default performance metrics for failed backtests."""
        return {
            'total_return': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0,
            'volatility': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'total_trades': 0,
            'avg_trade_duration': 0.0,
            'var_95': 0.0,
            'cvar_95': 0.0,
            'calmar_ratio': 0.0,
            'sortino_ratio': 0.0
        }


class MonteCarloEngine:
    """Monte Carlo simulation engine for strategy stress testing."""
    
    def __init__(self):
        self.data_generator = MarketDataGenerator()
        self.backtester = StrategyBacktester()
        self.max_workers = min(mp.cpu_count(), 8)  # Limit CPU usage
    
    async def run_monte_carlo_simulation(self, strategy_params: StrategyParameters,
                                       num_simulations: int = None,
                                       simulation_days: int = 252) -> Dict[str, Any]:
        """Run Monte Carlo simulation for strategy stress testing."""
        try:
            if num_simulations is None:
                num_simulations = settings.MONTE_CARLO_SIMULATIONS
            
            logger.info(f"Starting Monte Carlo simulation: {num_simulations} scenarios")
            start_time = datetime.now()
            
            # Generate market scenarios
            scenarios = await self._generate_market_scenarios(num_simulations, simulation_days)
            
            # Run simulations in parallel
            simulation_results = await self._run_parallel_simulations(scenarios, strategy_params)
            
            # Analyze results
            analysis = self._analyze_simulation_results(simulation_results)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'strategy_id': strategy_params.strategy_id,
                'num_simulations': num_simulations,
                'simulation_days': simulation_days,
                'execution_time_seconds': execution_time,
                'scenarios_per_second': num_simulations / execution_time,
                'individual_results': simulation_results,
                'statistical_analysis': analysis,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in Monte Carlo simulation: {e}")
            raise
    
    async def _generate_market_scenarios(self, num_scenarios: int, days: int) -> List[MarketScenario]:
        """Generate diverse market scenarios for simulation."""
        scenarios = []
        
        for i in range(num_scenarios):
            scenario_type = np.random.choice(['gbm', 'regime_switching', 'jump_diffusion'], 
                                           p=[0.4, 0.4, 0.2])
            
            if scenario_type == 'gbm':
                # Geometric Brownian Motion
                mu = np.random.normal(0.08, 0.05)  # Random drift
                sigma = np.random.uniform(0.10, 0.40)  # Random volatility
                prices = self.data_generator.generate_geometric_brownian_motion(days, mu=mu, sigma=sigma)
                returns = np.diff(np.log(prices))
                regime = 'normal'
                
            elif scenario_type == 'regime_switching':
                # Regime switching
                returns, regimes = self.data_generator.generate_regime_switching_returns(days)
                regime = regimes[-1]  # Last regime
                
            else:  # jump_diffusion
                # Jump diffusion
                jump_intensity = np.random.uniform(0.05, 0.20)
                returns = self.data_generator.generate_jump_diffusion_returns(days, jump_intensity)
                regime = 'volatile'
            
            scenario = MarketScenario(
                scenario_id=f"scenario_{i}",
                returns=returns,
                volatility=np.std(returns) * np.sqrt(252),
                trend=np.mean(returns) * 252,
                regime=regime
            )
            scenarios.append(scenario)
        
        return scenarios
    
    async def _run_parallel_simulations(self, scenarios: List[MarketScenario],
                                      strategy_params: StrategyParameters) -> List[SimulationResult]:
        """Run simulations in parallel for better performance."""
        # Prepare simulation tasks
        simulation_tasks = []
        
        # Use asyncio for I/O bound operations and ProcessPoolExecutor for CPU bound
        loop = asyncio.get_event_loop()
        
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all simulations
            futures = []
            for scenario in scenarios:
                future = loop.run_in_executor(
                    executor,
                    self._run_single_simulation,
                    scenario,
                    strategy_params
                )
                futures.append(future)
            
            # Wait for all simulations to complete
            results = await asyncio.gather(*futures, return_exceptions=True)
        
        # Filter out exceptions and return valid results
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Simulation {i} failed: {result}")
            else:
                valid_results.append(result)
        
        return valid_results
    
    def _run_single_simulation(self, scenario: MarketScenario, 
                             strategy_params: StrategyParameters) -> SimulationResult:
        """Run a single Monte Carlo simulation."""
        try:
            # Convert returns to prices
            prices = 100 * np.exp(np.cumsum(np.concatenate([[0], scenario.returns])))
            
            # Backtest strategy
            performance = self.backtester.backtest_strategy(prices, strategy_params)
            
            # Create simulation result
            result = SimulationResult(
                scenario_id=scenario.scenario_id,
                strategy_id=strategy_params.strategy_id,
                total_return=performance['total_return'],
                sharpe_ratio=performance['sharpe_ratio'],
                max_drawdown=performance['max_drawdown'],
                volatility=performance['volatility'],
                win_rate=performance['win_rate'],
                profit_factor=performance['profit_factor'],
                total_trades=performance['total_trades'],
                avg_trade_duration=performance['avg_trade_duration'],
                var_95=performance['var_95'],
                cvar_95=performance['cvar_95'],
                calmar_ratio=performance['calmar_ratio'],
                sortino_ratio=performance['sortino_ratio']
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in single simulation {scenario.scenario_id}: {e}")
            # Return default result
            return SimulationResult(
                scenario_id=scenario.scenario_id,
                strategy_id=strategy_params.strategy_id,
                total_return=0.0,
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                volatility=0.0,
                win_rate=0.0,
                profit_factor=0.0,
                total_trades=0,
                avg_trade_duration=0.0,
                var_95=0.0,
                cvar_95=0.0,
                calmar_ratio=0.0,
                sortino_ratio=0.0
            )
    
    def _analyze_simulation_results(self, results: List[SimulationResult]) -> Dict[str, Any]:
        """Analyze Monte Carlo simulation results."""
        if not results:
            return {"error": "No valid simulation results"}
        
        # Extract metrics
        returns = [r.total_return for r in results]
        sharpe_ratios = [r.sharpe_ratio for r in results]
        max_drawdowns = [r.max_drawdown for r in results]
        win_rates = [r.win_rate for r in results]
        
        # Statistical analysis
        analysis = {
            'total_simulations': len(results),
            'returns_statistics': {
                'mean': np.mean(returns),
                'median': np.median(returns),
                'std': np.std(returns),
                'min': np.min(returns),
                'max': np.max(returns),
                'percentile_5': np.percentile(returns, 5),
                'percentile_95': np.percentile(returns, 95),
                'skewness': stats.skew(returns),
                'kurtosis': stats.kurtosis(returns)
            },
            'sharpe_ratio_statistics': {
                'mean': np.mean(sharpe_ratios),
                'median': np.median(sharpe_ratios),
                'std': np.std(sharpe_ratios),
                'percentile_5': np.percentile(sharpe_ratios, 5),
                'percentile_95': np.percentile(sharpe_ratios, 95)
            },
            'max_drawdown_statistics': {
                'mean': np.mean(max_drawdowns),
                'median': np.median(max_drawdowns),
                'worst': np.min(max_drawdowns),
                'percentile_5': np.percentile(max_drawdowns, 5),
                'percentile_95': np.percentile(max_drawdowns, 95)
            },
            'win_rate_statistics': {
                'mean': np.mean(win_rates),
                'median': np.median(win_rates),
                'std': np.std(win_rates)
            },
            'risk_metrics': {
                'probability_of_loss': np.sum(np.array(returns) < 0) / len(returns),
                'probability_of_large_loss': np.sum(np.array(returns) < -0.20) / len(returns),
                'expected_shortfall_5': np.mean([r for r in returns if r <= np.percentile(returns, 5)]),
                'tail_ratio': np.percentile(returns, 95) / abs(np.percentile(returns, 5))
            }
        }
        
        return analysis
