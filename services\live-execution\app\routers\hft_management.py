"""
HFT Management API Router for Phase 10 Advanced Capabilities

This module provides REST API endpoints for managing High-Frequency Trading
capabilities including ultra-low latency execution, DPDK/FPGA monitoring,
advanced risk management, and Smart Order Routing.

Endpoints:
- GET /hft/status - Get overall HFT system status
- GET /hft/performance - Get HFT performance metrics
- GET /hft/latency - Get latency statistics
- GET /hft/dpdk - Get DPDK status and statistics
- GET /hft/fpga - Get FPGA acceleration metrics
- GET /hft/risk - Get advanced risk management metrics
- GET /hft/sor - Get Smart Order Routing statistics
- POST /hft/circuit-breaker/reset - Reset circuit breakers
- POST /hft/optimize - Trigger performance optimization
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

router = APIRouter()


def get_hft_engines(request: Request):
    """Dependency to get HFT engines from app state."""
    return {
        'ultra_low_latency_engine': getattr(request.app.state, 'ultra_low_latency_engine', None),
        'advanced_risk_manager': getattr(request.app.state, 'advanced_risk_manager', None),
        'smart_order_router': getattr(request.app.state, 'smart_order_router', None)
    }


@router.get("/status")
async def get_hft_status(engines: Dict = Depends(get_hft_engines)):
    """Get overall HFT system status and health."""
    try:
        status = {
            "system_status": "OPERATIONAL",
            "components": {},
            "overall_health": "HEALTHY",
            "timestamp": None
        }

        # Check Ultra-Low Latency Engine
        if engines['ultra_low_latency_engine']:
            ull_engine = engines['ultra_low_latency_engine']
            performance_summary = ull_engine.get_performance_summary()

            status["components"]["ultra_low_latency"] = {
                "status": "ACTIVE" if ull_engine.is_running else "INACTIVE",
                "hft_performance_grade": performance_summary.get('hft_performance_grade', 'Unknown'),
                "average_latency_us": performance_summary.get('hft_metrics', {}).get('average_latency_us', 0),
                "throughput_ops_per_sec": performance_summary.get('overall_metrics', {}).get('throughput_ops_per_sec', 0)
            }

            status["timestamp"] = performance_summary.get('timestamp')

        # Check Advanced Risk Manager
        if engines['advanced_risk_manager']:
            risk_manager = engines['advanced_risk_manager']
            risk_metrics = risk_manager.get_risk_metrics()

            status["components"]["advanced_risk_management"] = {
                "status": "ACTIVE",
                "success_rate_percent": risk_metrics.get('performance_metrics', {}).get('success_rate_percent', 0),
                "avg_latency_us": risk_metrics.get('performance_metrics', {}).get('avg_latency_us', 0),
                "active_circuit_breakers": sum(
                    1 for cb in risk_metrics.get('circuit_breakers', {}).values()
                    if cb.get('is_triggered', False)
                )
            }

        # Check Smart Order Router
        if engines['smart_order_router']:
            sor_stats = await engines['smart_order_router'].get_routing_statistics()

            status["components"]["smart_order_routing"] = {
                "status": "ACTIVE",
                "total_orders_routed": sor_stats.get('total_orders_routed', 0),
                "avg_venues_per_order": sor_stats.get('avg_venues_per_order', 0)
            }

        # Determine overall health
        component_statuses = [comp.get('status') for comp in status["components"].values()]
        if all(s == "ACTIVE" for s in component_statuses):
            status["overall_health"] = "HEALTHY"
        elif any(s == "ACTIVE" for s in component_statuses):
            status["overall_health"] = "DEGRADED"
        else:
            status["overall_health"] = "CRITICAL"
            status["system_status"] = "DEGRADED"

        return status

    except Exception as e:
        logger.error(f"Failed to get HFT status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get HFT status: {str(e)}")


@router.get("/performance")
async def get_hft_performance(engines: Dict = Depends(get_hft_engines)):
    """Get comprehensive HFT performance metrics."""
    try:
        if not engines['ultra_low_latency_engine']:
            raise HTTPException(status_code=503, detail="Ultra-low latency engine not available")

        performance_data = engines['ultra_low_latency_engine'].get_performance_summary()

        # Add additional performance insights
        hft_metrics = performance_data.get('hft_metrics', {})
        overall_metrics = performance_data.get('overall_metrics', {})

        performance_insights = {
            "latency_performance": {
                "grade": "A+" if hft_metrics.get('average_latency_us', 0) < 25 else "A" if hft_metrics.get('average_latency_us', 0) < 50 else "B",
                "target_compliance": hft_metrics.get('average_latency_us', 0) < 50,
                "p95_latency_us": overall_metrics.get('p95_latency_us', 0),
                "p99_latency_us": overall_metrics.get('p99_latency_us', 0)
            },
            "throughput_performance": {
                "current_ops_per_sec": overall_metrics.get('throughput_ops_per_sec', 0),
                "peak_ops_per_sec": hft_metrics.get('peak_throughput_ops_per_sec', 0),
                "target_compliance": hft_metrics.get('peak_throughput_ops_per_sec', 0) >= 100000
            },
            "acceleration_efficiency": {
                "fpga_acceleration_rate": hft_metrics.get('fpga_acceleration_rate', 0),
                "dpdk_packets_sent": hft_metrics.get('dpdk_packets_sent', 0),
                "total_orders_processed": hft_metrics.get('total_orders_processed', 0)
            }
        }

        return {
            "performance_summary": performance_data,
            "performance_insights": performance_insights,
            "recommendations": _generate_performance_recommendations(performance_insights)
        }

    except Exception as e:
        logger.error(f"Failed to get HFT performance: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get HFT performance: {str(e)}")


@router.get("/latency")
async def get_latency_statistics(engines: Dict = Depends(get_hft_engines)):
    """Get detailed latency statistics and analysis."""
    try:
        if not engines['ultra_low_latency_engine']:
            raise HTTPException(status_code=503, detail="Ultra-low latency engine not available")

        ull_engine = engines['ultra_low_latency_engine']

        # Get latency target compliance
        target_results = ull_engine.profiler.check_latency_targets()

        # Get detailed latency metrics
        latency_stats = {
            "target_compliance": target_results,
            "detailed_metrics": {},
            "performance_grade": ull_engine._calculate_hft_performance_grade(),
            "recommendations": []
        }

        # Get per-operation latency metrics
        for operation in ['order_processing', 'market_data', 'risk_check', 'sor_routing']:
            if operation in ull_engine.profiler.operation_stats:
                operation_metrics = ull_engine.profiler.get_performance_metrics(operation)
                latency_stats["detailed_metrics"][operation] = {
                    "avg_latency_us": operation_metrics.avg_latency_ns / 1000,
                    "p50_latency_us": operation_metrics.p50_latency_ns / 1000,
                    "p95_latency_us": operation_metrics.p95_latency_ns / 1000,
                    "p99_latency_us": operation_metrics.p99_latency_ns / 1000,
                    "max_latency_us": operation_metrics.max_latency_ns / 1000,
                    "target_met": target_results.get(operation, True)
                }

        # Generate recommendations
        for operation, target_met in target_results.items():
            if not target_met:
                latency_stats["recommendations"].append(
                    f"Optimize {operation} - latency target not met"
                )

        return latency_stats

    except Exception as e:
        logger.error(f"Failed to get latency statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get latency statistics: {str(e)}")


@router.get("/dpdk")
async def get_dpdk_status(engines: Dict = Depends(get_hft_engines)):
    """Get DPDK status and performance statistics."""
    try:
        if not engines['ultra_low_latency_engine']:
            raise HTTPException(status_code=503, detail="Ultra-low latency engine not available")

        ull_engine = engines['ultra_low_latency_engine']
        dpdk_stats = ull_engine.dpdk_manager.get_statistics()

        return {
            "dpdk_status": dpdk_stats,
            "configuration": {
                "enabled": ull_engine.dpdk_config.enabled,
                "core_mask": ull_engine.dpdk_config.core_mask,
                "memory_channels": ull_engine.dpdk_config.memory_channels,
                "huge_pages": ull_engine.dpdk_config.huge_pages
            },
            "performance_impact": {
                "kernel_bypass_active": dpdk_stats.get('status') == 'ACTIVE',
                "estimated_latency_reduction_us": 10 if dpdk_stats.get('status') == 'ACTIVE' else 0
            }
        }

    except Exception as e:
        logger.error(f"Failed to get DPDK status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get DPDK status: {str(e)}")


@router.get("/fpga")
async def get_fpga_metrics(engines: Dict = Depends(get_hft_engines)):
    """Get FPGA acceleration metrics and status."""
    try:
        if not engines['ultra_low_latency_engine']:
            raise HTTPException(status_code=503, detail="Ultra-low latency engine not available")

        ull_engine = engines['ultra_low_latency_engine']
        fpga_metrics = ull_engine.fpga_accelerator.get_performance_metrics()

        return {
            "fpga_metrics": fpga_metrics,
            "configuration": {
                "enabled": ull_engine.fpga_config.enabled,
                "device_id": ull_engine.fpga_config.device_id,
                "clock_frequency_mhz": ull_engine.fpga_config.clock_frequency_mhz,
                "acceleration_types": [t.value for t in ull_engine.fpga_config.acceleration_types]
            },
            "acceleration_impact": {
                "operations_accelerated": fpga_metrics.get('operations_accelerated', 0),
                "estimated_speedup_factor": fpga_metrics.get('estimated_speedup_factor', 1.0),
                "utilization_percent": fpga_metrics.get('utilization_percent', 0)
            }
        }

    except Exception as e:
        logger.error(f"Failed to get FPGA metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get FPGA metrics: {str(e)}")


def _generate_performance_recommendations(performance_insights: Dict[str, Any]) -> list:
    """Generate performance optimization recommendations."""
    recommendations = []

    # Latency recommendations
    latency_perf = performance_insights.get('latency_performance', {})
    if not latency_perf.get('target_compliance', True):
        recommendations.append("Enable DPDK kernel bypass to reduce latency")
        recommendations.append("Enable FPGA acceleration for critical path operations")

    # Throughput recommendations
    throughput_perf = performance_insights.get('throughput_performance', {})
    if not throughput_perf.get('target_compliance', True):
        recommendations.append("Scale up processing cores for higher throughput")
        recommendations.append("Optimize message queue sizes")

    # Acceleration recommendations
    accel_eff = performance_insights.get('acceleration_efficiency', {})
    if accel_eff.get('fpga_acceleration_rate', 0) < 80:
        recommendations.append("Increase FPGA acceleration utilization")

    return recommendations


@router.get("/risk")
async def get_risk_metrics(engines: Dict = Depends(get_hft_engines)):
    """Get advanced risk management metrics."""
    try:
        if not engines['advanced_risk_manager']:
            raise HTTPException(status_code=503, detail="Advanced risk manager not available")

        risk_metrics = engines['advanced_risk_manager'].get_risk_metrics()

        return {
            "risk_metrics": risk_metrics,
            "risk_assessment": {
                "overall_risk_level": _assess_overall_risk_level(risk_metrics),
                "active_alerts": sum(
                    1 for cb in risk_metrics.get('circuit_breakers', {}).values()
                    if cb.get('is_triggered', False)
                ),
                "performance_grade": _calculate_risk_performance_grade(risk_metrics)
            }
        }

    except Exception as e:
        logger.error(f"Failed to get risk metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get risk metrics: {str(e)}")


@router.get("/sor")
async def get_sor_statistics(engines: Dict = Depends(get_hft_engines)):
    """Get Smart Order Routing statistics."""
    try:
        if not engines['smart_order_router']:
            raise HTTPException(status_code=503, detail="Smart Order Router not available")

        sor_stats = await engines['smart_order_router'].get_routing_statistics()

        return {
            "routing_statistics": sor_stats,
            "performance_analysis": {
                "routing_efficiency": _calculate_routing_efficiency(sor_stats),
                "venue_diversification": len(sor_stats.get('venue_usage_distribution', {})),
                "optimization_opportunities": _identify_sor_optimizations(sor_stats)
            }
        }

    except Exception as e:
        logger.error(f"Failed to get SOR statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get SOR statistics: {str(e)}")


@router.post("/circuit-breaker/reset")
async def reset_circuit_breakers(engines: Dict = Depends(get_hft_engines)):
    """Reset all circuit breakers."""
    try:
        if not engines['advanced_risk_manager']:
            raise HTTPException(status_code=503, detail="Advanced risk manager not available")

        risk_manager = engines['advanced_risk_manager']
        reset_count = 0

        # Reset all circuit breakers
        for cb_type, circuit_breaker in risk_manager.circuit_breakers.items():
            if circuit_breaker.is_triggered:
                circuit_breaker.is_triggered = False
                circuit_breaker.current_value = 0
                reset_count += 1
                logger.info(f"Reset circuit breaker: {cb_type}")

        return {
            "status": "success",
            "message": f"Reset {reset_count} circuit breakers",
            "reset_count": reset_count,
            "timestamp": risk_manager.get_risk_metrics().get('timestamp')
        }

    except Exception as e:
        logger.error(f"Failed to reset circuit breakers: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to reset circuit breakers: {str(e)}")


@router.post("/optimize")
async def trigger_optimization(engines: Dict = Depends(get_hft_engines)):
    """Trigger HFT performance optimization."""
    try:
        optimization_results = {
            "status": "completed",
            "optimizations_applied": [],
            "performance_impact": {},
            "timestamp": None
        }

        # Optimize Ultra-Low Latency Engine
        if engines['ultra_low_latency_engine']:
            ull_engine = engines['ultra_low_latency_engine']

            # Apply CPU optimizations
            ull_engine.cpu_manager.optimize_for_trading()
            optimization_results["optimizations_applied"].append("CPU affinity optimization")

            # Get current performance metrics
            performance_summary = ull_engine.get_performance_summary()
            optimization_results["performance_impact"]["latency_us"] = performance_summary.get('hft_metrics', {}).get('average_latency_us', 0)
            optimization_results["performance_impact"]["throughput_ops_per_sec"] = performance_summary.get('overall_metrics', {}).get('throughput_ops_per_sec', 0)
            optimization_results["timestamp"] = performance_summary.get('timestamp')

        # Optimize Smart Order Router
        if engines['smart_order_router']:
            # In production, this would trigger ML model retraining
            optimization_results["optimizations_applied"].append("SOR venue selection optimization")

        return optimization_results

    except Exception as e:
        logger.error(f"Failed to trigger optimization: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger optimization: {str(e)}")


def _assess_overall_risk_level(risk_metrics: Dict[str, Any]) -> str:
    """Assess overall risk level based on metrics."""
    try:
        circuit_breakers = risk_metrics.get('circuit_breakers', {})
        active_breakers = sum(1 for cb in circuit_breakers.values() if cb.get('is_triggered', False))

        performance_metrics = risk_metrics.get('performance_metrics', {})
        success_rate = performance_metrics.get('success_rate_percent', 100)
        avg_latency = performance_metrics.get('avg_latency_us', 0)

        if active_breakers > 2 or success_rate < 90:
            return "HIGH"
        elif active_breakers > 0 or success_rate < 95 or avg_latency > 10:
            return "MEDIUM"
        else:
            return "LOW"

    except Exception:
        return "UNKNOWN"


def _calculate_risk_performance_grade(risk_metrics: Dict[str, Any]) -> str:
    """Calculate risk management performance grade."""
    try:
        performance_metrics = risk_metrics.get('performance_metrics', {})
        success_rate = performance_metrics.get('success_rate_percent', 0)
        avg_latency = performance_metrics.get('avg_latency_us', 0)

        if success_rate >= 99 and avg_latency <= 5:
            return "A+ (Excellent)"
        elif success_rate >= 95 and avg_latency <= 10:
            return "A (Good)"
        elif success_rate >= 90 and avg_latency <= 20:
            return "B (Acceptable)"
        else:
            return "C (Needs Improvement)"

    except Exception:
        return "Unknown"


def _calculate_routing_efficiency(sor_stats: Dict[str, Any]) -> float:
    """Calculate Smart Order Routing efficiency."""
    try:
        total_orders = sor_stats.get('total_orders_routed', 0)
        avg_venues = sor_stats.get('avg_venues_per_order', 0)

        if total_orders == 0:
            return 0.0

        # Efficiency based on venue diversification and order count
        efficiency = min((avg_venues / 3.0) * 100, 100.0)  # Normalize to 3 venues as optimal
        return round(efficiency, 2)

    except Exception:
        return 0.0


def _identify_sor_optimizations(sor_stats: Dict[str, Any]) -> list:
    """Identify Smart Order Routing optimization opportunities."""
    optimizations = []

    try:
        venue_distribution = sor_stats.get('venue_usage_distribution', {})
        avg_venues = sor_stats.get('avg_venues_per_order', 0)

        # Check venue diversification
        if len(venue_distribution) < 3:
            optimizations.append("Increase venue connectivity for better diversification")

        # Check venue utilization balance
        if venue_distribution:
            max_usage = max(venue_distribution.values())
            if max_usage > 0.7:  # 70% concentration in one venue
                optimizations.append("Rebalance venue allocation to reduce concentration risk")

        # Check average venues per order
        if avg_venues < 1.5:
            optimizations.append("Increase order fragmentation for better execution")
        elif avg_venues > 4:
            optimizations.append("Reduce excessive order fragmentation")

        if not optimizations:
            optimizations.append("SOR performance is optimal")

    except Exception:
        optimizations.append("Unable to analyze SOR performance")

    return optimizations
