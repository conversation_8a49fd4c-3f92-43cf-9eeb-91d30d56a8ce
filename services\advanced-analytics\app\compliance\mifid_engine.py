"""
AthenaTrader Phase 4: MiFID II Compliance Engine

Core implementation of Markets in Financial Instruments Directive II (MiFID II)
compliance engine for European financial markets regulation.

Key Components:
- MiFIDIIComplianceEngine: Main compliance validation engine
- BestExecutionEngine: Best execution monitoring and reporting
- TransactionReportingEngine: Transaction reporting to competent authorities
- MarketAbuseSurveillanceEngine: Market abuse detection and monitoring
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal
import json
import hashlib

from ..core.logging_config import get_logger
from ..models.compliance import MiFIDTransaction, BestExecutionReport, ComplianceStatus

# Setup logging
logger = get_logger(__name__)
compliance_logger = get_logger("compliance.mifid.engine")


class MiFIDIIComplianceEngine:
    """
    MiFID II compliance validation engine.
    
    Implements comprehensive MiFID II compliance checks including
    transaction reporting, best execution, market abuse surveillance,
    and client protection measures.
    """
    
    def __init__(self):
        """Initialize MiFID II compliance engine."""
        self.reporting_deadline_hours = 24  # T+1 reporting
        self.best_execution = BestExecutionEngine()
        self.transaction_reporting = TransactionReportingEngine()
        self.market_abuse = MarketAbuseSurveillanceEngine()
        
        # MiFID II configuration
        self.config = {
            'transaction_reporting_deadline_hours': self.reporting_deadline_hours,
            'competent_authorities': ['FCA', 'BAFIN', 'AMF', 'CONSOB', 'CNMV'],
            'client_categories': ['RETAIL', 'PROFESSIONAL', 'ELIGIBLE_COUNTERPARTY'],
            'surveillance_patterns': ['LAYERING', 'SPOOFING', 'WASH_TRADING', 'MOMENTUM_IGNITION'],
            'best_execution_venues': ['LSE', 'XETRA', 'EURONEXT', 'BATS', 'TURQUOISE']
        }
        
        compliance_logger.info("MiFID II Compliance Engine initialized")
    
    async def validate_transaction_reporting(
        self,
        trade_id: str,
        instrument_type: str,
        quantity: Decimal,
        price: Decimal,
        execution_timestamp: datetime,
        venue: str
    ) -> Dict[str, Any]:
        """
        Validate MiFID II transaction reporting requirements.
        
        Implements Article 26 transaction reporting obligations
        with T+1 deadline compliance validation.
        """
        try:
            compliance_logger.info(f"Validating MiFID II transaction reporting for trade {trade_id}")
            
            validation_result = {
                'trade_id': trade_id,
                'requires_reporting': False,
                'reporting_deadline': None,
                'competent_authority': None,
                'status': 'COMPLIANT',
                'violations': [],
                'recommendations': []
            }
            
            # Check if instrument requires reporting
            requires_reporting = await self._requires_transaction_reporting(
                instrument_type, quantity, price, venue
            )
            validation_result['requires_reporting'] = requires_reporting
            
            if requires_reporting:
                # Calculate T+1 reporting deadline
                reporting_deadline = execution_timestamp + timedelta(hours=self.reporting_deadline_hours)
                validation_result['reporting_deadline'] = reporting_deadline
                
                # Determine competent authority
                competent_authority = await self._determine_competent_authority(venue)
                validation_result['competent_authority'] = competent_authority
                
                # Check if deadline has passed
                if datetime.now() > reporting_deadline:
                    validation_result['violations'].append('Late transaction reporting - MiFID II requires T+1 reporting')
                    validation_result['status'] = 'NON_COMPLIANT'
                    validation_result['recommendations'].append('Submit transaction report immediately')
                
                # Validate required fields
                missing_fields = await self._validate_transaction_reporting_fields({
                    'trade_id': trade_id,
                    'instrument_type': instrument_type,
                    'quantity': quantity,
                    'price': price,
                    'venue': venue
                })
                
                if missing_fields:
                    validation_result['violations'].extend([f"Missing required field: {field}" for field in missing_fields])
                    validation_result['status'] = 'NON_COMPLIANT'
            
            compliance_logger.info(
                f"MiFID II transaction reporting validation completed for trade {trade_id} - "
                f"Status: {validation_result['status']}, Requires Reporting: {requires_reporting}"
            )
            
            return validation_result
            
        except Exception as e:
            logger.error(f"MiFID II transaction reporting validation failed for trade {trade_id}: {e}")
            raise
    
    async def market_abuse_surveillance(
        self,
        trade_id: str,
        client_id: str,
        instrument_id: str,
        quantity: Decimal,
        price: Decimal,
        execution_timestamp: datetime
    ) -> Dict[str, Any]:
        """
        Perform market abuse surveillance under Market Abuse Regulation (MAR).
        
        Detects suspicious trading patterns and generates alerts
        for potential market manipulation.
        """
        try:
            return await self.market_abuse.analyze_transaction(
                trade_id=trade_id,
                client_id=client_id,
                instrument_id=instrument_id,
                quantity=quantity,
                price=price,
                execution_timestamp=execution_timestamp
            )
            
        except Exception as e:
            logger.error(f"Market abuse surveillance failed for trade {trade_id}: {e}")
            raise
    
    async def validate_client_protection(
        self,
        client_id: str,
        client_category: str,
        instrument_type: str,
        trade_value: Decimal
    ) -> Dict[str, Any]:
        """
        Validate MiFID II client protection measures.
        
        Implements investor protection requirements including
        suitability assessments and appropriateness tests.
        """
        try:
            compliance_logger.info(f"Validating client protection for client {client_id}")
            
            protection_result = {
                'client_id': client_id,
                'status': 'COMPLIANT',
                'violations': [],
                'recommendations': [],
                'suitability_assessment': None,
                'appropriateness_test': None
            }
            
            # Validate client categorization
            if client_category not in self.config['client_categories']:
                protection_result['violations'].append(f"Invalid client category: {client_category}")
                protection_result['status'] = 'NON_COMPLIANT'
            
            # Perform suitability assessment for investment advice
            if client_category == 'RETAIL':
                suitability = await self._perform_suitability_assessment(
                    client_id, instrument_type, trade_value
                )
                protection_result['suitability_assessment'] = suitability
                
                if not suitability['suitable']:
                    protection_result['violations'].append('Investment not suitable for retail client')
                    protection_result['status'] = 'NON_COMPLIANT'
                    protection_result['recommendations'].append('Provide suitability warning or decline transaction')
            
            # Perform appropriateness test for execution-only services
            appropriateness = await self._perform_appropriateness_test(
                client_id, instrument_type
            )
            protection_result['appropriateness_test'] = appropriateness
            
            if not appropriateness['appropriate'] and client_category == 'RETAIL':
                protection_result['recommendations'].append('Provide appropriateness warning to client')
            
            compliance_logger.info(
                f"Client protection validation completed for client {client_id} - "
                f"Status: {protection_result['status']}"
            )
            
            return protection_result
            
        except Exception as e:
            logger.error(f"Client protection validation failed for client {client_id}: {e}")
            raise
    
    async def calculate_risk_score(
        self,
        reporting_status: Dict[str, Any],
        best_execution_status: Dict[str, Any],
        market_abuse_status: Dict[str, Any],
        client_protection_status: Dict[str, Any]
    ) -> float:
        """Calculate overall MiFID II compliance risk score."""
        risk_score = 0.0
        
        # Transaction reporting risk
        if reporting_status.get('status') == 'NON_COMPLIANT':
            risk_score += 0.3
        
        # Best execution risk
        if best_execution_status.get('status') == 'NON_COMPLIANT':
            risk_score += 0.25
        
        # Market abuse risk
        if market_abuse_status.get('suspicious', False):
            risk_score += 0.35
        
        # Client protection risk
        if client_protection_status.get('status') == 'NON_COMPLIANT':
            risk_score += 0.1
        
        return min(risk_score, 1.0)
    
    async def get_market_abuse_alerts(
        self,
        start_date: datetime,
        end_date: datetime,
        alert_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get market abuse surveillance alerts."""
        return await self.market_abuse.get_alerts(start_date, end_date, alert_type)
    
    async def _requires_transaction_reporting(
        self,
        instrument_type: str,
        quantity: Decimal,
        price: Decimal,
        venue: str
    ) -> bool:
        """Determine if transaction requires MiFID II reporting."""
        # All financial instruments traded on EU venues require reporting
        eu_venues = ['LSE', 'XETRA', 'EURONEXT', 'BATS', 'TURQUOISE']
        return venue in eu_venues
    
    async def _determine_competent_authority(self, venue: str) -> str:
        """Determine competent authority based on trading venue."""
        venue_authority_map = {
            'LSE': 'FCA',
            'XETRA': 'BAFIN',
            'EURONEXT': 'AMF',
            'BATS': 'FCA',
            'TURQUOISE': 'FCA'
        }
        return venue_authority_map.get(venue, 'FCA')
    
    async def _validate_transaction_reporting_fields(self, trade_data: Dict[str, Any]) -> List[str]:
        """Validate required MiFID II transaction reporting fields."""
        required_fields = [
            'trade_id', 'instrument_type', 'quantity', 'price', 'venue'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in trade_data or trade_data[field] is None:
                missing_fields.append(field)
        
        return missing_fields
    
    async def _perform_suitability_assessment(
        self,
        client_id: str,
        instrument_type: str,
        trade_value: Decimal
    ) -> Dict[str, Any]:
        """Perform MiFID II suitability assessment."""
        # Simplified suitability assessment
        return {
            'suitable': True,
            'risk_tolerance': 'MODERATE',
            'investment_objectives': 'GROWTH',
            'knowledge_experience': 'ADEQUATE',
            'assessment_date': datetime.now()
        }
    
    async def _perform_appropriateness_test(
        self,
        client_id: str,
        instrument_type: str
    ) -> Dict[str, Any]:
        """Perform MiFID II appropriateness test."""
        # Simplified appropriateness test
        complex_instruments = ['DERIVATIVE', 'STRUCTURED_PRODUCT']
        is_complex = instrument_type in complex_instruments
        
        return {
            'appropriate': not is_complex,
            'instrument_complexity': 'COMPLEX' if is_complex else 'NON_COMPLEX',
            'client_knowledge': 'ADEQUATE',
            'test_date': datetime.now()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of MiFID II compliance engine."""
        try:
            # Check engine components
            best_execution_status = await self.best_execution.health_check()
            reporting_status = await self.transaction_reporting.health_check()
            market_abuse_status = await self.market_abuse.health_check()
            
            return {
                'status': 'healthy',
                'best_execution_engine': best_execution_status,
                'transaction_reporting_engine': reporting_status,
                'market_abuse_engine': market_abuse_status,
                'config': self.config
            }
            
        except Exception as e:
            logger.error(f"MiFID II engine health check failed: {e}")
            return {'status': 'unhealthy', 'error': str(e)}


class BestExecutionEngine:
    """
    MiFID II best execution monitoring and reporting engine.
    
    Implements RTS 28 best execution reporting requirements
    and venue performance analysis.
    """
    
    def __init__(self):
        """Initialize best execution engine."""
        self.execution_venues = [
            'LSE', 'XETRA', 'EURONEXT', 'BATS', 'TURQUOISE'
        ]
        
        compliance_logger.info("Best Execution Engine initialized")
    
    async def assess_best_execution(
        self,
        trade_id: str,
        instrument_id: str,
        quantity: Decimal,
        price: Decimal,
        venue: str,
        order_type: str,
        client_category: str
    ) -> Dict[str, Any]:
        """
        Assess best execution compliance for a trade.
        
        Evaluates execution quality against MiFID II
        best execution requirements.
        """
        try:
            compliance_logger.info(f"Assessing best execution for trade {trade_id}")
            
            assessment = {
                'trade_id': trade_id,
                'status': 'COMPLIANT',
                'execution_quality_score': 0.0,
                'venue_ranking': None,
                'price_improvement': None,
                'violations': [],
                'recommendations': []
            }
            
            # Calculate execution quality score
            quality_score = await self._calculate_execution_quality(
                instrument_id, quantity, price, venue, order_type
            )
            assessment['execution_quality_score'] = quality_score
            
            # Get venue ranking for instrument
            venue_ranking = await self._get_venue_ranking(instrument_id)
            assessment['venue_ranking'] = venue_ranking
            
            # Check if venue is in top tier for instrument
            if venue not in venue_ranking[:3]:  # Top 3 venues
                assessment['violations'].append('Execution venue not in top tier for instrument')
                assessment['status'] = 'NON_COMPLIANT'
                assessment['recommendations'].append('Consider routing to higher-ranked venue')
            
            # Calculate price improvement
            price_improvement = await self._calculate_price_improvement(
                instrument_id, price, venue, order_type
            )
            assessment['price_improvement'] = price_improvement
            
            if price_improvement < 0:  # Price deterioration
                assessment['recommendations'].append('Review execution venue selection for better pricing')
            
            compliance_logger.info(
                f"Best execution assessment completed for trade {trade_id} - "
                f"Status: {assessment['status']}, Quality Score: {quality_score}"
            )
            
            return assessment
            
        except Exception as e:
            logger.error(f"Best execution assessment failed for trade {trade_id}: {e}")
            raise
    
    async def generate_best_execution_report(
        self,
        client_id: str,
        period_start: datetime,
        period_end: datetime
    ) -> Dict[str, Any]:
        """
        Generate RTS 28 best execution report for a client.
        
        Provides detailed analysis of execution quality
        and venue performance as required by MiFID II RTS 28.
        """
        try:
            compliance_logger.info(f"Generating best execution report for client {client_id}")
            
            report = {
                'client_id': client_id,
                'period_start': period_start,
                'period_end': period_end,
                'venue_analysis': {},
                'execution_metrics': {},
                'recommendations': []
            }
            
            # Analyze execution by venue
            for venue in self.execution_venues:
                venue_analysis = await self._analyze_venue_performance(
                    client_id, venue, period_start, period_end
                )
                report['venue_analysis'][venue] = venue_analysis
            
            # Calculate overall execution metrics
            execution_metrics = await self._calculate_execution_metrics(
                client_id, period_start, period_end
            )
            report['execution_metrics'] = execution_metrics
            
            # Generate recommendations
            recommendations = await self._generate_execution_recommendations(
                report['venue_analysis'], execution_metrics
            )
            report['recommendations'] = recommendations
            
            compliance_logger.info(f"Best execution report generated for client {client_id}")
            
            return report
            
        except Exception as e:
            logger.error(f"Best execution report generation failed for client {client_id}: {e}")
            raise
    
    async def _calculate_execution_quality(
        self,
        instrument_id: str,
        quantity: Decimal,
        price: Decimal,
        venue: str,
        order_type: str
    ) -> float:
        """Calculate execution quality score (0.0-1.0)."""
        # Simplified quality calculation
        base_score = 0.8
        
        # Adjust for venue quality
        venue_scores = {
            'LSE': 0.95, 'XETRA': 0.92, 'EURONEXT': 0.90,
            'BATS': 0.88, 'TURQUOISE': 0.85
        }
        venue_adjustment = venue_scores.get(venue, 0.7) - 0.8
        
        # Adjust for order type
        order_type_scores = {
            'MARKET': 0.9, 'LIMIT': 0.95, 'STOP': 0.85
        }
        order_adjustment = order_type_scores.get(order_type, 0.8) - 0.8
        
        return min(max(base_score + venue_adjustment + order_adjustment, 0.0), 1.0)
    
    async def _get_venue_ranking(self, instrument_id: str) -> List[str]:
        """Get venue ranking for instrument based on execution quality."""
        # Simplified venue ranking
        return ['LSE', 'XETRA', 'EURONEXT', 'BATS', 'TURQUOISE']
    
    async def _calculate_price_improvement(
        self,
        instrument_id: str,
        execution_price: Decimal,
        venue: str,
        order_type: str
    ) -> Decimal:
        """Calculate price improvement vs. market benchmark."""
        # Simplified price improvement calculation
        # In production, this would compare against NBBO or other benchmarks
        return Decimal('0.001')  # 0.1 basis points improvement
    
    async def _analyze_venue_performance(
        self,
        client_id: str,
        venue: str,
        period_start: datetime,
        period_end: datetime
    ) -> Dict[str, Any]:
        """Analyze venue performance for client over period."""
        return {
            'venue': venue,
            'total_trades': 150,
            'total_volume': Decimal('10000000'),
            'average_execution_quality': 0.92,
            'price_improvement': Decimal('0.0015'),
            'fill_rate': 0.98,
            'speed_of_execution_ms': 45
        }
    
    async def _calculate_execution_metrics(
        self,
        client_id: str,
        period_start: datetime,
        period_end: datetime
    ) -> Dict[str, Any]:
        """Calculate overall execution metrics for client."""
        return {
            'total_trades': 750,
            'total_volume': Decimal('50000000'),
            'weighted_average_quality': 0.91,
            'overall_price_improvement': Decimal('0.0012'),
            'best_execution_compliance_rate': 0.96
        }
    
    async def _generate_execution_recommendations(
        self,
        venue_analysis: Dict[str, Any],
        execution_metrics: Dict[str, Any]
    ) -> List[str]:
        """Generate execution improvement recommendations."""
        recommendations = []
        
        if execution_metrics['best_execution_compliance_rate'] < 0.95:
            recommendations.append('Improve venue selection algorithm to enhance best execution compliance')
        
        if execution_metrics['overall_price_improvement'] < Decimal('0.001'):
            recommendations.append('Consider additional venues or smart order routing to improve pricing')
        
        return recommendations
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for best execution engine."""
        return {
            'status': 'healthy',
            'venues_monitored': len(self.execution_venues),
            'execution_engine': 'operational'
        }


class TransactionReportingEngine:
    """MiFID II transaction reporting engine."""
    
    def __init__(self):
        """Initialize transaction reporting engine."""
        self.competent_authorities = {
            'FCA': 'https://fca.org.uk/api',
            'BAFIN': 'https://bafin.de/api',
            'AMF': 'https://amf-france.org/api',
            'CONSOB': 'https://consob.it/api'
        }
        
        compliance_logger.info("Transaction Reporting Engine initialized")
    
    async def submit_transaction_report(
        self,
        trade_id: str,
        competent_authority: str
    ) -> Dict[str, Any]:
        """Submit MiFID II transaction report to competent authority."""
        try:
            # Generate transaction report
            report_data = await self._generate_transaction_report(trade_id)
            
            # Submit to competent authority (simulated)
            submission_result = await self._submit_to_authority(competent_authority, report_data)
            
            compliance_logger.info(f"Transaction report submitted for trade {trade_id}")
            
            return {
                'report_id': submission_result['report_id'],
                'competent_authority': competent_authority,
                'timestamp': datetime.now(),
                'acknowledgment': submission_result.get('acknowledgment')
            }
            
        except Exception as e:
            logger.error(f"Transaction report submission failed for trade {trade_id}: {e}")
            raise
    
    async def _generate_transaction_report(self, trade_id: str) -> Dict[str, Any]:
        """Generate MiFID II-compliant transaction report."""
        return {
            'trade_id': trade_id,
            'report_type': 'NEW',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0'
        }
    
    async def _submit_to_authority(self, authority: str, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """Submit report to competent authority."""
        # Simulated submission
        return {
            'report_id': f"MIFID_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'acknowledgment': 'ACCEPTED'
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for transaction reporting engine."""
        return {
            'status': 'healthy',
            'authorities_configured': len(self.competent_authorities),
            'reporting_engine': 'operational'
        }


class MarketAbuseSurveillanceEngine:
    """Market abuse surveillance engine for MAR compliance."""
    
    def __init__(self):
        """Initialize market abuse surveillance engine."""
        self.surveillance_patterns = [
            'LAYERING', 'SPOOFING', 'WASH_TRADING', 'MOMENTUM_IGNITION'
        ]
        
        compliance_logger.info("Market Abuse Surveillance Engine initialized")
    
    async def analyze_transaction(
        self,
        trade_id: str,
        client_id: str,
        instrument_id: str,
        quantity: Decimal,
        price: Decimal,
        execution_timestamp: datetime
    ) -> Dict[str, Any]:
        """Analyze transaction for market abuse patterns."""
        try:
            analysis = {
                'trade_id': trade_id,
                'suspicious': False,
                'patterns_detected': [],
                'risk_score': 0.0,
                'status': 'CLEAN',
                'recommendations': []
            }
            
            # Check for suspicious patterns
            for pattern in self.surveillance_patterns:
                pattern_detected = await self._check_pattern(
                    pattern, client_id, instrument_id, quantity, price, execution_timestamp
                )
                
                if pattern_detected['detected']:
                    analysis['patterns_detected'].append({
                        'pattern': pattern,
                        'confidence': pattern_detected['confidence'],
                        'details': pattern_detected['details']
                    })
                    analysis['risk_score'] += pattern_detected['risk_contribution']
            
            # Determine overall status
            if analysis['risk_score'] > 0.7:
                analysis['suspicious'] = True
                analysis['status'] = 'SUSPICIOUS'
                analysis['recommendations'].append('Manual review required for potential market abuse')
            elif analysis['risk_score'] > 0.3:
                analysis['status'] = 'REVIEW'
                analysis['recommendations'].append('Enhanced monitoring recommended')
            
            return analysis
            
        except Exception as e:
            logger.error(f"Market abuse analysis failed for trade {trade_id}: {e}")
            raise
    
    async def get_alerts(
        self,
        start_date: datetime,
        end_date: datetime,
        alert_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get market abuse surveillance alerts."""
        # Simulated alert retrieval
        return [
            {
                'alert_id': 'MAR_001',
                'pattern': 'LAYERING',
                'client_id': 'CLIENT_001',
                'instrument_id': 'AAPL',
                'detection_timestamp': datetime.now() - timedelta(hours=2),
                'severity': 'HIGH',
                'status': 'OPEN'
            }
        ]
    
    async def _check_pattern(
        self,
        pattern: str,
        client_id: str,
        instrument_id: str,
        quantity: Decimal,
        price: Decimal,
        execution_timestamp: datetime
    ) -> Dict[str, Any]:
        """Check for specific market abuse pattern."""
        # Simplified pattern detection
        return {
            'detected': False,
            'confidence': 0.0,
            'risk_contribution': 0.0,
            'details': f"No {pattern} pattern detected"
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for market abuse surveillance engine."""
        return {
            'status': 'healthy',
            'patterns_monitored': len(self.surveillance_patterns),
            'surveillance_engine': 'operational'
        }
