"""
AthenaTrader Phase 4: EMIR Compliance Engine

Core implementation of European Market Infrastructure Regulation (EMIR)
compliance engine for derivatives trading and reporting.

Key Components:
- EMIRComplianceEngine: Main compliance validation engine
- EMIRReportingEngine: Trade repository reporting engine
- MarginCalculationEngine: EMIR margin requirements calculator
- LEIValidationEngine: Legal Entity Identifier validation
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal
import json
import hashlib

from ..core.logging_config import get_logger
from ..models.compliance import EMIRTransaction, EMIRReport, ComplianceStatus

# Setup logging
logger = get_logger(__name__)
compliance_logger = get_logger("compliance.emir.engine")


class EMIRComplianceEngine:
    """
    EMIR compliance validation engine.
    
    Implements comprehensive EMIR compliance checks including
    reporting requirements, margin calculations, and risk assessments.
    """
    
    def __init__(self):
        """Initialize EMIR compliance engine."""
        self.reporting_threshold = Decimal('1000000')  # €1M threshold
        self.lei_registry = LEIValidationEngine()
        self.margin_calculator = MarginCalculationEngine()
        
        # EMIR configuration
        self.config = {
            'reporting_frequency': 'daily',
            'required_fields': [
                'trade_id', 'execution_timestamp', 'counterparty', 'notional_amount',
                'currency', 'maturity_date', 'underlying_asset'
            ],
            'trade_repositories': [
                'DTCC_GTR', 'REGIS_TR', 'UPI_TR', 'KDPW_TR'
            ]
        }
        
        compliance_logger.info("EMIR Compliance Engine initialized")
    
    async def validate_transaction(
        self,
        trade_id: str,
        counterparty_id: str,
        instrument_type: str,
        notional_amount: Decimal,
        currency: str,
        execution_timestamp: datetime,
        maturity_date: Optional[datetime],
        underlying_asset: str,
        clearing_status: str
    ) -> Dict[str, Any]:
        """
        Validate EMIR compliance for a derivatives transaction.
        
        Performs comprehensive EMIR compliance validation including
        reporting requirements, field validation, and risk assessment.
        """
        try:
            compliance_logger.info(f"Validating EMIR compliance for trade {trade_id}")
            
            # Initialize validation result
            validation_result = {
                'trade_id': trade_id,
                'status': 'COMPLIANT',
                'risk_score': 0.0,
                'violations': [],
                'recommendations': [],
                'checks_performed': []
            }
            
            # Check if derivative instrument
            is_derivative = await self._is_derivative_instrument(instrument_type)
            validation_result['checks_performed'].append('derivative_classification')
            
            if not is_derivative:
                validation_result['status'] = 'EXEMPT'
                validation_result['recommendations'].append('Not a derivative instrument - EMIR does not apply')
                return validation_result
            
            # Validate required fields
            missing_fields = await self._validate_required_fields({
                'trade_id': trade_id,
                'counterparty': counterparty_id,
                'notional_amount': notional_amount,
                'currency': currency,
                'execution_timestamp': execution_timestamp,
                'maturity_date': maturity_date,
                'underlying_asset': underlying_asset
            })
            validation_result['checks_performed'].append('required_fields_validation')
            
            if missing_fields:
                validation_result['violations'].extend([f"Missing required field: {field}" for field in missing_fields])
                validation_result['risk_score'] += 0.3
            
            # Check reporting requirements
            requires_reporting = await self.requires_trade_reporting(
                instrument_type, notional_amount, counterparty_id
            )
            validation_result['checks_performed'].append('reporting_requirements')
            validation_result['requires_reporting'] = requires_reporting
            
            # Validate LEI identifier
            lei_valid = await self.lei_registry.validate_lei(counterparty_id)
            validation_result['checks_performed'].append('lei_validation')
            
            if requires_reporting and not lei_valid:
                validation_result['violations'].append('Invalid or missing LEI identifier for reporting counterparty')
                validation_result['risk_score'] += 0.4
            
            # Check timing requirements (T+1 reporting)
            if requires_reporting:
                reporting_deadline = execution_timestamp + timedelta(days=1)
                if datetime.now() > reporting_deadline:
                    validation_result['violations'].append('Late reporting - EMIR requires T+1 reporting')
                    validation_result['risk_score'] += 0.5
            
            # Validate clearing status
            clearing_validation = await self._validate_clearing_status(
                instrument_type, notional_amount, clearing_status
            )
            validation_result['checks_performed'].append('clearing_validation')
            
            if clearing_validation['violations']:
                validation_result['violations'].extend(clearing_validation['violations'])
                validation_result['risk_score'] += clearing_validation['risk_score']
            
            # Determine final compliance status
            if validation_result['violations']:
                validation_result['status'] = 'NON_COMPLIANT'
            elif validation_result['risk_score'] > 0.7:
                validation_result['status'] = 'HIGH_RISK'
            
            compliance_logger.info(
                f"EMIR validation completed for trade {trade_id} - Status: {validation_result['status']}"
            )
            
            return validation_result
            
        except Exception as e:
            logger.error(f"EMIR validation failed for trade {trade_id}: {e}")
            raise
    
    async def calculate_margin_requirements(
        self,
        instrument_type: str,
        notional_amount: Decimal,
        counterparty_id: str,
        clearing_status: str
    ) -> Dict[str, Any]:
        """Calculate EMIR margin requirements for non-centrally cleared derivatives."""
        try:
            if clearing_status.upper() == 'CENTRALLY_CLEARED':
                return {
                    'initial_margin': Decimal('0'),
                    'variation_margin': Decimal('0'),
                    'reason': 'Centrally cleared derivatives exempt from bilateral margin requirements'
                }
            
            # Calculate margin using EMIR RTS
            margin_calc = await self.margin_calculator.calculate_emir_margin(
                instrument_type=instrument_type,
                notional_amount=notional_amount,
                counterparty_id=counterparty_id
            )
            
            return margin_calc
            
        except Exception as e:
            logger.error(f"EMIR margin calculation failed: {e}")
            raise
    
    async def requires_trade_reporting(
        self,
        instrument_type: str,
        notional_amount: Decimal,
        counterparty_id: str
    ) -> bool:
        """Determine if trade requires EMIR reporting."""
        try:
            # Check if derivative
            if not await self._is_derivative_instrument(instrument_type):
                return False
            
            # Check if counterparty is EU entity
            is_eu_counterparty = await self._is_eu_counterparty(counterparty_id)
            
            # EMIR reporting applies to derivatives with at least one EU counterparty
            return is_eu_counterparty
            
        except Exception as e:
            logger.error(f"EMIR reporting requirement check failed: {e}")
            return False
    
    async def validate_lei_identifier(self, counterparty_id: str) -> Dict[str, Any]:
        """Validate Legal Entity Identifier (LEI)."""
        return await self.lei_registry.validate_lei_detailed(counterparty_id)
    
    async def generate_recommendations(
        self,
        compliance_result: Dict[str, Any],
        margin_requirements: Dict[str, Any],
        lei_validation: Dict[str, Any]
    ) -> List[str]:
        """Generate compliance recommendations based on validation results."""
        recommendations = []
        
        if compliance_result.get('violations'):
            recommendations.append("Address compliance violations before trade execution")
        
        if not lei_validation.get('valid', False):
            recommendations.append("Obtain valid LEI identifier for counterparty")
        
        if margin_requirements.get('initial_margin', 0) > 0:
            recommendations.append("Ensure adequate collateral for margin requirements")
        
        if compliance_result.get('risk_score', 0) > 0.5:
            recommendations.append("Consider additional risk mitigation measures")
        
        return recommendations
    
    async def _is_derivative_instrument(self, instrument_type: str) -> bool:
        """Check if instrument is a derivative under EMIR."""
        derivative_types = ['IRS', 'CDS', 'FRA', 'SWAP', 'OPTION', 'FUTURE']
        return instrument_type.upper() in derivative_types
    
    async def _validate_required_fields(self, trade_data: Dict[str, Any]) -> List[str]:
        """Validate required EMIR fields."""
        missing_fields = []
        
        for field in self.config['required_fields']:
            if field not in trade_data or trade_data[field] is None:
                missing_fields.append(field)
        
        return missing_fields
    
    async def _is_eu_counterparty(self, counterparty_id: str) -> bool:
        """Check if counterparty is EU entity."""
        # Simplified check - in production, this would query LEI registry
        # and check jurisdiction codes
        return True  # Assume EU counterparty for demo
    
    async def _validate_clearing_status(
        self,
        instrument_type: str,
        notional_amount: Decimal,
        clearing_status: str
    ) -> Dict[str, Any]:
        """Validate clearing obligations under EMIR."""
        result = {
            'violations': [],
            'risk_score': 0.0
        }
        
        # Check clearing obligation for standardized derivatives
        if await self._is_clearing_mandatory(instrument_type, notional_amount):
            if clearing_status.upper() != 'CENTRALLY_CLEARED':
                result['violations'].append('Clearing obligation not met for standardized derivative')
                result['risk_score'] = 0.8
        
        return result
    
    async def _is_clearing_mandatory(self, instrument_type: str, notional_amount: Decimal) -> bool:
        """Check if clearing is mandatory for the derivative."""
        # Simplified logic - in production, this would check ESMA clearing obligations
        mandatory_types = ['IRS', 'CDS']
        return instrument_type.upper() in mandatory_types and notional_amount >= Decimal('1000000')
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check of EMIR compliance engine."""
        try:
            # Check engine components
            lei_status = await self.lei_registry.health_check()
            margin_status = await self.margin_calculator.health_check()
            
            return {
                'status': 'healthy',
                'lei_registry': lei_status,
                'margin_calculator': margin_status,
                'config': self.config
            }
            
        except Exception as e:
            logger.error(f"EMIR engine health check failed: {e}")
            return {'status': 'unhealthy', 'error': str(e)}


class EMIRReportingEngine:
    """EMIR trade repository reporting engine."""
    
    def __init__(self):
        """Initialize EMIR reporting engine."""
        self.trade_repositories = {
            'DTCC_GTR': 'https://gtr.dtcc.com/api',
            'REGIS_TR': 'https://www.regis-tr.com/api',
            'UPI_TR': 'https://www.upi-tr.com/api'
        }
        
        compliance_logger.info("EMIR Reporting Engine initialized")
    
    async def submit_trade_report(self, trade_id: str, report_type: str) -> Dict[str, Any]:
        """Submit trade report to authorized trade repository."""
        try:
            # Select appropriate trade repository
            repository = await self._select_trade_repository(trade_id)
            
            # Generate report
            report_data = await self._generate_emir_report(trade_id, report_type)
            
            # Submit to trade repository (simulated)
            submission_result = await self._submit_to_repository(repository, report_data)
            
            compliance_logger.info(f"EMIR report submitted for trade {trade_id}")
            
            return {
                'report_id': submission_result['report_id'],
                'repository': repository,
                'timestamp': datetime.now(),
                'acknowledgment': submission_result.get('acknowledgment')
            }
            
        except Exception as e:
            logger.error(f"EMIR report submission failed for trade {trade_id}: {e}")
            raise
    
    async def get_trade_reports(self, trade_id: str) -> List[Dict[str, Any]]:
        """Get EMIR reporting history for a trade."""
        # Simulated report history
        return [
            {
                'report_id': f"EMIR_{trade_id}_001",
                'report_type': 'NEW',
                'submission_timestamp': datetime.now() - timedelta(hours=1),
                'repository': 'DTCC_GTR',
                'status': 'ACCEPTED'
            }
        ]
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """Check connectivity to trade repositories."""
        connectivity_status = {}
        
        for repo_name, repo_url in self.trade_repositories.items():
            try:
                # Simulated connectivity check
                connectivity_status[repo_name] = {
                    'status': 'connected',
                    'url': repo_url,
                    'last_check': datetime.now()
                }
            except Exception as e:
                connectivity_status[repo_name] = {
                    'status': 'disconnected',
                    'error': str(e),
                    'last_check': datetime.now()
                }
        
        return connectivity_status
    
    async def _select_trade_repository(self, trade_id: str) -> str:
        """Select appropriate trade repository for submission."""
        # Simplified selection logic
        return 'DTCC_GTR'
    
    async def _generate_emir_report(self, trade_id: str, report_type: str) -> Dict[str, Any]:
        """Generate EMIR-compliant trade report."""
        return {
            'trade_id': trade_id,
            'report_type': report_type,
            'timestamp': datetime.now().isoformat(),
            'version': '1.0'
        }
    
    async def _submit_to_repository(self, repository: str, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """Submit report to trade repository."""
        # Simulated submission
        return {
            'report_id': f"RPT_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'acknowledgment': 'ACCEPTED'
        }


class MarginCalculationEngine:
    """EMIR margin requirements calculation engine."""
    
    def __init__(self):
        """Initialize margin calculation engine."""
        compliance_logger.info("EMIR Margin Calculation Engine initialized")
    
    async def calculate_emir_margin(
        self,
        instrument_type: str,
        notional_amount: Decimal,
        counterparty_id: str
    ) -> Dict[str, Any]:
        """Calculate EMIR margin requirements."""
        # Simplified margin calculation
        base_margin_rate = Decimal('0.02')  # 2% base rate
        
        initial_margin = notional_amount * base_margin_rate
        variation_margin = notional_amount * Decimal('0.01')  # 1% variation margin
        
        return {
            'initial_margin': initial_margin,
            'variation_margin': variation_margin,
            'threshold_amount': Decimal('50000'),  # €50k threshold
            'minimum_transfer_amount': Decimal('500000'),  # €500k minimum transfer
            'eligible_collateral': ['CASH', 'GOVERNMENT_BONDS', 'CORPORATE_BONDS'],
            'margin_period_of_risk': 10  # 10 days MPOR
        }
    
    async def calculate_counterparty_margin(
        self,
        counterparty_id: str,
        calculation_date: datetime
    ) -> Dict[str, Any]:
        """Calculate total margin requirements for a counterparty."""
        # Aggregate margin calculation across all trades
        return await self.calculate_emir_margin('IRS', Decimal('10000000'), counterparty_id)
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for margin calculation engine."""
        return {'status': 'healthy', 'calculation_engine': 'operational'}


class LEIValidationEngine:
    """Legal Entity Identifier validation engine."""
    
    def __init__(self):
        """Initialize LEI validation engine."""
        compliance_logger.info("LEI Validation Engine initialized")
    
    async def validate_lei(self, lei_code: str) -> bool:
        """Validate LEI code format and status."""
        # Simplified LEI validation
        return len(lei_code) == 20 and lei_code.isalnum()
    
    async def validate_lei_detailed(self, lei_code: str) -> Dict[str, Any]:
        """Detailed LEI validation with entity information."""
        is_valid = await self.validate_lei(lei_code)
        
        return {
            'lei_code': lei_code,
            'valid': is_valid,
            'entity_name': 'Sample Entity' if is_valid else None,
            'jurisdiction': 'EU' if is_valid else None,
            'status': 'ACTIVE' if is_valid else 'INVALID',
            'last_updated': datetime.now()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check for LEI validation engine."""
        return {'status': 'healthy', 'lei_registry': 'connected'}
