"""
AthenaTrader Phase 11 Advanced Analytics Engine - Agent-Based Market Simulator

PRIORITY 2: Enhanced Backtesting Engine with Agent-Based Simulation
- Multi-agent market simulation with realistic trader behaviors
- High-fidelity order book reconstruction
- Monte Carlo stress testing framework
- Walk-forward optimization engine
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import random
from abc import ABC, abstractmethod
from ..core.config import BacktestingConfig

logger = logging.getLogger("backtesting")


class AgentType(Enum):
    """Types of trading agents in the simulation."""
    MARKET_MAKER = "market_maker"
    INSTITUTIONAL = "institutional"
    RETAIL = "retail"
    ALGORITHMIC = "algorithmic"


class OrderType(Enum):
    """Order types in the simulation."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderSide(Enum):
    """Order sides."""
    BUY = "buy"
    SELL = "sell"


@dataclass
class Order:
    """Order representation in the simulation."""
    id: str
    agent_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    filled_quantity: float = 0.0
    status: str = "pending"  # pending, filled, cancelled, partial


@dataclass
class Trade:
    """Trade execution representation."""
    id: str
    buy_order_id: str
    sell_order_id: str
    symbol: str
    price: float
    quantity: float
    timestamp: datetime
    buyer_agent_id: str
    seller_agent_id: str


@dataclass
class MarketData:
    """Market data snapshot."""
    symbol: str
    timestamp: datetime
    bid_price: float
    ask_price: float
    bid_size: float
    ask_size: float
    last_price: float
    volume: float


class TradingAgent(ABC):
    """Abstract base class for trading agents."""
    
    def __init__(self, agent_id: str, agent_type: AgentType, initial_cash: float, initial_position: float = 0.0):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.cash = initial_cash
        self.position = initial_position
        self.orders = []
        self.trades = []
        self.pnl = 0.0
        
        # Agent-specific parameters
        self.risk_tolerance = random.uniform(0.1, 0.9)
        self.trading_frequency = random.uniform(0.1, 1.0)
        self.order_size_preference = random.uniform(0.5, 2.0)
    
    @abstractmethod
    async def generate_orders(self, market_data: MarketData, order_book: Dict[str, Any]) -> List[Order]:
        """Generate orders based on market conditions."""
        pass
    
    def update_position(self, trade: Trade):
        """Update agent position after trade execution."""
        if trade.buyer_agent_id == self.agent_id:
            self.position += trade.quantity
            self.cash -= trade.price * trade.quantity
        elif trade.seller_agent_id == self.agent_id:
            self.position -= trade.quantity
            self.cash += trade.price * trade.quantity
        
        self.trades.append(trade)
        self._calculate_pnl(trade.price)
    
    def _calculate_pnl(self, current_price: float):
        """Calculate unrealized P&L."""
        self.pnl = self.position * current_price + self.cash - (self.cash + self.position * current_price)


class MarketMakerAgent(TradingAgent):
    """Market maker agent that provides liquidity."""
    
    def __init__(self, agent_id: str, initial_cash: float):
        super().__init__(agent_id, AgentType.MARKET_MAKER, initial_cash)
        self.spread_target = random.uniform(0.001, 0.005)  # 0.1% to 0.5% spread
        self.inventory_target = 0.0
        self.max_inventory = initial_cash * 0.1  # 10% of cash as max inventory
    
    async def generate_orders(self, market_data: MarketData, order_book: Dict[str, Any]) -> List[Order]:
        """Generate bid/ask orders to provide liquidity."""
        orders = []
        
        # Calculate fair value (mid-price)
        mid_price = (market_data.bid_price + market_data.ask_price) / 2
        
        # Adjust spread based on volatility and inventory
        volatility_adjustment = min(market_data.volume / 10000, 2.0)  # Higher volume = wider spread
        inventory_adjustment = abs(self.position) / self.max_inventory
        
        adjusted_spread = self.spread_target * (1 + volatility_adjustment + inventory_adjustment)
        
        # Generate bid order
        bid_price = mid_price * (1 - adjusted_spread / 2)
        bid_size = random.uniform(100, 1000) * self.order_size_preference
        
        bid_order = Order(
            id=f"mm_bid_{self.agent_id}_{datetime.now().timestamp()}",
            agent_id=self.agent_id,
            symbol=market_data.symbol,
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=bid_size,
            price=bid_price
        )
        orders.append(bid_order)
        
        # Generate ask order
        ask_price = mid_price * (1 + adjusted_spread / 2)
        ask_size = random.uniform(100, 1000) * self.order_size_preference
        
        ask_order = Order(
            id=f"mm_ask_{self.agent_id}_{datetime.now().timestamp()}",
            agent_id=self.agent_id,
            symbol=market_data.symbol,
            side=OrderSide.SELL,
            order_type=OrderType.LIMIT,
            quantity=ask_size,
            price=ask_price
        )
        orders.append(ask_order)
        
        return orders


class InstitutionalAgent(TradingAgent):
    """Institutional trader with large orders and sophisticated strategies."""
    
    def __init__(self, agent_id: str, initial_cash: float):
        super().__init__(agent_id, AgentType.INSTITUTIONAL, initial_cash)
        self.target_position = random.uniform(-initial_cash * 0.5, initial_cash * 0.5)
        self.execution_strategy = random.choice(["twap", "vwap", "implementation_shortfall"])
        self.order_split_size = random.uniform(0.1, 0.3)  # Split large orders
    
    async def generate_orders(self, market_data: MarketData, order_book: Dict[str, Any]) -> List[Order]:
        """Generate institutional-style orders."""
        orders = []
        
        # Calculate position difference
        position_diff = self.target_position - self.position
        
        if abs(position_diff) < 100:  # Small difference, no action
            return orders
        
        # Determine order side
        side = OrderSide.BUY if position_diff > 0 else OrderSide.SELL
        
        # Calculate order size (split large orders)
        total_size = abs(position_diff)
        order_size = min(total_size * self.order_split_size, total_size)
        
        # Choose execution strategy
        if self.execution_strategy == "twap":
            # Time-weighted average price - use limit orders near mid
            mid_price = (market_data.bid_price + market_data.ask_price) / 2
            price_offset = random.uniform(-0.001, 0.001)  # Small price improvement
            order_price = mid_price * (1 + price_offset)
        
        elif self.execution_strategy == "vwap":
            # Volume-weighted average price - use market orders for immediate execution
            order_price = None  # Market order
        
        else:  # implementation_shortfall
            # Balance market impact vs. timing risk
            if random.random() < 0.7:  # 70% limit orders
                price_offset = random.uniform(-0.002, 0.002)
                mid_price = (market_data.bid_price + market_data.ask_price) / 2
                order_price = mid_price * (1 + price_offset)
            else:  # 30% market orders
                order_price = None
        
        order = Order(
            id=f"inst_{self.agent_id}_{datetime.now().timestamp()}",
            agent_id=self.agent_id,
            symbol=market_data.symbol,
            side=side,
            order_type=OrderType.LIMIT if order_price else OrderType.MARKET,
            quantity=order_size,
            price=order_price
        )
        orders.append(order)
        
        return orders


class RetailAgent(TradingAgent):
    """Retail trader with behavioral biases and smaller orders."""
    
    def __init__(self, agent_id: str, initial_cash: float):
        super().__init__(agent_id, AgentType.RETAIL, initial_cash)
        self.momentum_bias = random.uniform(0.1, 0.9)  # Tendency to follow trends
        self.loss_aversion = random.uniform(1.5, 3.0)  # Loss aversion coefficient
        self.overconfidence = random.uniform(0.8, 1.5)  # Overconfidence in predictions
        self.herding_tendency = random.uniform(0.1, 0.8)  # Follow other traders
    
    async def generate_orders(self, market_data: MarketData, order_book: Dict[str, Any]) -> List[Order]:
        """Generate retail-style orders with behavioral biases."""
        orders = []
        
        # Random trading decision (retail traders don't trade continuously)
        if random.random() > self.trading_frequency:
            return orders
        
        # Calculate recent price momentum
        # In a real implementation, this would use historical price data
        price_momentum = random.uniform(-0.05, 0.05)  # Simulated momentum
        
        # Apply momentum bias
        momentum_signal = price_momentum * self.momentum_bias
        
        # Apply herding behavior (follow market sentiment)
        market_sentiment = random.uniform(-1, 1)  # Simulated market sentiment
        herding_signal = market_sentiment * self.herding_tendency
        
        # Combine signals
        total_signal = momentum_signal + herding_signal
        
        # Apply overconfidence (amplify signal)
        adjusted_signal = total_signal * self.overconfidence
        
        # Generate order if signal is strong enough
        if abs(adjusted_signal) > 0.1:
            side = OrderSide.BUY if adjusted_signal > 0 else OrderSide.SELL
            
            # Retail order size (smaller than institutional)
            order_size = random.uniform(10, 500) * self.order_size_preference
            
            # Retail traders often use market orders or limit orders close to market
            if random.random() < 0.6:  # 60% market orders
                order_type = OrderType.MARKET
                order_price = None
            else:  # 40% limit orders
                order_type = OrderType.LIMIT
                mid_price = (market_data.bid_price + market_data.ask_price) / 2
                price_offset = random.uniform(-0.005, 0.005)
                order_price = mid_price * (1 + price_offset)
            
            order = Order(
                id=f"retail_{self.agent_id}_{datetime.now().timestamp()}",
                agent_id=self.agent_id,
                symbol=market_data.symbol,
                side=side,
                order_type=order_type,
                quantity=order_size,
                price=order_price
            )
            orders.append(order)
        
        return orders


class OrderBook:
    """Order book implementation for the simulation."""
    
    def __init__(self, symbol: str):
        self.symbol = symbol
        self.bids = []  # List of (price, quantity, order_id)
        self.asks = []  # List of (price, quantity, order_id)
        self.orders = {}  # order_id -> Order
        self.trades = []
    
    def add_order(self, order: Order) -> List[Trade]:
        """Add order to book and return any resulting trades."""
        trades = []
        
        if order.order_type == OrderType.MARKET:
            trades = self._execute_market_order(order)
        elif order.order_type == OrderType.LIMIT:
            trades = self._add_limit_order(order)
        
        return trades
    
    def _execute_market_order(self, order: Order) -> List[Trade]:
        """Execute market order against best available prices."""
        trades = []
        remaining_quantity = order.quantity
        
        if order.side == OrderSide.BUY:
            # Buy market order - match against asks
            self.asks.sort(key=lambda x: x[0])  # Sort by price (ascending)
            
            while remaining_quantity > 0 and self.asks:
                best_ask = self.asks[0]
                ask_price, ask_quantity, ask_order_id = best_ask
                
                # Determine trade quantity
                trade_quantity = min(remaining_quantity, ask_quantity)
                
                # Create trade
                trade = Trade(
                    id=f"trade_{datetime.now().timestamp()}_{random.randint(1000, 9999)}",
                    buy_order_id=order.id,
                    sell_order_id=ask_order_id,
                    symbol=order.symbol,
                    price=ask_price,
                    quantity=trade_quantity,
                    timestamp=datetime.now(),
                    buyer_agent_id=order.agent_id,
                    seller_agent_id=self.orders[ask_order_id].agent_id
                )
                trades.append(trade)
                
                # Update quantities
                remaining_quantity -= trade_quantity
                order.filled_quantity += trade_quantity
                
                # Update or remove ask
                if ask_quantity > trade_quantity:
                    self.asks[0] = (ask_price, ask_quantity - trade_quantity, ask_order_id)
                    self.orders[ask_order_id].filled_quantity += trade_quantity
                else:
                    self.asks.pop(0)
                    self.orders[ask_order_id].status = "filled"
        
        else:  # SELL
            # Sell market order - match against bids
            self.bids.sort(key=lambda x: x[0], reverse=True)  # Sort by price (descending)
            
            while remaining_quantity > 0 and self.bids:
                best_bid = self.bids[0]
                bid_price, bid_quantity, bid_order_id = best_bid
                
                # Determine trade quantity
                trade_quantity = min(remaining_quantity, bid_quantity)
                
                # Create trade
                trade = Trade(
                    id=f"trade_{datetime.now().timestamp()}_{random.randint(1000, 9999)}",
                    buy_order_id=bid_order_id,
                    sell_order_id=order.id,
                    symbol=order.symbol,
                    price=bid_price,
                    quantity=trade_quantity,
                    timestamp=datetime.now(),
                    buyer_agent_id=self.orders[bid_order_id].agent_id,
                    seller_agent_id=order.agent_id
                )
                trades.append(trade)
                
                # Update quantities
                remaining_quantity -= trade_quantity
                order.filled_quantity += trade_quantity
                
                # Update or remove bid
                if bid_quantity > trade_quantity:
                    self.bids[0] = (bid_price, bid_quantity - trade_quantity, bid_order_id)
                    self.orders[bid_order_id].filled_quantity += trade_quantity
                else:
                    self.bids.pop(0)
                    self.orders[bid_order_id].status = "filled"
        
        # Update order status
        if order.filled_quantity >= order.quantity:
            order.status = "filled"
        elif order.filled_quantity > 0:
            order.status = "partial"
        
        return trades
    
    def _add_limit_order(self, order: Order) -> List[Trade]:
        """Add limit order to book, executing against existing orders if possible."""
        trades = []
        
        # First, try to execute against existing orders
        if order.side == OrderSide.BUY:
            # Check if we can execute against asks
            executable_asks = [ask for ask in self.asks if ask[0] <= order.price]
            if executable_asks:
                # Convert to market order for execution
                market_order = Order(
                    id=order.id,
                    agent_id=order.agent_id,
                    symbol=order.symbol,
                    side=order.side,
                    order_type=OrderType.MARKET,
                    quantity=order.quantity
                )
                trades = self._execute_market_order(market_order)
                order.filled_quantity = market_order.filled_quantity
                order.status = market_order.status
        
        else:  # SELL
            # Check if we can execute against bids
            executable_bids = [bid for bid in self.bids if bid[0] >= order.price]
            if executable_bids:
                # Convert to market order for execution
                market_order = Order(
                    id=order.id,
                    agent_id=order.agent_id,
                    symbol=order.symbol,
                    side=order.side,
                    order_type=OrderType.MARKET,
                    quantity=order.quantity
                )
                trades = self._execute_market_order(market_order)
                order.filled_quantity = market_order.filled_quantity
                order.status = market_order.status
        
        # Add remaining quantity to book if not fully filled
        remaining_quantity = order.quantity - order.filled_quantity
        if remaining_quantity > 0:
            self.orders[order.id] = order
            
            if order.side == OrderSide.BUY:
                self.bids.append((order.price, remaining_quantity, order.id))
                self.bids.sort(key=lambda x: x[0], reverse=True)  # Sort by price (descending)
            else:
                self.asks.append((order.price, remaining_quantity, order.id))
                self.asks.sort(key=lambda x: x[0])  # Sort by price (ascending)
        
        return trades
    
    def get_best_bid_ask(self) -> Tuple[Optional[float], Optional[float]]:
        """Get best bid and ask prices."""
        best_bid = max(self.bids, key=lambda x: x[0])[0] if self.bids else None
        best_ask = min(self.asks, key=lambda x: x[0])[0] if self.asks else None
        return best_bid, best_ask
    
    def get_market_data(self) -> MarketData:
        """Get current market data snapshot."""
        best_bid, best_ask = self.get_best_bid_ask()
        
        # Calculate sizes
        bid_size = sum(bid[1] for bid in self.bids) if self.bids else 0
        ask_size = sum(ask[1] for ask in self.asks) if self.asks else 0
        
        # Last trade price
        last_price = self.trades[-1].price if self.trades else (best_bid + best_ask) / 2 if best_bid and best_ask else 100.0
        
        # Volume (sum of recent trades)
        recent_volume = sum(trade.quantity for trade in self.trades[-100:]) if self.trades else 0
        
        return MarketData(
            symbol=self.symbol,
            timestamp=datetime.now(),
            bid_price=best_bid or last_price * 0.999,
            ask_price=best_ask or last_price * 1.001,
            bid_size=bid_size,
            ask_size=ask_size,
            last_price=last_price,
            volume=recent_volume
        )


class AgentBasedSimulator:
    """Main agent-based market simulator."""
    
    def __init__(self, symbol: str, num_agents: int = None):
        self.symbol = symbol
        self.order_book = OrderBook(symbol)
        self.agents = []
        self.simulation_time = datetime.now()
        self.all_trades = []
        
        # Default agent distribution
        if num_agents is None:
            num_agents = BacktestingConfig.AGENT_SIMULATION_AGENTS
        
        self._create_agents(num_agents)
    
    def _create_agents(self, num_agents: int):
        """Create agents with realistic distribution."""
        # Calculate agent distribution
        num_market_makers = int(num_agents * BacktestingConfig.MARKET_MAKER_RATIO)
        num_institutional = int(num_agents * BacktestingConfig.INSTITUTIONAL_RATIO)
        num_retail = num_agents - num_market_makers - num_institutional
        
        # Create market makers
        for i in range(num_market_makers):
            agent = MarketMakerAgent(f"mm_{i}", initial_cash=1_000_000)
            self.agents.append(agent)
        
        # Create institutional traders
        for i in range(num_institutional):
            agent = InstitutionalAgent(f"inst_{i}", initial_cash=10_000_000)
            self.agents.append(agent)
        
        # Create retail traders
        for i in range(num_retail):
            agent = RetailAgent(f"retail_{i}", initial_cash=50_000)
            self.agents.append(agent)
        
        logger.info(f"Created {len(self.agents)} agents: {num_market_makers} MM, {num_institutional} Inst, {num_retail} Retail")
    
    async def run_simulation(self, duration_minutes: int = 60, time_step_seconds: int = 1) -> Dict[str, Any]:
        """Run the agent-based simulation."""
        try:
            logger.info(f"Starting agent-based simulation for {duration_minutes} minutes")
            start_time = datetime.now()
            
            simulation_results = {
                "symbol": self.symbol,
                "start_time": start_time.isoformat(),
                "duration_minutes": duration_minutes,
                "agents_count": len(self.agents),
                "trades": [],
                "market_data_snapshots": [],
                "agent_performance": {}
            }
            
            # Run simulation steps
            total_steps = duration_minutes * 60 // time_step_seconds
            
            for step in range(total_steps):
                # Update simulation time
                self.simulation_time = start_time + timedelta(seconds=step * time_step_seconds)
                
                # Get current market data
                market_data = self.order_book.get_market_data()
                
                # Generate orders from all agents
                all_orders = []
                for agent in self.agents:
                    try:
                        agent_orders = await agent.generate_orders(market_data, self.order_book.__dict__)
                        all_orders.extend(agent_orders)
                    except Exception as e:
                        logger.error(f"Error generating orders for agent {agent.agent_id}: {e}")
                
                # Process orders and execute trades
                step_trades = []
                for order in all_orders:
                    try:
                        trades = self.order_book.add_order(order)
                        step_trades.extend(trades)
                        
                        # Update agent positions
                        for trade in trades:
                            for agent in self.agents:
                                if agent.agent_id in [trade.buyer_agent_id, trade.seller_agent_id]:
                                    agent.update_position(trade)
                    except Exception as e:
                        logger.error(f"Error processing order {order.id}: {e}")
                
                # Store results
                self.all_trades.extend(step_trades)
                simulation_results["trades"].extend([
                    {
                        "id": trade.id,
                        "price": trade.price,
                        "quantity": trade.quantity,
                        "timestamp": trade.timestamp.isoformat(),
                        "buyer_agent_id": trade.buyer_agent_id,
                        "seller_agent_id": trade.seller_agent_id
                    }
                    for trade in step_trades
                ])
                
                # Store market data snapshots (every 60 seconds)
                if step % 60 == 0:
                    simulation_results["market_data_snapshots"].append({
                        "timestamp": self.simulation_time.isoformat(),
                        "bid_price": market_data.bid_price,
                        "ask_price": market_data.ask_price,
                        "last_price": market_data.last_price,
                        "volume": market_data.volume,
                        "spread": market_data.ask_price - market_data.bid_price
                    })
                
                # Small delay to prevent overwhelming
                if step % 100 == 0:
                    await asyncio.sleep(0.001)
            
            # Calculate agent performance
            final_market_data = self.order_book.get_market_data()
            for agent in self.agents:
                agent._calculate_pnl(final_market_data.last_price)
                simulation_results["agent_performance"][agent.agent_id] = {
                    "agent_type": agent.agent_type.value,
                    "final_cash": agent.cash,
                    "final_position": agent.position,
                    "pnl": agent.pnl,
                    "trades_count": len(agent.trades),
                    "total_volume": sum(trade.quantity for trade in agent.trades)
                }
            
            # Calculate simulation statistics
            simulation_results.update({
                "end_time": datetime.now().isoformat(),
                "total_trades": len(simulation_results["trades"]),
                "total_volume": sum(trade["quantity"] for trade in simulation_results["trades"]),
                "price_range": {
                    "min": min(trade["price"] for trade in simulation_results["trades"]) if simulation_results["trades"] else 0,
                    "max": max(trade["price"] for trade in simulation_results["trades"]) if simulation_results["trades"] else 0
                },
                "average_spread": np.mean([snap["spread"] for snap in simulation_results["market_data_snapshots"]]) if simulation_results["market_data_snapshots"] else 0
            })
            
            logger.info(f"Simulation completed: {len(simulation_results['trades'])} trades executed")
            return simulation_results
            
        except Exception as e:
            logger.error(f"Error in agent-based simulation: {e}")
            raise
