{"timestamp": "2025-05-25T21:38:12.213615", "hostname": "DESKTOP-K6O9OV4", "platform": "Windows-11-10.0.22631-SP0", "cpu_info": {"model": "Intel(R) Xeon(R) Gold 6248R CPU @ 3.00GHz", "cores": 48, "threads": 96, "frequency_mhz": 3000, "cache_sizes": {"L1": "32KB", "L2": "1MB", "L3": "35.75MB"}, "features": ["avx2", "sse4_2", "rdtscp", "tsx"], "numa_nodes": 2, "isolated_cores": ["0", "1"], "governor": "performance"}, "memory_info": {"total_gb": 128, "available_gb": 120, "huge_pages_2mb": 1024, "huge_pages_1gb": 8, "huge_pages_free": 1020, "numa_memory": {"0": 65536, "1": 65536}}, "network_interfaces": [{"pci_address": "0000:3b:00.0", "device_id": "1572", "vendor_id": "8086", "device_name": "Intel X710 10GbE", "driver": "i40e", "driver_version": "2.19.3", "firmware_version": "8.30 0x8000a49d", "link_speed": "10000Mb/s", "numa_node": 0, "dpdk_compatible": true, "serial_number": "68:05:CA:3D:15:99", "mac_address": "68:05:ca:3d:15:99"}, {"pci_address": "0000:3b:00.1", "device_id": "1572", "vendor_id": "8086", "device_name": "Intel X710 10GbE", "driver": "i40e", "driver_version": "2.19.3", "firmware_version": "8.30 0x8000a49d", "link_speed": "10000Mb/s", "numa_node": 0, "dpdk_compatible": true, "serial_number": "68:05:CA:3D:15:9A", "mac_address": "68:05:ca:3d:15:9a"}], "fpga_devices": [{"pci_address": "0000:d8:00.0", "device_id": "5001", "vendor_id": "10ee", "device_name": "Xilinx Alveo U250", "driver": "xclmgmt", "driver_version": "2.14.354", "firmware_version": "1.4.0", "numa_node": 1, "fpga_compatible": true, "serial_number": "XFL1RKH9P05A", "board_name": "xilinx_u250_gen3x16_xdma_4_1_202210_1", "shell_version": "4.1"}], "system_info": {"os_name": "Windows", "os_version": "10.0.22631", "kernel_version": "11", "iommu_enabled": true, "python_version": "3.13.2", "cpu_count": 16, "memory_total_gb": 31.92517852783203}, "compliance_status": {"cpu_performance_governor": true, "cpu_isolated_cores": true, "cpu_avx2_support": true, "cpu_numa_nodes": true, "memory_sufficient": true, "huge_pages_configured": true, "dpdk_nics_available": true, "network_numa_distributed": true, "fpga_devices_available": true}, "simulation_mode": true}