#!/usr/bin/env python3
"""
AthenaTrader Phase 10 Compliance Validation Script

This script validates regulatory compliance and risk management standards
for HFT deployment, ensuring adherence to:

- MiFID II requirements
- FINRA regulations
- Institutional risk management standards
- Circuit breaker functionality
- Position limit enforcement
- Audit trail requirements

Usage:
    python compliance_validation.py --test-type [risk|circuit-breakers|audit|all]
"""

import asyncio
import time
import json
import logging
import uuid
import sys
import os
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
from decimal import Decimal

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.engine.advanced_risk_manager import AdvancedRiskManager, CircuitBreakerType, RiskLevel
from app.schemas.execution import OrderRequest, OrderSide, OrderType, AssetClass

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class ComplianceTestResult:
    """Individual compliance test result."""
    test_name: str
    test_category: str
    passed: bool
    details: str
    execution_time_ms: float
    regulatory_requirement: str


@dataclass
class ComplianceValidationResults:
    """Complete compliance validation results."""
    timestamp: str
    test_duration_seconds: float
    total_tests: int
    passed_tests: int
    failed_tests: int
    compliance_score: float
    test_results: List[ComplianceTestResult]
    regulatory_compliance: Dict[str, bool]
    overall_status: str


class ComplianceValidator:
    """Regulatory compliance validation for HFT systems."""

    def __init__(self):
        """Initialize compliance validator."""
        self.risk_manager = None
        self.validation_results = None
        self.test_results = []

        logger.info("Compliance validator initialized")

    async def setup_test_environment(self) -> bool:
        """Setup test environment for compliance validation."""
        logger.info("Setting up compliance test environment...")

        try:
            # Initialize risk manager with FPGA disabled for testing
            from app.engine.ultra_low_latency import FPGAConfiguration
            fpga_config = FPGAConfiguration(enabled=False)

            self.risk_manager = AdvancedRiskManager(fpga_config)
            await self.risk_manager.initialize()

            logger.info("Compliance test environment setup completed")
            return True

        except Exception as e:
            logger.error(f"Compliance test environment setup failed: {e}")
            return False

    async def cleanup_test_environment(self):
        """Cleanup test environment."""
        logger.info("Cleaning up compliance test environment...")

        try:
            if self.risk_manager:
                await self.risk_manager.cleanup()

            logger.info("Compliance test environment cleanup completed")

        except Exception as e:
            logger.error(f"Compliance test environment cleanup failed: {e}")

    async def run_risk_management_tests(self) -> List[ComplianceTestResult]:
        """Run risk management compliance tests."""
        logger.info("Running risk management compliance tests...")

        risk_tests = []

        # Test 1: Position limit enforcement (MiFID II)
        risk_tests.append(await self._test_position_limit_enforcement())

        # Test 2: Loss limit enforcement (FINRA)
        risk_tests.append(await self._test_loss_limit_enforcement())

        # Test 3: Risk check latency compliance
        risk_tests.append(await self._test_risk_check_latency())

        # Test 4: Order validation completeness
        risk_tests.append(await self._test_order_validation_completeness())

        # Test 5: Risk scoring accuracy
        risk_tests.append(await self._test_risk_scoring_accuracy())

        return risk_tests

    async def _test_position_limit_enforcement(self) -> ComplianceTestResult:
        """Test position limit enforcement compliance."""
        start_time = time.time()

        try:
            # Create order that should exceed position limits
            large_order = OrderRequest(
                symbol="AAPL",
                quantity=Decimal("1000000"),  # Very large quantity
                price=Decimal("150.0"),
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            result = await self.risk_manager.validate_order_ultra_fast(large_order)

            # Should be rejected due to position limits
            passed = not result.get('approved', True)
            details = f"Large order validation result: {result.get('approved', 'unknown')}"

            if passed:
                details += " - Correctly rejected large position"
            else:
                details += " - FAILED to reject large position"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Position Limit Enforcement",
            test_category="Risk Management",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="MiFID II Article 17"
        )

    async def _test_loss_limit_enforcement(self) -> ComplianceTestResult:
        """Test loss limit enforcement compliance."""
        start_time = time.time()

        try:
            # Simulate portfolio with significant losses
            # This would normally check against actual portfolio state

            # Create order that would increase loss exposure
            order = OrderRequest(
                symbol="AAPL",
                quantity=Decimal("10000"),
                price=Decimal("150.0"),
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            result = await self.risk_manager.validate_order_ultra_fast(order)

            # Check if loss limit circuit breaker is functioning
            loss_cb = self.risk_manager.circuit_breakers.get(CircuitBreakerType.LOSS_LIMIT)

            passed = loss_cb is not None
            details = f"Loss limit circuit breaker configured: {passed}"

            if passed:
                details += f" - Threshold: ${loss_cb.threshold_value:,.0f}"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Loss Limit Enforcement",
            test_category="Risk Management",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="FINRA Rule 15c3-5"
        )

    async def _test_risk_check_latency(self) -> ComplianceTestResult:
        """Test risk check latency compliance."""
        start_time = time.time()

        try:
            latencies = []
            sample_count = 1000

            for i in range(sample_count):
                order = OrderRequest(
                    symbol="AAPL",
                    quantity=Decimal("100"),
                    price=Decimal(f"{150.0 + (i * 0.01):.2f}"),
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    asset_class=AssetClass.EQUITY,
                    strategy_id=str(uuid.uuid4())
                )

                check_start = time.time_ns()
                result = await self.risk_manager.validate_order_ultra_fast(order)
                check_end = time.time_ns()

                latency_ns = check_end - check_start
                latencies.append(latency_ns)

            # Calculate P95 latency
            latencies.sort()
            p95_latency_ns = latencies[int(len(latencies) * 0.95)]
            p95_latency_us = p95_latency_ns / 1000

            # Target: <5μs P95
            target_us = 5.0
            passed = p95_latency_us <= target_us

            details = f"P95 latency: {p95_latency_us:.1f}μs (target: {target_us}μs)"
            if passed:
                details += " - PASSED"
            else:
                details += " - FAILED"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Risk Check Latency",
            test_category="Performance",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="Institutional Standards"
        )

    async def _test_order_validation_completeness(self) -> ComplianceTestResult:
        """Test order validation completeness."""
        start_time = time.time()

        try:
            # Test various order scenarios
            test_orders = [
                # Valid order
                OrderRequest(
                    symbol="AAPL", quantity=Decimal("100"), price=Decimal("150.0"),
                    side=OrderSide.BUY, order_type=OrderType.LIMIT, asset_class=AssetClass.EQUITY,
                    strategy_id=str(uuid.uuid4())
                ),
                # Zero quantity (should be rejected)
                OrderRequest(
                    symbol="AAPL", quantity=Decimal("0"), price=Decimal("150.0"),
                    side=OrderSide.BUY, order_type=OrderType.LIMIT, asset_class=AssetClass.EQUITY,
                    strategy_id=str(uuid.uuid4())
                ),
                # Negative price (should be rejected)
                OrderRequest(
                    symbol="AAPL", quantity=Decimal("100"), price=Decimal("-150.0"),
                    side=OrderSide.BUY, order_type=OrderType.LIMIT, asset_class=AssetClass.EQUITY,
                    strategy_id=str(uuid.uuid4())
                )
            ]

            validation_results = []
            for order in test_orders:
                result = await self.risk_manager.validate_order_ultra_fast(order)
                validation_results.append(result)

            # Check validation logic
            valid_order_approved = validation_results[0].get('approved', False)
            zero_qty_rejected = not validation_results[1].get('approved', True)
            negative_price_rejected = not validation_results[2].get('approved', True)

            passed = valid_order_approved and zero_qty_rejected and negative_price_rejected

            details = f"Valid order approved: {valid_order_approved}, "
            details += f"Zero qty rejected: {zero_qty_rejected}, "
            details += f"Negative price rejected: {negative_price_rejected}"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Order Validation Completeness",
            test_category="Risk Management",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="MiFID II Article 17"
        )

    async def _test_risk_scoring_accuracy(self) -> ComplianceTestResult:
        """Test risk scoring accuracy."""
        start_time = time.time()

        try:
            # Test orders with different risk profiles
            low_risk_order = OrderRequest(
                symbol="AAPL", quantity=Decimal("10"), price=Decimal("150.0"),
                side=OrderSide.BUY, order_type=OrderType.LIMIT, asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            high_risk_order = OrderRequest(
                symbol="AAPL", quantity=Decimal("100000"), price=Decimal("150.0"),
                side=OrderSide.BUY, order_type=OrderType.MARKET, asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            low_risk_result = await self.risk_manager.validate_order_ultra_fast(low_risk_order)
            high_risk_result = await self.risk_manager.validate_order_ultra_fast(high_risk_order)

            low_risk_score = low_risk_result.get('risk_score', 0)
            high_risk_score = high_risk_result.get('risk_score', 0)

            # High risk order should have higher risk score
            passed = high_risk_score > low_risk_score

            details = f"Low risk score: {low_risk_score:.3f}, High risk score: {high_risk_score:.3f}"
            if passed:
                details += " - Risk scoring working correctly"
            else:
                details += " - Risk scoring may be incorrect"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Risk Scoring Accuracy",
            test_category="Risk Management",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="Institutional Standards"
        )

    async def run_circuit_breaker_tests(self) -> List[ComplianceTestResult]:
        """Run circuit breaker compliance tests."""
        logger.info("Running circuit breaker compliance tests...")

        cb_tests = []

        # Test each circuit breaker type
        for cb_type in CircuitBreakerType:
            cb_tests.append(await self._test_circuit_breaker_functionality(cb_type))

        # Test circuit breaker reset functionality
        cb_tests.append(await self._test_circuit_breaker_reset())

        # Test circuit breaker response time
        cb_tests.append(await self._test_circuit_breaker_response_time())

        return cb_tests

    async def _test_circuit_breaker_functionality(self, cb_type: CircuitBreakerType) -> ComplianceTestResult:
        """Test individual circuit breaker functionality."""
        start_time = time.time()

        try:
            cb = self.risk_manager.circuit_breakers.get(cb_type)

            if not cb:
                passed = False
                details = f"Circuit breaker {cb_type} not configured"
            else:
                # Test threshold breach detection
                test_value = cb.threshold_value + Decimal('1')  # Exceed threshold

                # Simulate breach
                original_triggered = cb.is_triggered
                breach_detected = cb.check_breach(test_value)

                passed = breach_detected and cb.is_triggered
                details = f"Threshold: {cb.threshold_value}, Test value: {test_value}, Triggered: {cb.is_triggered}"

                # Reset for next test
                cb.is_triggered = original_triggered

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name=f"Circuit Breaker {cb_type.value}",
            test_category="Circuit Breakers",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="FINRA Rule 15c3-5"
        )

    async def _test_circuit_breaker_reset(self) -> ComplianceTestResult:
        """Test circuit breaker reset functionality."""
        start_time = time.time()

        try:
            # Trigger a circuit breaker
            cb = self.risk_manager.circuit_breakers[CircuitBreakerType.POSITION_LIMIT]

            # Force trigger
            cb.is_triggered = True
            original_state = cb.is_triggered

            # Reset
            cb.is_triggered = False
            cb.current_value = Decimal('0')

            passed = not cb.is_triggered and original_state
            details = f"Original state: {original_state}, Reset state: {cb.is_triggered}"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Circuit Breaker Reset",
            test_category="Circuit Breakers",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="Operational Procedures"
        )

    async def _test_circuit_breaker_response_time(self) -> ComplianceTestResult:
        """Test circuit breaker response time."""
        start_time = time.time()

        try:
            cb = self.risk_manager.circuit_breakers[CircuitBreakerType.POSITION_LIMIT]

            # Measure response time
            response_times = []
            for _ in range(100):
                test_value = cb.threshold_value + Decimal('1')

                check_start = time.time_ns()
                cb.check_breach(test_value)
                check_end = time.time_ns()

                response_time_ns = check_end - check_start
                response_times.append(response_time_ns)

                # Reset for next test
                cb.is_triggered = False

            # Calculate average response time
            avg_response_ns = sum(response_times) / len(response_times)
            avg_response_us = avg_response_ns / 1000

            # Target: <1μs response time
            target_us = 1.0
            passed = avg_response_us <= target_us

            details = f"Average response time: {avg_response_us:.3f}μs (target: {target_us}μs)"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Circuit Breaker Response Time",
            test_category="Performance",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="Institutional Standards"
        )

    async def run_audit_trail_tests(self) -> List[ComplianceTestResult]:
        """Run audit trail compliance tests."""
        logger.info("Running audit trail compliance tests...")

        audit_tests = []

        # Test audit trail completeness
        audit_tests.append(await self._test_audit_trail_completeness())

        # Test data retention compliance
        audit_tests.append(await self._test_data_retention_compliance())

        # Test audit trail integrity
        audit_tests.append(await self._test_audit_trail_integrity())

        return audit_tests

    async def _test_audit_trail_completeness(self) -> ComplianceTestResult:
        """Test audit trail completeness."""
        start_time = time.time()

        try:
            # Test that risk checks generate audit records
            order = OrderRequest(
                symbol="AAPL", quantity=Decimal("100"), price=Decimal("150.0"),
                side=OrderSide.BUY, order_type=OrderType.LIMIT, asset_class=AssetClass.EQUITY,
                strategy_id=str(uuid.uuid4())
            )

            initial_checks = self.risk_manager.total_risk_checks
            result = await self.risk_manager.validate_order_ultra_fast(order)
            final_checks = self.risk_manager.total_risk_checks

            # Check if audit record was created
            audit_record_created = final_checks > initial_checks

            passed = audit_record_created and 'latency_ns' in result
            details = f"Audit record created: {audit_record_created}, Latency recorded: {'latency_ns' in result}"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Audit Trail Completeness",
            test_category="Audit Trail",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="MiFID II Article 25"
        )

    async def _test_data_retention_compliance(self) -> ComplianceTestResult:
        """Test data retention compliance."""
        start_time = time.time()

        try:
            # Check if risk metrics are being stored
            risk_metrics = self.risk_manager.get_risk_metrics()

            # Verify required fields are present
            required_fields = ['performance_metrics', 'circuit_breakers', 'timestamp']
            fields_present = all(field in risk_metrics for field in required_fields)

            # Check timestamp format (ISO 8601)
            timestamp_valid = False
            if 'timestamp' in risk_metrics:
                try:
                    datetime.fromisoformat(risk_metrics['timestamp'].replace('Z', '+00:00'))
                    timestamp_valid = True
                except:
                    pass

            passed = fields_present and timestamp_valid
            details = f"Required fields present: {fields_present}, Valid timestamp: {timestamp_valid}"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Data Retention Compliance",
            test_category="Audit Trail",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="MiFID II Article 25"
        )

    async def _test_audit_trail_integrity(self) -> ComplianceTestResult:
        """Test audit trail integrity."""
        start_time = time.time()

        try:
            # Perform multiple operations and verify metrics consistency
            initial_metrics = self.risk_manager.get_risk_metrics()
            initial_total = initial_metrics['performance_metrics']['total_risk_checks']

            # Perform several risk checks
            test_count = 10
            for i in range(test_count):
                order = OrderRequest(
                    symbol="AAPL", quantity=Decimal("100"), price=Decimal(f"{150.0 + i:.2f}"),
                    side=OrderSide.BUY, order_type=OrderType.LIMIT, asset_class=AssetClass.EQUITY,
                    strategy_id=str(uuid.uuid4())
                )
                await self.risk_manager.validate_order_ultra_fast(order)

            # Check final metrics
            final_metrics = self.risk_manager.get_risk_metrics()
            final_total = final_metrics['performance_metrics']['total_risk_checks']

            # Verify count increased correctly
            count_increase = final_total - initial_total
            passed = count_increase == test_count

            details = f"Expected increase: {test_count}, Actual increase: {count_increase}"

        except Exception as e:
            passed = False
            details = f"Test failed with exception: {e}"

        execution_time = (time.time() - start_time) * 1000

        return ComplianceTestResult(
            test_name="Audit Trail Integrity",
            test_category="Audit Trail",
            passed=passed,
            details=details,
            execution_time_ms=execution_time,
            regulatory_requirement="SOX Section 404"
        )

    async def run_comprehensive_compliance_validation(self) -> ComplianceValidationResults:
        """Run comprehensive compliance validation."""
        logger.info("Starting comprehensive compliance validation...")

        start_time = time.time()
        self.test_results = []

        # Run all test categories
        logger.info("Phase 1: Risk management tests")
        risk_tests = await self.run_risk_management_tests()
        self.test_results.extend(risk_tests)

        logger.info("Phase 2: Circuit breaker tests")
        cb_tests = await self.run_circuit_breaker_tests()
        self.test_results.extend(cb_tests)

        logger.info("Phase 3: Audit trail tests")
        audit_tests = await self.run_audit_trail_tests()
        self.test_results.extend(audit_tests)

        # Calculate results
        total_tests = len(self.test_results)
        passed_tests = sum(1 for test in self.test_results if test.passed)
        failed_tests = total_tests - passed_tests
        compliance_score = (passed_tests / max(total_tests, 1)) * 100

        # Determine regulatory compliance
        regulatory_compliance = self._assess_regulatory_compliance()

        # Determine overall status
        if compliance_score >= 95 and all(regulatory_compliance.values()):
            overall_status = "FULLY COMPLIANT"
        elif compliance_score >= 90:
            overall_status = "SUBSTANTIALLY COMPLIANT"
        elif compliance_score >= 80:
            overall_status = "PARTIALLY COMPLIANT"
        else:
            overall_status = "NON-COMPLIANT"

        # Create validation results
        self.validation_results = ComplianceValidationResults(
            timestamp=datetime.now().isoformat(),
            test_duration_seconds=time.time() - start_time,
            total_tests=total_tests,
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            compliance_score=compliance_score,
            test_results=self.test_results,
            regulatory_compliance=regulatory_compliance,
            overall_status=overall_status
        )

        logger.info(f"Compliance validation completed in {self.validation_results.test_duration_seconds:.1f} seconds")
        logger.info(f"Overall status: {overall_status}")
        logger.info(f"Compliance score: {compliance_score:.1f}%")

        return self.validation_results

    def _assess_regulatory_compliance(self) -> Dict[str, bool]:
        """Assess compliance with specific regulatory requirements."""
        compliance = {}

        # Group tests by regulatory requirement
        requirement_tests = {}
        for test in self.test_results:
            req = test.regulatory_requirement
            if req not in requirement_tests:
                requirement_tests[req] = []
            requirement_tests[req].append(test)

        # Assess each requirement
        for requirement, tests in requirement_tests.items():
            all_passed = all(test.passed for test in tests)
            compliance[requirement] = all_passed

        return compliance

    def save_results(self, output_file: str = "compliance_validation_results.json"):
        """Save compliance validation results to file."""
        if not self.validation_results:
            raise ValueError("No validation results available")

        results_dict = asdict(self.validation_results)

        with open(output_file, 'w') as f:
            json.dump(results_dict, f, indent=2, default=str)

        logger.info(f"Compliance validation results saved to {output_file}")

    def generate_compliance_report(self) -> str:
        """Generate comprehensive compliance report."""
        if not self.validation_results:
            raise ValueError("No validation results available")

        report = []
        report.append("=" * 80)
        report.append("ATHENATRADER PHASE 10 HFT COMPLIANCE VALIDATION REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {self.validation_results.timestamp}")
        report.append(f"Test Duration: {self.validation_results.test_duration_seconds:.1f} seconds")
        report.append(f"Overall Status: {self.validation_results.overall_status}")
        report.append(f"Compliance Score: {self.validation_results.compliance_score:.1f}%")
        report.append("")

        # Test Summary
        report.append("TEST SUMMARY:")
        report.append(f"  Total Tests: {self.validation_results.total_tests}")
        report.append(f"  Passed: {self.validation_results.passed_tests}")
        report.append(f"  Failed: {self.validation_results.failed_tests}")
        report.append("")

        # Regulatory Compliance
        report.append("REGULATORY COMPLIANCE:")
        for requirement, compliant in self.validation_results.regulatory_compliance.items():
            status = "✓ COMPLIANT" if compliant else "✗ NON-COMPLIANT"
            report.append(f"  {requirement}: {status}")
        report.append("")

        # Test Results by Category
        categories = {}
        for test in self.validation_results.test_results:
            if test.test_category not in categories:
                categories[test.test_category] = []
            categories[test.test_category].append(test)

        for category, tests in categories.items():
            report.append(f"{category.upper()} TESTS:")
            for test in tests:
                status = "✓ PASS" if test.passed else "✗ FAIL"
                report.append(f"  {test.test_name}: {status}")
                report.append(f"    Execution Time: {test.execution_time_ms:.1f}ms")
                report.append(f"    Details: {test.details}")
                report.append(f"    Requirement: {test.regulatory_requirement}")
                report.append("")

        # Compliance Certification
        report.append("COMPLIANCE CERTIFICATION:")
        if self.validation_results.overall_status == "FULLY COMPLIANT":
            report.append("  ✓ System is FULLY COMPLIANT with regulatory requirements")
            report.append("  ✓ Ready for production HFT deployment")
        else:
            report.append("  ✗ System is NOT FULLY COMPLIANT")
            report.append("  ✗ Additional remediation required before production deployment")

        return "\n".join(report)


async def main():
    """Main compliance validation function."""
    import argparse

    parser = argparse.ArgumentParser(description="Run AthenaTrader Phase 10 HFT compliance validation")
    parser.add_argument("--test-type", choices=["risk", "circuit-breakers", "audit", "all"], default="all",
                       help="Type of compliance tests to run")
    parser.add_argument("--output", default="compliance_validation_results.json",
                       help="Output file for results")
    parser.add_argument("--report", action="store_true",
                       help="Generate compliance report")
    parser.add_argument("--certify", action="store_true",
                       help="Generate compliance certification")

    args = parser.parse_args()

    # Create validator instance
    validator = ComplianceValidator()

    try:
        # Setup test environment
        if not await validator.setup_test_environment():
            logger.error("Failed to setup compliance test environment")
            sys.exit(1)

        # Run compliance tests based on test type
        if args.test_type == "risk":
            logger.info("Running risk management compliance tests only")
            risk_tests = await validator.run_risk_management_tests()

            # Create minimal results for risk-only test
            validator.validation_results = ComplianceValidationResults(
                timestamp=datetime.now().isoformat(),
                test_duration_seconds=0,
                total_tests=len(risk_tests),
                passed_tests=sum(1 for test in risk_tests if test.passed),
                failed_tests=sum(1 for test in risk_tests if not test.passed),
                compliance_score=(sum(1 for test in risk_tests if test.passed) / max(len(risk_tests), 1)) * 100,
                test_results=risk_tests,
                regulatory_compliance={},
                overall_status="Risk Tests Only"
            )

        elif args.test_type == "circuit-breakers":
            logger.info("Running circuit breaker compliance tests only")
            cb_tests = await validator.run_circuit_breaker_tests()

            # Create minimal results for circuit breaker-only test
            validator.validation_results = ComplianceValidationResults(
                timestamp=datetime.now().isoformat(),
                test_duration_seconds=0,
                total_tests=len(cb_tests),
                passed_tests=sum(1 for test in cb_tests if test.passed),
                failed_tests=sum(1 for test in cb_tests if not test.passed),
                compliance_score=(sum(1 for test in cb_tests if test.passed) / max(len(cb_tests), 1)) * 100,
                test_results=cb_tests,
                regulatory_compliance={},
                overall_status="Circuit Breaker Tests Only"
            )

        elif args.test_type == "audit":
            logger.info("Running audit trail compliance tests only")
            audit_tests = await validator.run_audit_trail_tests()

            # Create minimal results for audit-only test
            validator.validation_results = ComplianceValidationResults(
                timestamp=datetime.now().isoformat(),
                test_duration_seconds=0,
                total_tests=len(audit_tests),
                passed_tests=sum(1 for test in audit_tests if test.passed),
                failed_tests=sum(1 for test in audit_tests if not test.passed),
                compliance_score=(sum(1 for test in audit_tests if test.passed) / max(len(audit_tests), 1)) * 100,
                test_results=audit_tests,
                regulatory_compliance={},
                overall_status="Audit Trail Tests Only"
            )

        else:  # args.test_type == "all"
            logger.info("Running comprehensive compliance validation")
            await validator.run_comprehensive_compliance_validation()

        # Save results
        validator.save_results(args.output)

        # Generate and display report
        if args.report:
            report = validator.generate_compliance_report()
            print(report)

            # Save report to file
            report_file = args.output.replace('.json', '_report.txt')
            with open(report_file, 'w') as f:
                f.write(report)
            logger.info(f"Compliance report saved to {report_file}")

        # Generate compliance certification
        if args.certify:
            certification_passed = validate_compliance_certification(validator.validation_results)
            if certification_passed:
                logger.info("✓ COMPLIANCE CERTIFICATION PASSED - HFT deployment approved")
                sys.exit(0)
            else:
                logger.error("✗ COMPLIANCE CERTIFICATION FAILED - HFT deployment not approved")
                sys.exit(1)

        logger.info("Compliance validation completed successfully")

    except Exception as e:
        logger.error(f"Compliance validation failed: {e}")
        sys.exit(1)

    finally:
        # Cleanup test environment
        await validator.cleanup_test_environment()


def validate_compliance_certification(results: ComplianceValidationResults) -> bool:
    """Validate compliance certification requirements."""
    if not results:
        return False

    logger.info("Validating compliance certification requirements...")

    certification_passed = True

    # Check overall compliance score
    if results.compliance_score < 95.0:
        logger.error(f"Compliance score FAILED: {results.compliance_score:.1f}% (required: 95.0%)")
        certification_passed = False
    else:
        logger.info(f"Compliance score PASSED: {results.compliance_score:.1f}%")

    # Check regulatory compliance
    for requirement, compliant in results.regulatory_compliance.items():
        if not compliant:
            logger.error(f"Regulatory requirement FAILED: {requirement}")
            certification_passed = False
        else:
            logger.info(f"Regulatory requirement PASSED: {requirement}")

    # Check critical test categories
    critical_categories = ["Risk Management", "Circuit Breakers", "Audit Trail"]
    category_results = {}

    for test in results.test_results:
        category = test.test_category
        if category not in category_results:
            category_results[category] = []
        category_results[category].append(test.passed)

    for category in critical_categories:
        if category in category_results:
            category_passed = all(category_results[category])
            if not category_passed:
                logger.error(f"Critical category FAILED: {category}")
                certification_passed = False
            else:
                logger.info(f"Critical category PASSED: {category}")
        else:
            logger.error(f"Critical category MISSING: {category}")
            certification_passed = False

    # Check overall status
    if results.overall_status != "FULLY COMPLIANT":
        logger.error(f"Overall status FAILED: {results.overall_status} (required: FULLY COMPLIANT)")
        certification_passed = False
    else:
        logger.info(f"Overall status PASSED: {results.overall_status}")

    return certification_passed


if __name__ == "__main__":
    asyncio.run(main())
