"""
AthenaTrader Phase 4: EMIR Compliance Module

European Market Infrastructure Regulation (EMIR) compliance implementation
for derivatives trading, reporting, and risk mitigation requirements.

Key Features:
- Real-time derivatives transaction reporting to trade repositories
- Risk mitigation techniques for non-centrally cleared derivatives
- Margin requirements calculation and monitoring
- Counterparty risk assessment and exposure limits
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal
from fastapi import APIRouter, HTTPException, Query, Body, Depends
from pydantic import BaseModel, Field, validator
from sqlalchemy.ext.asyncio import AsyncSession

from ...core.database import get_db
from ...core.logging_config import get_logger
from ...compliance.emir_engine import EMIRComplianceEngine, EMIRReportingEngine
from ...models.compliance import EMIRTransaction, EMIRReport, ComplianceStatus

# Setup logging with Winston-style configuration
logger = get_logger(__name__)
compliance_logger = get_logger("compliance.emir")

# Create router
router = APIRouter()

# Initialize EMIR engines
emir_compliance = EMIRComplianceEngine()
emir_reporting = EMIRReportingEngine()


class EMIRTransactionRequest(BaseModel):
    """EMIR transaction validation request."""
    
    trade_id: str = Field(..., description="Unique trade identifier")
    counterparty_id: str = Field(..., description="Counterparty identifier")
    instrument_type: str = Field(..., description="Derivative instrument type")
    notional_amount: Decimal = Field(..., description="Notional amount in EUR")
    currency: str = Field(..., description="Trade currency")
    execution_timestamp: datetime = Field(..., description="Trade execution time")
    maturity_date: Optional[datetime] = Field(None, description="Maturity date")
    underlying_asset: str = Field(..., description="Underlying asset identifier")
    clearing_status: str = Field(..., description="Centrally cleared or bilateral")
    
    @validator('notional_amount')
    def validate_notional_amount(cls, v):
        """Validate notional amount is positive."""
        if v <= 0:
            raise ValueError("Notional amount must be positive")
        return v
    
    @validator('instrument_type')
    def validate_instrument_type(cls, v):
        """Validate instrument type is supported."""
        allowed_types = ['IRS', 'CDS', 'FRA', 'SWAP', 'OPTION', 'FUTURE']
        if v.upper() not in allowed_types:
            raise ValueError(f"Instrument type must be one of: {allowed_types}")
        return v.upper()


class EMIRComplianceResponse(BaseModel):
    """EMIR compliance validation response."""
    
    trade_id: str
    compliance_status: str
    risk_score: float
    requires_reporting: bool
    reporting_deadline: Optional[datetime]
    margin_requirements: Dict[str, Any]
    violations: List[str]
    recommendations: List[str]
    lei_validation: Dict[str, Any]
    timestamp: datetime


@router.post("/validate", response_model=EMIRComplianceResponse)
async def validate_emir_compliance(
    transaction: EMIRTransactionRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    Validate EMIR compliance for a derivatives transaction.
    
    Performs comprehensive EMIR compliance checks including:
    - Reporting requirements validation
    - LEI (Legal Entity Identifier) verification
    - Margin requirements calculation
    - Risk mitigation assessment
    """
    try:
        # Log compliance validation start
        compliance_logger.info(
            f"Starting EMIR compliance validation - Trade ID: {transaction.trade_id}, "
            f"Instrument: {transaction.instrument_type}, "
            f"Notional: {transaction.notional_amount} {transaction.currency}"
        )
        
        # Validate EMIR compliance
        compliance_result = await emir_compliance.validate_transaction(
            trade_id=transaction.trade_id,
            counterparty_id=transaction.counterparty_id,
            instrument_type=transaction.instrument_type,
            notional_amount=transaction.notional_amount,
            currency=transaction.currency,
            execution_timestamp=transaction.execution_timestamp,
            maturity_date=transaction.maturity_date,
            underlying_asset=transaction.underlying_asset,
            clearing_status=transaction.clearing_status
        )
        
        # Calculate margin requirements
        margin_requirements = await emir_compliance.calculate_margin_requirements(
            instrument_type=transaction.instrument_type,
            notional_amount=transaction.notional_amount,
            counterparty_id=transaction.counterparty_id,
            clearing_status=transaction.clearing_status
        )
        
        # Validate LEI identifier
        lei_validation = await emir_compliance.validate_lei_identifier(
            counterparty_id=transaction.counterparty_id
        )
        
        # Determine reporting requirements
        requires_reporting = await emir_compliance.requires_trade_reporting(
            instrument_type=transaction.instrument_type,
            notional_amount=transaction.notional_amount,
            counterparty_id=transaction.counterparty_id
        )
        
        # Calculate reporting deadline (T+1 for EMIR)
        reporting_deadline = None
        if requires_reporting:
            reporting_deadline = transaction.execution_timestamp + timedelta(days=1)
        
        # Generate compliance recommendations
        recommendations = await emir_compliance.generate_recommendations(
            compliance_result, margin_requirements, lei_validation
        )
        
        # Log compliance validation result
        compliance_logger.info(
            f"EMIR compliance validation completed - Trade ID: {transaction.trade_id}, "
            f"Status: {compliance_result['status']}, "
            f"Risk Score: {compliance_result['risk_score']}, "
            f"Requires Reporting: {requires_reporting}"
        )
        
        return EMIRComplianceResponse(
            trade_id=transaction.trade_id,
            compliance_status=compliance_result['status'],
            risk_score=compliance_result['risk_score'],
            requires_reporting=requires_reporting,
            reporting_deadline=reporting_deadline,
            margin_requirements=margin_requirements,
            violations=compliance_result.get('violations', []),
            recommendations=recommendations,
            lei_validation=lei_validation,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"EMIR compliance validation failed for trade {transaction.trade_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"EMIR compliance validation failed: {str(e)}"
        )


@router.post("/report")
async def submit_emir_report(
    trade_id: str,
    report_type: str = Query(..., description="Report type: NEW, MODIFY, CANCEL"),
    db: AsyncSession = Depends(get_db)
):
    """
    Submit EMIR trade report to authorized trade repository.
    
    Handles real-time submission of derivatives transaction reports
    as required by EMIR Article 9 reporting obligations.
    """
    try:
        # Log report submission start
        compliance_logger.info(
            f"Starting EMIR report submission - Trade ID: {trade_id}, "
            f"Report Type: {report_type}"
        )
        
        # Submit report to trade repository
        report_result = await emir_reporting.submit_trade_report(
            trade_id=trade_id,
            report_type=report_type
        )
        
        # Log successful submission
        compliance_logger.info(
            f"EMIR report submitted successfully - Trade ID: {trade_id}, "
            f"Report ID: {report_result['report_id']}, "
            f"Repository: {report_result['repository']}"
        )
        
        return {
            "trade_id": trade_id,
            "report_id": report_result['report_id'],
            "report_type": report_type,
            "repository": report_result['repository'],
            "submission_timestamp": report_result['timestamp'],
            "status": "SUBMITTED",
            "acknowledgment": report_result.get('acknowledgment')
        }
        
    except Exception as e:
        logger.error(f"EMIR report submission failed for trade {trade_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"EMIR report submission failed: {str(e)}"
        )


@router.get("/reports/{trade_id}")
async def get_emir_reports(
    trade_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Get EMIR reporting history for a specific trade."""
    try:
        reports = await emir_reporting.get_trade_reports(trade_id)
        
        return {
            "trade_id": trade_id,
            "reports": reports,
            "total_reports": len(reports)
        }
        
    except Exception as e:
        logger.error(f"Failed to retrieve EMIR reports for trade {trade_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve EMIR reports: {str(e)}"
        )


@router.get("/margin-requirements/{counterparty_id}")
async def get_margin_requirements(
    counterparty_id: str,
    calculation_date: Optional[datetime] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    Calculate EMIR margin requirements for a counterparty.
    
    Implements EMIR RTS on risk-mitigation techniques for
    non-centrally cleared OTC derivatives.
    """
    try:
        if calculation_date is None:
            calculation_date = datetime.now()
        
        # Calculate margin requirements
        margin_calc = await emir_compliance.calculate_counterparty_margin(
            counterparty_id=counterparty_id,
            calculation_date=calculation_date
        )
        
        return {
            "counterparty_id": counterparty_id,
            "calculation_date": calculation_date,
            "initial_margin": margin_calc['initial_margin'],
            "variation_margin": margin_calc['variation_margin'],
            "threshold_amount": margin_calc['threshold_amount'],
            "minimum_transfer_amount": margin_calc['minimum_transfer_amount'],
            "eligible_collateral": margin_calc['eligible_collateral'],
            "margin_period_of_risk": margin_calc['margin_period_of_risk']
        }
        
    except Exception as e:
        logger.error(f"Margin calculation failed for counterparty {counterparty_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Margin calculation failed: {str(e)}"
        )


@router.get("/health")
async def emir_health_check():
    """EMIR compliance module health check."""
    try:
        # Check EMIR engine status
        engine_status = await emir_compliance.health_check()
        
        # Check reporting connectivity
        reporting_status = await emir_reporting.check_connectivity()
        
        return {
            "status": "healthy",
            "module": "EMIR Compliance",
            "engine_status": engine_status,
            "reporting_status": reporting_status,
            "timestamp": datetime.now()
        }
        
    except Exception as e:
        logger.error(f"EMIR health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"EMIR compliance module unhealthy: {str(e)}"
        )
