"""
AthenaTrader Phase 11 Advanced Analytics Engine Redis Configuration
"""

import asyncio
import json
import logging
from typing import Any, Optional, Dict, List
import redis.asyncio as redis
from datetime import datetime, timedelta
from .config import settings

logger = logging.getLogger(__name__)

# Global Redis connection pool
redis_pool = None


async def init_redis():
    """Initialize Redis connection pool."""
    global redis_pool
    
    try:
        redis_pool = redis.ConnectionPool.from_url(
            settings.REDIS_URL,
            max_connections=settings.REDIS_POOL_SIZE,
            decode_responses=True
        )
        
        # Test connection
        async with redis.Redis(connection_pool=redis_pool) as client:
            await client.ping()
        
        logger.info("Redis connection pool initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize Redis: {e}")
        raise


async def close_redis():
    """Close Redis connection pool."""
    global redis_pool
    
    if redis_pool:
        await redis_pool.disconnect()
        logger.info("Redis connection pool closed")


async def get_redis_client():
    """Get Redis client from pool."""
    if not redis_pool:
        raise RuntimeError("Redis not initialized")
    
    return redis.Redis(connection_pool=redis_pool)


class RedisCache:
    """Redis caching utility for Advanced Analytics Engine."""
    
    def __init__(self):
        self.default_ttl = settings.MODEL_CACHE_TTL
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        try:
            async with get_redis_client() as client:
                value = await client.get(key)
                if value:
                    return json.loads(value)
                return None
        except Exception as e:
            logger.error(f"Redis get error for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache."""
        try:
            async with get_redis_client() as client:
                ttl = ttl or self.default_ttl
                serialized_value = json.dumps(value, default=str)
                await client.setex(key, ttl, serialized_value)
                return True
        except Exception as e:
            logger.error(f"Redis set error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        try:
            async with get_redis_client() as client:
                await client.delete(key)
                return True
        except Exception as e:
            logger.error(f"Redis delete error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        try:
            async with get_redis_client() as client:
                return await client.exists(key) > 0
        except Exception as e:
            logger.error(f"Redis exists error for key {key}: {e}")
            return False
    
    async def get_keys_pattern(self, pattern: str) -> List[str]:
        """Get keys matching pattern."""
        try:
            async with get_redis_client() as client:
                return await client.keys(pattern)
        except Exception as e:
            logger.error(f"Redis keys pattern error for {pattern}: {e}")
            return []


class ModelCache:
    """Specialized cache for ML models and predictions."""
    
    def __init__(self):
        self.cache = RedisCache()
        self.model_prefix = "model:"
        self.prediction_prefix = "prediction:"
        self.sentiment_prefix = "sentiment:"
        self.regime_prefix = "regime:"
    
    async def cache_model_prediction(self, model_name: str, symbol: str, 
                                   horizon_minutes: int, prediction: Dict[str, Any]) -> bool:
        """Cache model prediction."""
        key = f"{self.prediction_prefix}{model_name}:{symbol}:{horizon_minutes}"
        return await self.cache.set(key, prediction, ttl=horizon_minutes * 60)
    
    async def get_cached_prediction(self, model_name: str, symbol: str, 
                                  horizon_minutes: int) -> Optional[Dict[str, Any]]:
        """Get cached model prediction."""
        key = f"{self.prediction_prefix}{model_name}:{symbol}:{horizon_minutes}"
        return await self.cache.get(key)
    
    async def cache_sentiment_analysis(self, symbol: str, sentiment_data: Dict[str, Any]) -> bool:
        """Cache sentiment analysis result."""
        key = f"{self.sentiment_prefix}{symbol}"
        return await self.cache.set(key, sentiment_data, ttl=300)  # 5 minutes
    
    async def get_cached_sentiment(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached sentiment analysis."""
        key = f"{self.sentiment_prefix}{symbol}"
        return await self.cache.get(key)
    
    async def cache_market_regime(self, symbol: str, regime_data: Dict[str, Any]) -> bool:
        """Cache market regime detection result."""
        key = f"{self.regime_prefix}{symbol}"
        return await self.cache.set(key, regime_data, ttl=3600)  # 1 hour
    
    async def get_cached_regime(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get cached market regime."""
        key = f"{self.regime_prefix}{symbol}"
        return await self.cache.get(key)
    
    async def invalidate_symbol_cache(self, symbol: str) -> bool:
        """Invalidate all cached data for a symbol."""
        try:
            patterns = [
                f"{self.prediction_prefix}*:{symbol}:*",
                f"{self.sentiment_prefix}{symbol}",
                f"{self.regime_prefix}{symbol}"
            ]
            
            for pattern in patterns:
                keys = await self.cache.get_keys_pattern(pattern)
                for key in keys:
                    await self.cache.delete(key)
            
            return True
        except Exception as e:
            logger.error(f"Failed to invalidate cache for symbol {symbol}: {e}")
            return False


class RealTimeDataStream:
    """Redis-based real-time data streaming for market data and analytics."""
    
    def __init__(self):
        self.market_data_stream = "market_data_stream"
        self.prediction_stream = "prediction_stream"
        self.sentiment_stream = "sentiment_stream"
        self.compliance_stream = "compliance_stream"
    
    async def publish_market_data(self, symbol: str, data: Dict[str, Any]) -> bool:
        """Publish market data to stream."""
        try:
            async with get_redis_client() as client:
                message = {
                    "symbol": symbol,
                    "timestamp": datetime.now().isoformat(),
                    "data": data
                }
                await client.xadd(self.market_data_stream, message)
                return True
        except Exception as e:
            logger.error(f"Failed to publish market data for {symbol}: {e}")
            return False
    
    async def publish_prediction(self, model_name: str, symbol: str, prediction: Dict[str, Any]) -> bool:
        """Publish prediction to stream."""
        try:
            async with get_redis_client() as client:
                message = {
                    "model_name": model_name,
                    "symbol": symbol,
                    "timestamp": datetime.now().isoformat(),
                    "prediction": json.dumps(prediction, default=str)
                }
                await client.xadd(self.prediction_stream, message)
                return True
        except Exception as e:
            logger.error(f"Failed to publish prediction for {symbol}: {e}")
            return False
    
    async def publish_sentiment(self, symbol: str, sentiment: Dict[str, Any]) -> bool:
        """Publish sentiment analysis to stream."""
        try:
            async with get_redis_client() as client:
                message = {
                    "symbol": symbol,
                    "timestamp": datetime.now().isoformat(),
                    "sentiment": json.dumps(sentiment, default=str)
                }
                await client.xadd(self.sentiment_stream, message)
                return True
        except Exception as e:
            logger.error(f"Failed to publish sentiment for {symbol}: {e}")
            return False
    
    async def publish_compliance_alert(self, trade_id: str, alert: Dict[str, Any]) -> bool:
        """Publish compliance alert to stream."""
        try:
            async with get_redis_client() as client:
                message = {
                    "trade_id": trade_id,
                    "timestamp": datetime.now().isoformat(),
                    "alert": json.dumps(alert, default=str)
                }
                await client.xadd(self.compliance_stream, message)
                return True
        except Exception as e:
            logger.error(f"Failed to publish compliance alert for {trade_id}: {e}")
            return False
    
    async def read_stream(self, stream_name: str, count: int = 10, block: int = 1000) -> List[Dict[str, Any]]:
        """Read messages from stream."""
        try:
            async with get_redis_client() as client:
                messages = await client.xread({stream_name: '$'}, count=count, block=block)
                
                parsed_messages = []
                for stream, msgs in messages:
                    for msg_id, fields in msgs:
                        parsed_messages.append({
                            "id": msg_id,
                            "stream": stream,
                            "fields": fields
                        })
                
                return parsed_messages
        except Exception as e:
            logger.error(f"Failed to read from stream {stream_name}: {e}")
            return []


class PerformanceMetrics:
    """Redis-based performance metrics tracking."""
    
    def __init__(self):
        self.cache = RedisCache()
        self.metrics_prefix = "metrics:"
    
    async def record_api_latency(self, endpoint: str, latency_ms: float) -> bool:
        """Record API endpoint latency."""
        key = f"{self.metrics_prefix}api_latency:{endpoint}"
        timestamp = datetime.now().isoformat()
        
        # Store as time series data
        metric_data = {
            "timestamp": timestamp,
            "latency_ms": latency_ms,
            "target_ms": settings.API_RESPONSE_TIME_TARGET_MS,
            "target_met": latency_ms <= settings.API_RESPONSE_TIME_TARGET_MS
        }
        
        return await self.cache.set(f"{key}:{timestamp}", metric_data, ttl=86400)  # 24 hours
    
    async def record_model_performance(self, model_name: str, inference_time_ms: float, 
                                     accuracy: Optional[float] = None) -> bool:
        """Record ML model performance metrics."""
        key = f"{self.metrics_prefix}model_performance:{model_name}"
        timestamp = datetime.now().isoformat()
        
        metric_data = {
            "timestamp": timestamp,
            "inference_time_ms": inference_time_ms,
            "target_ms": settings.MODEL_INFERENCE_TIME_TARGET_MS,
            "latency_target_met": inference_time_ms <= settings.MODEL_INFERENCE_TIME_TARGET_MS
        }
        
        if accuracy is not None:
            metric_data.update({
                "accuracy": accuracy,
                "accuracy_target": settings.PREDICTION_ACCURACY_TARGET,
                "accuracy_target_met": accuracy >= settings.PREDICTION_ACCURACY_TARGET
            })
        
        return await self.cache.set(f"{key}:{timestamp}", metric_data, ttl=86400)  # 24 hours
    
    async def get_performance_summary(self, metric_type: str, hours: int = 24) -> Dict[str, Any]:
        """Get performance summary for the last N hours."""
        try:
            pattern = f"{self.metrics_prefix}{metric_type}:*"
            keys = await self.cache.get_keys_pattern(pattern)
            
            # Filter keys by time range
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_keys = []
            
            for key in keys:
                # Extract timestamp from key
                timestamp_str = key.split(':')[-1]
                try:
                    key_time = datetime.fromisoformat(timestamp_str)
                    if key_time >= cutoff_time:
                        recent_keys.append(key)
                except ValueError:
                    continue
            
            # Collect metrics
            metrics = []
            for key in recent_keys:
                metric_data = await self.cache.get(key)
                if metric_data:
                    metrics.append(metric_data)
            
            if not metrics:
                return {"summary": "No data available"}
            
            # Calculate summary statistics
            if metric_type == "api_latency":
                latencies = [m["latency_ms"] for m in metrics]
                targets_met = sum(1 for m in metrics if m["target_met"])
                
                return {
                    "metric_type": metric_type,
                    "time_range_hours": hours,
                    "total_requests": len(metrics),
                    "avg_latency_ms": sum(latencies) / len(latencies),
                    "min_latency_ms": min(latencies),
                    "max_latency_ms": max(latencies),
                    "target_met_percentage": (targets_met / len(metrics)) * 100,
                    "target_ms": settings.API_RESPONSE_TIME_TARGET_MS
                }
            
            elif metric_type == "model_performance":
                inference_times = [m["inference_time_ms"] for m in metrics]
                latency_targets_met = sum(1 for m in metrics if m["latency_target_met"])
                
                summary = {
                    "metric_type": metric_type,
                    "time_range_hours": hours,
                    "total_inferences": len(metrics),
                    "avg_inference_time_ms": sum(inference_times) / len(inference_times),
                    "min_inference_time_ms": min(inference_times),
                    "max_inference_time_ms": max(inference_times),
                    "latency_target_met_percentage": (latency_targets_met / len(metrics)) * 100,
                    "target_ms": settings.MODEL_INFERENCE_TIME_TARGET_MS
                }
                
                # Add accuracy metrics if available
                accuracies = [m["accuracy"] for m in metrics if "accuracy" in m]
                if accuracies:
                    accuracy_targets_met = sum(1 for m in metrics if m.get("accuracy_target_met", False))
                    summary.update({
                        "avg_accuracy": sum(accuracies) / len(accuracies),
                        "min_accuracy": min(accuracies),
                        "max_accuracy": max(accuracies),
                        "accuracy_target_met_percentage": (accuracy_targets_met / len(accuracies)) * 100,
                        "accuracy_target": settings.PREDICTION_ACCURACY_TARGET
                    })
                
                return summary
            
            return {"summary": "Unknown metric type"}
            
        except Exception as e:
            logger.error(f"Failed to get performance summary for {metric_type}: {e}")
            return {"error": str(e)}


# Global instances
model_cache = ModelCache()
data_stream = RealTimeDataStream()
performance_metrics = PerformanceMetrics()
