"""
AthenaTrader Phase 11 Advanced Analytics Engine - Cross-Asset Risk Management

Cross-asset risk management with correlation-based position sizing and portfolio-level VaR calculations.
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
from scipy import stats
from scipy.optimize import minimize
from .asset_manager import AssetClass, Position, MultiAssetManager
from ..core.config import settings, MultiAssetConfig
from ..core.database import DatabaseManager

logger = logging.getLogger("multi_asset")


@dataclass
class RiskMetrics:
    """Portfolio risk metrics."""
    portfolio_id: str
    total_value: float
    var_95: float
    var_99: float
    cvar_95: float
    cvar_99: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    beta: float
    correlation_risk: float
    concentration_risk: float
    liquidity_risk: float
    timestamp: datetime


@dataclass
class PositionLimit:
    """Position limit configuration."""
    symbol: str
    asset_class: AssetClass
    max_position_usd: float
    max_weight_percent: float
    max_leverage: float
    risk_factor: float


@dataclass
class CorrelationMatrix:
    """Asset correlation matrix."""
    symbols: List[str]
    matrix: np.ndarray
    timestamp: datetime
    lookback_days: int


class CorrelationAnalyzer:
    """Analyze correlations between different asset classes."""
    
    def __init__(self):
        self.correlation_cache = {}
        self.price_history = {}
        self.lookback_days = MultiAssetConfig.CORRELATION_WINDOW_DAYS
    
    async def calculate_correlation_matrix(self, symbols: List[str], 
                                         lookback_days: int = None) -> CorrelationMatrix:
        """Calculate correlation matrix for given symbols."""
        try:
            if lookback_days is None:
                lookback_days = self.lookback_days
            
            # Get price history for all symbols
            price_data = {}
            for symbol in symbols:
                prices = await self._get_price_history(symbol, lookback_days)
                if prices is not None and len(prices) > 10:
                    price_data[symbol] = prices
            
            if len(price_data) < 2:
                logger.warning("Insufficient price data for correlation calculation")
                return self._get_default_correlation_matrix(symbols)
            
            # Calculate returns
            returns_data = {}
            for symbol, prices in price_data.items():
                returns = np.diff(np.log(prices))
                returns_data[symbol] = returns
            
            # Align returns (use minimum length)
            min_length = min(len(returns) for returns in returns_data.values())
            aligned_returns = {}
            for symbol, returns in returns_data.items():
                aligned_returns[symbol] = returns[-min_length:]
            
            # Create returns matrix
            symbols_with_data = list(aligned_returns.keys())
            returns_matrix = np.array([aligned_returns[symbol] for symbol in symbols_with_data]).T
            
            # Calculate correlation matrix
            correlation_matrix = np.corrcoef(returns_matrix.T)
            
            # Ensure positive definite
            correlation_matrix = self._ensure_positive_definite(correlation_matrix)
            
            return CorrelationMatrix(
                symbols=symbols_with_data,
                matrix=correlation_matrix,
                timestamp=datetime.now(),
                lookback_days=lookback_days
            )
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return self._get_default_correlation_matrix(symbols)
    
    async def _get_price_history(self, symbol: str, days: int) -> Optional[np.ndarray]:
        """Get historical price data for a symbol."""
        try:
            # In production, this would fetch from database or market data provider
            # For demo, generate synthetic price history
            
            if symbol in self.price_history:
                return self.price_history[symbol]
            
            # Generate synthetic price series
            np.random.seed(hash(symbol) % 2**32)  # Consistent seed per symbol
            
            # Different volatilities for different asset classes
            if 'BTC' in symbol or 'ETH' in symbol:
                volatility = 0.04  # 4% daily volatility for crypto
                drift = 0.0005
            elif 'TREASURY' in symbol or 'CORPORATE' in symbol:
                volatility = 0.005  # 0.5% daily volatility for bonds
                drift = 0.0001
            else:
                volatility = 0.02  # 2% daily volatility for equities
                drift = 0.0003
            
            # Generate price series
            returns = np.random.normal(drift, volatility, days)
            prices = 100 * np.exp(np.cumsum(returns))  # Start at $100
            
            self.price_history[symbol] = prices
            return prices
            
        except Exception as e:
            logger.error(f"Error getting price history for {symbol}: {e}")
            return None
    
    def _ensure_positive_definite(self, matrix: np.ndarray) -> np.ndarray:
        """Ensure correlation matrix is positive definite."""
        try:
            # Check if already positive definite
            eigenvals = np.linalg.eigvals(matrix)
            if np.all(eigenvals > 0):
                return matrix
            
            # Make positive definite using eigenvalue decomposition
            eigenvals, eigenvecs = np.linalg.eigh(matrix)
            eigenvals = np.maximum(eigenvals, 0.001)  # Minimum eigenvalue
            
            # Reconstruct matrix
            matrix = eigenvecs @ np.diag(eigenvals) @ eigenvecs.T
            
            # Normalize to correlation matrix
            d = np.sqrt(np.diag(matrix))
            matrix = matrix / np.outer(d, d)
            
            return matrix
            
        except Exception as e:
            logger.error(f"Error ensuring positive definite matrix: {e}")
            return np.eye(matrix.shape[0])
    
    def _get_default_correlation_matrix(self, symbols: List[str]) -> CorrelationMatrix:
        """Return default correlation matrix (identity)."""
        n = len(symbols)
        return CorrelationMatrix(
            symbols=symbols,
            matrix=np.eye(n),
            timestamp=datetime.now(),
            lookback_days=self.lookback_days
        )
    
    async def get_asset_class_correlations(self) -> Dict[str, Dict[str, float]]:
        """Get correlations between different asset classes."""
        try:
            # Representative symbols for each asset class
            asset_class_symbols = {
                'equity': ['AAPL', 'GOOGL', 'MSFT'],
                'crypto': ['BTC/USD', 'ETH/USD'],
                'fixed_income': ['US_TREASURY_10Y', 'CORPORATE_AAA']
            }
            
            # Calculate correlations between asset classes
            correlations = {}
            
            for class1, symbols1 in asset_class_symbols.items():
                correlations[class1] = {}
                for class2, symbols2 in asset_class_symbols.items():
                    if class1 == class2:
                        correlations[class1][class2] = 1.0
                    else:
                        # Calculate average correlation between asset classes
                        all_symbols = symbols1 + symbols2
                        corr_matrix = await self.calculate_correlation_matrix(all_symbols)
                        
                        # Extract cross-correlations
                        n1 = len(symbols1)
                        cross_corrs = corr_matrix.matrix[:n1, n1:]
                        avg_correlation = np.mean(cross_corrs)
                        
                        correlations[class1][class2] = float(avg_correlation)
            
            return correlations
            
        except Exception as e:
            logger.error(f"Error calculating asset class correlations: {e}")
            return {
                'equity': {'equity': 1.0, 'crypto': 0.3, 'fixed_income': -0.1},
                'crypto': {'equity': 0.3, 'crypto': 1.0, 'fixed_income': -0.2},
                'fixed_income': {'equity': -0.1, 'crypto': -0.2, 'fixed_income': 1.0}
            }


class VaRCalculator:
    """Value at Risk calculator for multi-asset portfolios."""
    
    def __init__(self):
        self.confidence_levels = [0.95, 0.99]
        self.methods = ['parametric', 'historical', 'monte_carlo']
    
    async def calculate_portfolio_var(self, positions: List[Position], 
                                    correlation_matrix: CorrelationMatrix,
                                    confidence_level: float = 0.95,
                                    method: str = 'parametric') -> Dict[str, float]:
        """Calculate portfolio Value at Risk."""
        try:
            if method == 'parametric':
                return await self._calculate_parametric_var(positions, correlation_matrix, confidence_level)
            elif method == 'historical':
                return await self._calculate_historical_var(positions, confidence_level)
            elif method == 'monte_carlo':
                return await self._calculate_monte_carlo_var(positions, correlation_matrix, confidence_level)
            else:
                raise ValueError(f"Unknown VaR method: {method}")
                
        except Exception as e:
            logger.error(f"Error calculating portfolio VaR: {e}")
            return {'var': 0.0, 'cvar': 0.0}
    
    async def _calculate_parametric_var(self, positions: List[Position],
                                      correlation_matrix: CorrelationMatrix,
                                      confidence_level: float) -> Dict[str, float]:
        """Calculate parametric VaR using correlation matrix."""
        try:
            # Get position values and volatilities
            position_values = []
            volatilities = []
            symbols = []
            
            for position in positions:
                position_values.append(position.market_value)
                symbols.append(position.symbol)
                
                # Estimate volatility (in production, use historical data)
                if position.asset_class == AssetClass.CRYPTOCURRENCY:
                    vol = 0.04  # 4% daily volatility
                elif position.asset_class == AssetClass.FIXED_INCOME:
                    vol = 0.005  # 0.5% daily volatility
                else:
                    vol = 0.02  # 2% daily volatility
                
                volatilities.append(vol)
            
            if not position_values:
                return {'var': 0.0, 'cvar': 0.0}
            
            # Create position vector
            w = np.array(position_values)
            sigma = np.array(volatilities)
            
            # Get correlation matrix for these symbols
            corr_symbols = correlation_matrix.symbols
            corr_matrix = correlation_matrix.matrix
            
            # Align symbols with correlation matrix
            aligned_corr = np.eye(len(symbols))
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols):
                    if symbol1 in corr_symbols and symbol2 in corr_symbols:
                        idx1 = corr_symbols.index(symbol1)
                        idx2 = corr_symbols.index(symbol2)
                        aligned_corr[i, j] = corr_matrix[idx1, idx2]
            
            # Calculate portfolio volatility
            # σ_p = sqrt(w^T * Σ * w) where Σ = diag(σ) * Corr * diag(σ)
            cov_matrix = np.outer(sigma, sigma) * aligned_corr
            portfolio_variance = np.dot(w, np.dot(cov_matrix, w))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Calculate VaR
            z_score = stats.norm.ppf(confidence_level)
            var = portfolio_volatility * z_score
            
            # Calculate Conditional VaR (Expected Shortfall)
            cvar = portfolio_volatility * stats.norm.pdf(z_score) / (1 - confidence_level)
            
            return {
                'var': float(var),
                'cvar': float(cvar),
                'portfolio_volatility': float(portfolio_volatility),
                'portfolio_value': float(np.sum(w))
            }
            
        except Exception as e:
            logger.error(f"Error in parametric VaR calculation: {e}")
            return {'var': 0.0, 'cvar': 0.0}
    
    async def _calculate_historical_var(self, positions: List[Position],
                                      confidence_level: float) -> Dict[str, float]:
        """Calculate historical VaR using historical simulation."""
        try:
            # Get historical returns for all positions
            all_returns = []
            
            for position in positions:
                # Get historical price data
                prices = await self._get_historical_prices(position.symbol, 252)  # 1 year
                if prices is not None and len(prices) > 1:
                    returns = np.diff(np.log(prices))
                    position_returns = returns * position.market_value
                    all_returns.append(position_returns)
            
            if not all_returns:
                return {'var': 0.0, 'cvar': 0.0}
            
            # Align returns to same length
            min_length = min(len(returns) for returns in all_returns)
            aligned_returns = [returns[-min_length:] for returns in all_returns]
            
            # Calculate portfolio returns
            portfolio_returns = np.sum(aligned_returns, axis=0)
            
            # Calculate VaR and CVaR
            var_percentile = (1 - confidence_level) * 100
            var = np.percentile(portfolio_returns, var_percentile)
            
            # CVaR is the mean of returns below VaR
            tail_returns = portfolio_returns[portfolio_returns <= var]
            cvar = np.mean(tail_returns) if len(tail_returns) > 0 else var
            
            return {
                'var': float(abs(var)),
                'cvar': float(abs(cvar)),
                'portfolio_volatility': float(np.std(portfolio_returns)),
                'portfolio_value': float(sum(pos.market_value for pos in positions))
            }
            
        except Exception as e:
            logger.error(f"Error in historical VaR calculation: {e}")
            return {'var': 0.0, 'cvar': 0.0}
    
    async def _calculate_monte_carlo_var(self, positions: List[Position],
                                       correlation_matrix: CorrelationMatrix,
                                       confidence_level: float,
                                       num_simulations: int = 10000) -> Dict[str, float]:
        """Calculate Monte Carlo VaR using simulation."""
        try:
            # Get position parameters
            position_values = []
            volatilities = []
            symbols = []
            
            for position in positions:
                position_values.append(position.market_value)
                symbols.append(position.symbol)
                
                # Estimate volatility
                if position.asset_class == AssetClass.CRYPTOCURRENCY:
                    vol = 0.04
                elif position.asset_class == AssetClass.FIXED_INCOME:
                    vol = 0.005
                else:
                    vol = 0.02
                
                volatilities.append(vol)
            
            if not position_values:
                return {'var': 0.0, 'cvar': 0.0}
            
            # Generate correlated random returns
            n_assets = len(positions)
            
            # Get correlation matrix
            corr_symbols = correlation_matrix.symbols
            corr_matrix = correlation_matrix.matrix
            
            # Align correlation matrix
            aligned_corr = np.eye(n_assets)
            for i, symbol1 in enumerate(symbols):
                for j, symbol2 in enumerate(symbols):
                    if symbol1 in corr_symbols and symbol2 in corr_symbols:
                        idx1 = corr_symbols.index(symbol1)
                        idx2 = corr_symbols.index(symbol2)
                        aligned_corr[i, j] = corr_matrix[idx1, idx2]
            
            # Cholesky decomposition for correlation
            try:
                L = np.linalg.cholesky(aligned_corr)
            except np.linalg.LinAlgError:
                # If not positive definite, use identity
                L = np.eye(n_assets)
            
            # Monte Carlo simulation
            portfolio_returns = []
            
            for _ in range(num_simulations):
                # Generate independent random returns
                independent_returns = np.random.normal(0, 1, n_assets)
                
                # Apply correlation
                correlated_returns = L @ independent_returns
                
                # Scale by volatilities
                scaled_returns = correlated_returns * np.array(volatilities)
                
                # Calculate portfolio return
                portfolio_return = np.sum(np.array(position_values) * scaled_returns)
                portfolio_returns.append(portfolio_return)
            
            portfolio_returns = np.array(portfolio_returns)
            
            # Calculate VaR and CVaR
            var_percentile = (1 - confidence_level) * 100
            var = np.percentile(portfolio_returns, var_percentile)
            
            tail_returns = portfolio_returns[portfolio_returns <= var]
            cvar = np.mean(tail_returns) if len(tail_returns) > 0 else var
            
            return {
                'var': float(abs(var)),
                'cvar': float(abs(cvar)),
                'portfolio_volatility': float(np.std(portfolio_returns)),
                'portfolio_value': float(sum(position_values))
            }
            
        except Exception as e:
            logger.error(f"Error in Monte Carlo VaR calculation: {e}")
            return {'var': 0.0, 'cvar': 0.0}
    
    async def _get_historical_prices(self, symbol: str, days: int) -> Optional[np.ndarray]:
        """Get historical prices for VaR calculation."""
        # This would fetch from database in production
        # For demo, generate synthetic data
        np.random.seed(hash(symbol) % 2**32)
        
        if 'BTC' in symbol or 'ETH' in symbol:
            volatility = 0.04
            drift = 0.0005
        elif 'TREASURY' in symbol or 'CORPORATE' in symbol:
            volatility = 0.005
            drift = 0.0001
        else:
            volatility = 0.02
            drift = 0.0003
        
        returns = np.random.normal(drift, volatility, days)
        prices = 100 * np.exp(np.cumsum(returns))
        
        return prices


class CrossAssetRiskManager:
    """Main cross-asset risk management system."""
    
    def __init__(self):
        self.correlation_analyzer = CorrelationAnalyzer()
        self.var_calculator = VaRCalculator()
        self.position_limits = {}
        self.risk_metrics_cache = {}
    
    async def initialize(self):
        """Initialize risk management system."""
        try:
            # Set up default position limits
            await self._setup_default_limits()
            
            logger.info("Cross-asset risk manager initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize risk manager: {e}")
            raise
    
    async def _setup_default_limits(self):
        """Set up default position limits for different asset classes."""
        # Cryptocurrency limits (higher risk)
        for symbol in MultiAssetConfig.SUPPORTED_CRYPTO:
            self.position_limits[symbol] = PositionLimit(
                symbol=symbol,
                asset_class=AssetClass.CRYPTOCURRENCY,
                max_position_usd=settings.MAX_POSITION_SIZE_USD * 0.1,  # 10% of max
                max_weight_percent=5.0,  # 5% max weight
                max_leverage=2.0,
                risk_factor=3.0  # High risk
            )
        
        # Fixed income limits (lower risk)
        for symbol in MultiAssetConfig.SUPPORTED_FIXED_INCOME:
            self.position_limits[symbol] = PositionLimit(
                symbol=symbol,
                asset_class=AssetClass.FIXED_INCOME,
                max_position_usd=settings.MAX_POSITION_SIZE_USD * 0.5,  # 50% of max
                max_weight_percent=20.0,  # 20% max weight
                max_leverage=5.0,
                risk_factor=1.0  # Low risk
            )
    
    async def calculate_portfolio_risk(self, portfolio_id: str, 
                                     positions: List[Position]) -> RiskMetrics:
        """Calculate comprehensive portfolio risk metrics."""
        try:
            if not positions:
                return self._get_default_risk_metrics(portfolio_id)
            
            # Get symbols for correlation analysis
            symbols = [pos.symbol for pos in positions]
            
            # Calculate correlation matrix
            correlation_matrix = await self.correlation_analyzer.calculate_correlation_matrix(symbols)
            
            # Calculate VaR metrics
            var_95_result = await self.var_calculator.calculate_portfolio_var(
                positions, correlation_matrix, 0.95, 'parametric'
            )
            var_99_result = await self.var_calculator.calculate_portfolio_var(
                positions, correlation_matrix, 0.99, 'parametric'
            )
            
            # Calculate portfolio metrics
            total_value = sum(pos.market_value for pos in positions)
            portfolio_return = sum(pos.unrealized_pnl for pos in positions) / total_value if total_value > 0 else 0
            
            # Calculate concentration risk
            concentration_risk = self._calculate_concentration_risk(positions)
            
            # Calculate correlation risk
            correlation_risk = self._calculate_correlation_risk(correlation_matrix)
            
            # Calculate liquidity risk
            liquidity_risk = self._calculate_liquidity_risk(positions)
            
            risk_metrics = RiskMetrics(
                portfolio_id=portfolio_id,
                total_value=total_value,
                var_95=var_95_result.get('var', 0.0),
                var_99=var_99_result.get('var', 0.0),
                cvar_95=var_95_result.get('cvar', 0.0),
                cvar_99=var_99_result.get('cvar', 0.0),
                volatility=var_95_result.get('portfolio_volatility', 0.0),
                sharpe_ratio=portfolio_return / var_95_result.get('portfolio_volatility', 1.0) if var_95_result.get('portfolio_volatility', 0) > 0 else 0,
                max_drawdown=0.0,  # Would calculate from historical data
                beta=1.0,  # Would calculate vs benchmark
                correlation_risk=correlation_risk,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                timestamp=datetime.now()
            )
            
            # Cache results
            self.risk_metrics_cache[portfolio_id] = risk_metrics
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error calculating portfolio risk: {e}")
            return self._get_default_risk_metrics(portfolio_id)
    
    def _calculate_concentration_risk(self, positions: List[Position]) -> float:
        """Calculate concentration risk (Herfindahl index)."""
        try:
            total_value = sum(pos.market_value for pos in positions)
            if total_value == 0:
                return 0.0
            
            weights = [pos.market_value / total_value for pos in positions]
            herfindahl_index = sum(w**2 for w in weights)
            
            # Normalize to 0-1 scale (1 = maximum concentration)
            n = len(positions)
            normalized_concentration = (herfindahl_index - 1/n) / (1 - 1/n) if n > 1 else 0
            
            return float(normalized_concentration)
            
        except Exception as e:
            logger.error(f"Error calculating concentration risk: {e}")
            return 0.0
    
    def _calculate_correlation_risk(self, correlation_matrix: CorrelationMatrix) -> float:
        """Calculate correlation risk (average correlation)."""
        try:
            if correlation_matrix.matrix.shape[0] < 2:
                return 0.0
            
            # Calculate average off-diagonal correlation
            n = correlation_matrix.matrix.shape[0]
            total_correlations = 0
            count = 0
            
            for i in range(n):
                for j in range(i + 1, n):
                    total_correlations += abs(correlation_matrix.matrix[i, j])
                    count += 1
            
            avg_correlation = total_correlations / count if count > 0 else 0
            return float(avg_correlation)
            
        except Exception as e:
            logger.error(f"Error calculating correlation risk: {e}")
            return 0.0
    
    def _calculate_liquidity_risk(self, positions: List[Position]) -> float:
        """Calculate liquidity risk based on asset classes."""
        try:
            total_value = sum(pos.market_value for pos in positions)
            if total_value == 0:
                return 0.0
            
            # Liquidity scores by asset class (0 = most liquid, 1 = least liquid)
            liquidity_scores = {
                AssetClass.EQUITY: 0.1,
                AssetClass.CRYPTOCURRENCY: 0.3,
                AssetClass.FIXED_INCOME: 0.2,
                AssetClass.COMMODITY: 0.4,
                AssetClass.FOREX: 0.05,
                AssetClass.DERIVATIVE: 0.5
            }
            
            weighted_liquidity_risk = 0
            for position in positions:
                weight = position.market_value / total_value
                liquidity_score = liquidity_scores.get(position.asset_class, 0.5)
                weighted_liquidity_risk += weight * liquidity_score
            
            return float(weighted_liquidity_risk)
            
        except Exception as e:
            logger.error(f"Error calculating liquidity risk: {e}")
            return 0.0
    
    def _get_default_risk_metrics(self, portfolio_id: str) -> RiskMetrics:
        """Return default risk metrics for empty portfolio."""
        return RiskMetrics(
            portfolio_id=portfolio_id,
            total_value=0.0,
            var_95=0.0,
            var_99=0.0,
            cvar_95=0.0,
            cvar_99=0.0,
            volatility=0.0,
            sharpe_ratio=0.0,
            max_drawdown=0.0,
            beta=0.0,
            correlation_risk=0.0,
            concentration_risk=0.0,
            liquidity_risk=0.0,
            timestamp=datetime.now()
        )
    
    async def check_position_limits(self, portfolio_id: str, symbol: str, 
                                  new_quantity: float, current_price: float) -> Dict[str, Any]:
        """Check if new position would violate risk limits."""
        try:
            if symbol not in self.position_limits:
                return {"allowed": True, "reason": "No limits defined"}
            
            limit = self.position_limits[symbol]
            new_value = abs(new_quantity * current_price)
            
            # Check absolute position limit
            if new_value > limit.max_position_usd:
                return {
                    "allowed": False,
                    "reason": f"Position value ${new_value:,.0f} exceeds limit ${limit.max_position_usd:,.0f}"
                }
            
            # Check portfolio weight limit (would need current portfolio value)
            # This is simplified - in production, would check against current portfolio
            
            return {"allowed": True, "reason": "Within limits"}
            
        except Exception as e:
            logger.error(f"Error checking position limits: {e}")
            return {"allowed": False, "reason": f"Error checking limits: {e}"}
    
    async def get_risk_summary(self, portfolio_id: str) -> Dict[str, Any]:
        """Get risk summary for portfolio."""
        try:
            if portfolio_id in self.risk_metrics_cache:
                metrics = self.risk_metrics_cache[portfolio_id]
                
                return {
                    "portfolio_id": portfolio_id,
                    "total_value": metrics.total_value,
                    "risk_metrics": {
                        "var_95": metrics.var_95,
                        "var_99": metrics.var_99,
                        "cvar_95": metrics.cvar_95,
                        "volatility": metrics.volatility,
                        "sharpe_ratio": metrics.sharpe_ratio
                    },
                    "risk_factors": {
                        "concentration_risk": metrics.concentration_risk,
                        "correlation_risk": metrics.correlation_risk,
                        "liquidity_risk": metrics.liquidity_risk
                    },
                    "risk_level": self._assess_risk_level(metrics),
                    "timestamp": metrics.timestamp.isoformat()
                }
            else:
                return {
                    "portfolio_id": portfolio_id,
                    "error": "No risk metrics available"
                }
                
        except Exception as e:
            logger.error(f"Error getting risk summary: {e}")
            return {"error": str(e)}
    
    def _assess_risk_level(self, metrics: RiskMetrics) -> str:
        """Assess overall risk level."""
        try:
            # Simple risk scoring
            risk_score = 0
            
            # VaR contribution
            var_pct = (metrics.var_95 / metrics.total_value) * 100 if metrics.total_value > 0 else 0
            if var_pct > 5:
                risk_score += 3
            elif var_pct > 2:
                risk_score += 2
            elif var_pct > 1:
                risk_score += 1
            
            # Concentration risk contribution
            if metrics.concentration_risk > 0.7:
                risk_score += 2
            elif metrics.concentration_risk > 0.5:
                risk_score += 1
            
            # Correlation risk contribution
            if metrics.correlation_risk > 0.8:
                risk_score += 2
            elif metrics.correlation_risk > 0.6:
                risk_score += 1
            
            # Determine risk level
            if risk_score >= 5:
                return "HIGH"
            elif risk_score >= 3:
                return "MEDIUM"
            else:
                return "LOW"
                
        except Exception as e:
            logger.error(f"Error assessing risk level: {e}")
            return "UNKNOWN"
