# AthenaTrader Phase 11 Advanced Analytics Engine Docker Compose

version: '3.8'

services:
  # Advanced Analytics Engine (Phase 11)
  advanced-analytics:
    build: .
    container_name: athenatrader-advanced-analytics
    ports:
      - "8007:8007"
      - "9007:9007"  # Metrics port
    environment:
      - DATABASE_URL=************************************************/athenatrader
      - REDIS_URL=redis://redis:6379/0
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - LOG_LEVEL=INFO
      - DEBUG=false
      - ENABLE_METRICS=true
    depends_on:
      - postgres
      - redis
      - kafka
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models
    networks:
      - athenatrader-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8007/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: athenatrader-postgres
    environment:
      - POSTGRES_DB=athenatrader
      - POSTGRES_USER=athenatrader
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - athenatrader-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: athenatrader-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - athenatrader-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Apache Kafka for Real-time Data Streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: athenatrader-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - athenatrader-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: athenatrader-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - athenatrader-network
    restart: unless-stopped

  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: athenatrader-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - athenatrader-network
    restart: unless-stopped

  # Grafana for Monitoring Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: athenatrader-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - athenatrader-network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: athenatrader-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - advanced-analytics
    networks:
      - athenatrader-network
    restart: unless-stopped

  # TimescaleDB for Time Series Data (Alternative to PostgreSQL for market data)
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: athenatrader-timescaledb
    environment:
      - POSTGRES_DB=market_data
      - POSTGRES_USER=athenatrader
      - POSTGRES_PASSWORD=password
    ports:
      - "5433:5432"
    volumes:
      - timescale_data:/var/lib/postgresql/data
    networks:
      - athenatrader-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  timescale_data:
    driver: local

networks:
  athenatrader-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
