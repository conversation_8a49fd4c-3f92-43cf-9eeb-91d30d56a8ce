"""
Test suite for Phase 10 HFT capabilities.

This module tests the ultra-low latency infrastructure, advanced risk management,
and Smart Order Routing capabilities implemented in Phase 10.
"""

import pytest
import asyncio
import time
from decimal import Decimal
from unittest.mock import Mock, patch

from app.engine.ultra_low_latency import (
    UltraLowLatencyEngine, DPDKConfiguration, FPGAConfiguration,
    DPDKManager, FPGAAccelerator, LatencyTarget
)
from app.engine.advanced_risk_manager import (
    AdvancedRiskManager, RiskLevel, CircuitBreakerType, MicrosecondCircuitBreaker
)
from app.engine.smart_order_router import SmartOrderRouter
from app.schemas.execution import OrderRequest, AssetClass, OrderSide, OrderType


class TestUltraLowLatencyEngine:
    """Test ultra-low latency execution engine."""
    
    @pytest.fixture
    async def ull_engine(self):
        """Create ultra-low latency engine for testing."""
        dpdk_config = DPDKConfiguration(enabled=False)  # Disabled for testing
        fpga_config = FPGAConfiguration(enabled=False)  # Disabled for testing
        engine = UltraLowLatencyEngine(dpdk_config, fpga_config)
        await engine.initialize()
        yield engine
        await engine.cleanup()
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self, ull_engine):
        """Test engine initializes correctly."""
        assert ull_engine.is_running is True
        assert ull_engine.dpdk_manager is not None
        assert ull_engine.fpga_accelerator is not None
        assert len(ull_engine.message_queues) == 5  # Including SOR queue
    
    @pytest.mark.asyncio
    async def test_order_processing_latency(self, ull_engine):
        """Test order processing meets latency targets."""
        order_data = {
            'order_id': 'test_001',
            'symbol': 'AAPL',
            'quantity': 100,
            'price': 150.0,
            'side': 'BUY',
            'order_type': 'LIMIT'
        }
        
        start_time = time.time_ns()
        result = await ull_engine.process_order(order_data)
        end_time = time.time_ns()
        
        latency_ns = end_time - start_time
        latency_us = latency_ns / 1000
        
        # Verify result
        assert result['status'] == 'PROCESSED'
        assert result['order_id'] == 'test_001'
        assert 'processing_latency_ns' in result
        assert 'total_latency_ns' in result
        
        # Verify latency target (should be well under 50μs for test)
        assert latency_us < 1000  # 1ms for test environment
        
        # Verify HFT metrics updated
        assert ull_engine.hft_metrics['total_orders_processed'] > 0
    
    @pytest.mark.asyncio
    async def test_market_data_processing(self, ull_engine):
        """Test market data processing latency."""
        market_data = {
            'bid_price': 149.95,
            'bid_quantity': 1000,
            'ask_price': 150.05,
            'ask_quantity': 1000
        }
        
        start_time = time.time_ns()
        await ull_engine.update_market_data('AAPL', market_data)
        end_time = time.time_ns()
        
        latency_ns = end_time - start_time
        latency_us = latency_ns / 1000
        
        # Verify latency target
        assert latency_us < 100  # 100μs for test environment
        
        # Verify order book updated
        best_bid_ask = ull_engine.get_best_bid_ask('AAPL')
        assert best_bid_ask is not None
        assert best_bid_ask['symbol'] == 'AAPL'
    
    @pytest.mark.asyncio
    async def test_risk_check_latency(self, ull_engine):
        """Test risk check meets latency targets."""
        order_data = {
            'symbol': 'AAPL',
            'quantity': 100,
            'price': 150.0,
            'side': 'BUY',
            'order_type': 'LIMIT'
        }
        
        start_time = time.time_ns()
        result = await ull_engine.perform_risk_check(order_data)
        end_time = time.time_ns()
        
        latency_ns = end_time - start_time
        latency_us = latency_ns / 1000
        
        # Verify result
        assert isinstance(result, bool)
        
        # Verify latency target (should be under 5μs target)
        assert latency_us < 50  # 50μs for test environment
    
    def test_performance_summary(self, ull_engine):
        """Test performance summary generation."""
        summary = ull_engine.get_performance_summary()
        
        assert 'overall_metrics' in summary
        assert 'hft_metrics' in summary
        assert 'dpdk_status' in summary
        assert 'fpga_status' in summary
        assert 'hft_performance_grade' in summary
        
        # Verify HFT metrics structure
        hft_metrics = summary['hft_metrics']
        assert 'total_orders_processed' in hft_metrics
        assert 'fpga_accelerated_orders' in hft_metrics
        assert 'average_latency_us' in hft_metrics
        assert 'peak_throughput_ops_per_sec' in hft_metrics


class TestAdvancedRiskManager:
    """Test advanced risk management capabilities."""
    
    @pytest.fixture
    async def risk_manager(self):
        """Create advanced risk manager for testing."""
        fpga_config = FPGAConfiguration(enabled=False)  # Disabled for testing
        manager = AdvancedRiskManager(fpga_config)
        await manager.initialize()
        yield manager
        await manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_risk_manager_initialization(self, risk_manager):
        """Test risk manager initializes correctly."""
        assert risk_manager.fpga_accelerator is not None
        assert len(risk_manager.circuit_breakers) > 0
        assert CircuitBreakerType.POSITION_LIMIT in risk_manager.circuit_breakers
        assert CircuitBreakerType.LOSS_LIMIT in risk_manager.circuit_breakers
    
    @pytest.mark.asyncio
    async def test_ultra_fast_order_validation(self, risk_manager):
        """Test ultra-fast order validation."""
        order_request = OrderRequest(
            symbol="AAPL",
            quantity=Decimal("100"),
            price=Decimal("150.0"),
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            asset_class=AssetClass.EQUITY
        )
        
        start_time = time.time_ns()
        result = await risk_manager.validate_order_ultra_fast(order_request)
        end_time = time.time_ns()
        
        latency_ns = end_time - start_time
        latency_us = latency_ns / 1000
        
        # Verify result structure
        assert 'approved' in result
        assert 'risk_level' in result
        assert 'risk_score' in result
        assert 'latency_ns' in result
        
        # Verify latency target
        assert latency_us < 100  # 100μs for test environment
        assert result['latency_ns'] > 0
        
        # Verify risk metrics updated
        assert risk_manager.total_risk_checks > 0
    
    def test_circuit_breaker_functionality(self, risk_manager):
        """Test circuit breaker triggers correctly."""
        cb = risk_manager.circuit_breakers[CircuitBreakerType.POSITION_LIMIT]
        
        # Test normal operation
        assert not cb.check_breach(Decimal("1000000"))  # Under limit
        assert not cb.is_triggered
        
        # Test breach
        assert cb.check_breach(Decimal("20000000"))  # Over limit
        assert cb.is_triggered
        
        # Test cooldown
        assert cb.check_breach(Decimal("1000000"))  # Still triggered due to cooldown
    
    def test_risk_metrics_generation(self, risk_manager):
        """Test risk metrics generation."""
        metrics = risk_manager.get_risk_metrics()
        
        assert 'performance_metrics' in metrics
        assert 'circuit_breakers' in metrics
        assert 'portfolio_metrics' in metrics
        assert 'fpga_acceleration' in metrics
        
        # Verify performance metrics
        perf_metrics = metrics['performance_metrics']
        assert 'total_risk_checks' in perf_metrics
        assert 'success_rate_percent' in perf_metrics
        assert 'avg_latency_us' in perf_metrics


class TestDPDKManager:
    """Test DPDK integration."""
    
    @pytest.fixture
    def dpdk_manager(self):
        """Create DPDK manager for testing."""
        config = DPDKConfiguration(enabled=False)  # Disabled for testing
        return DPDKManager(config)
    
    @pytest.mark.asyncio
    async def test_dpdk_initialization_disabled(self, dpdk_manager):
        """Test DPDK initialization when disabled."""
        result = await dpdk_manager.initialize()
        assert result is True
        assert dpdk_manager.status.value == "DISABLED"
    
    def test_dpdk_statistics(self, dpdk_manager):
        """Test DPDK statistics generation."""
        stats = dpdk_manager.get_statistics()
        
        assert 'status' in stats
        assert 'global_stats' in stats
        assert 'port_stats' in stats
        
        # Verify global stats structure
        global_stats = stats['global_stats']
        assert 'packets_transmitted' in global_stats
        assert 'packets_received' in global_stats
        assert 'errors' in global_stats


class TestFPGAAccelerator:
    """Test FPGA acceleration."""
    
    @pytest.fixture
    async def fpga_accelerator(self):
        """Create FPGA accelerator for testing."""
        config = FPGAConfiguration(enabled=False)  # Disabled for testing
        accelerator = FPGAAccelerator(config)
        await accelerator.initialize()
        yield accelerator
        await accelerator.cleanup()
    
    @pytest.mark.asyncio
    async def test_fpga_initialization_disabled(self, fpga_accelerator):
        """Test FPGA initialization when disabled."""
        assert fpga_accelerator.is_initialized is True  # Should succeed when disabled
    
    @pytest.mark.asyncio
    async def test_order_matching_acceleration(self, fpga_accelerator):
        """Test order matching acceleration."""
        order_data = {
            'order_id': 'test_001',
            'symbol': 'AAPL',
            'quantity': 100,
            'price': 150.0
        }
        
        result = await fpga_accelerator.accelerate_order_matching(order_data)
        
        # When disabled, should return original data
        assert result['order_id'] == 'test_001'
        assert 'fpga_accelerated' in result
    
    def test_fpga_performance_metrics(self, fpga_accelerator):
        """Test FPGA performance metrics."""
        metrics = fpga_accelerator.get_performance_metrics()
        
        assert 'enabled' in metrics
        assert 'initialized' in metrics
        assert 'operations_accelerated' in metrics
        assert 'estimated_speedup_factor' in metrics
        assert 'utilization_percent' in metrics


@pytest.mark.integration
class TestHFTIntegration:
    """Integration tests for HFT capabilities."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_order_flow(self):
        """Test complete order flow through HFT system."""
        # Initialize components
        dpdk_config = DPDKConfiguration(enabled=False)
        fpga_config = FPGAConfiguration(enabled=False)
        
        ull_engine = UltraLowLatencyEngine(dpdk_config, fpga_config)
        risk_manager = AdvancedRiskManager(fpga_config)
        
        await ull_engine.initialize()
        await risk_manager.initialize()
        
        try:
            # Create order request
            order_request = OrderRequest(
                symbol="AAPL",
                quantity=Decimal("100"),
                price=Decimal("150.0"),
                side=OrderSide.BUY,
                order_type=OrderType.LIMIT,
                asset_class=AssetClass.EQUITY
            )
            
            # Test risk validation
            start_time = time.time_ns()
            risk_result = await risk_manager.validate_order_ultra_fast(order_request)
            risk_latency = time.time_ns() - start_time
            
            assert risk_result['approved'] is True
            assert risk_latency < 100000  # 100μs
            
            # Test order processing
            order_data = {
                'order_id': 'integration_test_001',
                'symbol': order_request.symbol,
                'quantity': float(order_request.quantity),
                'price': float(order_request.price),
                'side': order_request.side.value,
                'order_type': order_request.order_type.value
            }
            
            start_time = time.time_ns()
            order_result = await ull_engine.process_order(order_data)
            order_latency = time.time_ns() - start_time
            
            assert order_result['status'] == 'PROCESSED'
            assert order_latency < 1000000  # 1ms for integration test
            
            # Verify metrics
            performance_summary = ull_engine.get_performance_summary()
            assert performance_summary['hft_metrics']['total_orders_processed'] > 0
            
            risk_metrics = risk_manager.get_risk_metrics()
            assert risk_metrics['performance_metrics']['total_risk_checks'] > 0
            
        finally:
            await ull_engine.cleanup()
            await risk_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self):
        """Test system performance under load."""
        dpdk_config = DPDKConfiguration(enabled=False)
        fpga_config = FPGAConfiguration(enabled=False)
        
        ull_engine = UltraLowLatencyEngine(dpdk_config, fpga_config)
        await ull_engine.initialize()
        
        try:
            # Process multiple orders
            order_count = 100
            latencies = []
            
            for i in range(order_count):
                order_data = {
                    'order_id': f'load_test_{i:03d}',
                    'symbol': 'AAPL',
                    'quantity': 100,
                    'price': 150.0 + (i * 0.01),
                    'side': 'BUY',
                    'order_type': 'LIMIT'
                }
                
                start_time = time.time_ns()
                result = await ull_engine.process_order(order_data)
                end_time = time.time_ns()
                
                latency_us = (end_time - start_time) / 1000
                latencies.append(latency_us)
                
                assert result['status'] == 'PROCESSED'
            
            # Analyze performance
            avg_latency = sum(latencies) / len(latencies)
            p95_latency = sorted(latencies)[int(len(latencies) * 0.95)]
            
            # Verify performance targets (relaxed for test environment)
            assert avg_latency < 1000  # 1ms average
            assert p95_latency < 2000   # 2ms P95
            
            # Verify metrics
            performance_summary = ull_engine.get_performance_summary()
            assert performance_summary['hft_metrics']['total_orders_processed'] >= order_count
            
        finally:
            await ull_engine.cleanup()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
