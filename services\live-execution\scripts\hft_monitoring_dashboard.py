#!/usr/bin/env python3
"""
AthenaTrader Phase 10 HFT Real-Time Monitoring Dashboard

This script provides real-time monitoring of HFT performance metrics,
system health, and compliance status for operational teams.
"""

import asyncio
import aiohttp
import json
import time
import os
from datetime import datetime
from typing import Dict, Any
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class HFTMonitoringDashboard:
    """Real-time HFT monitoring dashboard."""
    
    def __init__(self, base_url: str = "http://localhost:8006"):
        """Initialize monitoring dashboard."""
        self.base_url = base_url
        self.session = None
        self.monitoring_active = False
        
    async def initialize(self):
        """Initialize HTTP session."""
        self.session = aiohttp.ClientSession()
        logger.info("HFT Monitoring Dashboard initialized")
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.session:
            await self.session.close()
        logger.info("HFT Monitoring Dashboard cleanup completed")
    
    async def fetch_endpoint(self, endpoint: str) -> Dict[str, Any]:
        """Fetch data from HFT API endpoint."""
        try:
            url = f"{self.base_url}{endpoint}"
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to fetch {endpoint}: HTTP {response.status}")
                    return {}
        except Exception as e:
            logger.error(f"Error fetching {endpoint}: {e}")
            return {}
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        return await self.fetch_endpoint("/hft/status")
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        return await self.fetch_endpoint("/hft/performance")
    
    async def get_latency_metrics(self) -> Dict[str, Any]:
        """Get latency metrics."""
        return await self.fetch_endpoint("/hft/latency")
    
    async def get_risk_metrics(self) -> Dict[str, Any]:
        """Get risk management metrics."""
        return await self.fetch_endpoint("/hft/risk")
    
    async def get_dpdk_status(self) -> Dict[str, Any]:
        """Get DPDK status."""
        return await self.fetch_endpoint("/hft/dpdk")
    
    async def get_fpga_status(self) -> Dict[str, Any]:
        """Get FPGA status."""
        return await self.fetch_endpoint("/hft/fpga")
    
    def clear_screen(self):
        """Clear terminal screen."""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def format_latency(self, latency_us: float) -> str:
        """Format latency with color coding."""
        if latency_us <= 50:
            return f"✅ {latency_us:.1f}μs"
        elif latency_us <= 100:
            return f"⚠️  {latency_us:.1f}μs"
        else:
            return f"❌ {latency_us:.1f}μs"
    
    def format_throughput(self, current: int, target: int) -> str:
        """Format throughput with target comparison."""
        if current >= target:
            return f"✅ {current:,} ops/sec"
        elif current >= target * 0.8:
            return f"⚠️  {current:,} ops/sec"
        else:
            return f"❌ {current:,} ops/sec"
    
    def format_status(self, status: str) -> str:
        """Format status with icons."""
        status_map = {
            "OPERATIONAL": "✅ OPERATIONAL",
            "ACTIVE": "✅ ACTIVE", 
            "ARMED": "🛡️ ARMED",
            "CONNECTED": "🔗 CONNECTED",
            "ENABLED": "✅ ENABLED",
            "SIMULATED": "🔄 SIMULATED"
        }
        return status_map.get(status, f"❓ {status}")
    
    async def display_dashboard(self):
        """Display real-time monitoring dashboard."""
        self.clear_screen()
        
        print("=" * 100)
        print("🚀 ATHENATRADER PHASE 10 HFT REAL-TIME MONITORING DASHBOARD")
        print("=" * 100)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 🔄 Auto-refresh every 5 seconds")
        print()
        
        # Fetch all metrics concurrently
        status_task = asyncio.create_task(self.get_system_status())
        performance_task = asyncio.create_task(self.get_performance_metrics())
        latency_task = asyncio.create_task(self.get_latency_metrics())
        risk_task = asyncio.create_task(self.get_risk_metrics())
        dpdk_task = asyncio.create_task(self.get_dpdk_status())
        fpga_task = asyncio.create_task(self.get_fpga_status())
        
        status = await status_task
        performance = await performance_task
        latency = await latency_task
        risk = await risk_task
        dpdk = await dpdk_task
        fpga = await fpga_task
        
        # System Health Overview
        print("🏥 SYSTEM HEALTH OVERVIEW")
        print("-" * 50)
        if status:
            print(f"Overall Status: {self.format_status(status.get('overall_status', 'UNKNOWN'))}")
            print(f"Performance Grade: 🏆 {status.get('performance_grade', 'N/A')}")
            print(f"Uptime: ⏱️  {status.get('uptime_seconds', 0):.1f}s ({status.get('uptime_percentage', 0):.2f}%)")
            
            health = status.get('system_health', {})
            print(f"Ultra-Low Latency Engine: {self.format_status(health.get('ultra_low_latency_engine', 'UNKNOWN'))}")
            print(f"Risk Manager: {self.format_status(health.get('advanced_risk_manager', 'UNKNOWN'))}")
            print(f"Circuit Breakers: {self.format_status(health.get('circuit_breakers', 'UNKNOWN'))}")
            print(f"Market Data Feed: {self.format_status(health.get('market_data_feed', 'UNKNOWN'))}")
        print()
        
        # Performance Metrics
        print("📊 PERFORMANCE METRICS")
        print("-" * 50)
        if performance:
            latency_metrics = performance.get('latency_metrics', {})
            
            # Latency Performance
            print("⚡ Latency Performance:")
            order_proc = latency_metrics.get('order_processing', {})
            risk_checks = latency_metrics.get('risk_checks', {})
            sor_routing = latency_metrics.get('sor_routing', {})
            
            print(f"  Order Processing P95: {self.format_latency(order_proc.get('p95_microseconds', 0))} (target: 50μs)")
            print(f"  Risk Checks P95: {self.format_latency(risk_checks.get('p95_microseconds', 0))} (target: 5μs)")
            print(f"  SOR Routing P95: {self.format_latency(sor_routing.get('p95_microseconds', 0))} (target: 25μs)")
            
            # Throughput Performance
            throughput_metrics = performance.get('throughput_metrics', {})
            print("\n🚀 Throughput Performance:")
            order_tp = throughput_metrics.get('order_processing', {})
            risk_tp = throughput_metrics.get('risk_checks', {})
            
            print(f"  Order Processing: {self.format_throughput(order_tp.get('current_ops_per_second', 0), 100000)}")
            print(f"  Risk Checks: {self.format_throughput(risk_tp.get('current_ops_per_second', 0), 500000)}")
        print()
        
        # Real-Time Latency
        print("⚡ REAL-TIME LATENCY MONITORING")
        print("-" * 50)
        if latency:
            rt_latencies = latency.get('real_time_latencies', {})
            print(f"Last Order Processing: {self.format_latency(rt_latencies.get('last_order_processing_us', 0))}")
            print(f"Last Risk Check: {self.format_latency(rt_latencies.get('last_risk_check_us', 0))}")
            print(f"Last SOR Routing: {self.format_latency(rt_latencies.get('last_sor_routing_us', 0))}")
            print(f"Last Market Data: {self.format_latency(rt_latencies.get('last_market_data_us', 0))}")
            
            compliance = latency.get('compliance_status', {})
            print(f"\nCompliance: {compliance.get('targets_met_percent', 0):.1f}% targets met")
        print()
        
        # Risk Management
        print("🛡️ RISK MANAGEMENT STATUS")
        print("-" * 50)
        if risk:
            print(f"Risk Engine: {self.format_status(risk.get('risk_engine_status', 'UNKNOWN'))}")
            print(f"Total Risk Checks: {risk.get('total_risk_checks', 0):,}")
            
            circuit_breakers = risk.get('circuit_breakers', {})
            print("\nCircuit Breakers:")
            for cb_name, cb_data in circuit_breakers.items():
                status_icon = "🔴" if cb_data.get('triggered', False) else "🟢"
                print(f"  {cb_name.replace('_', ' ').title()}: {status_icon} {self.format_status(cb_data.get('status', 'UNKNOWN'))}")
                print(f"    Current: {cb_data.get('current_value', 0):,} / Threshold: {cb_data.get('threshold', 0):,}")
        print()
        
        # Hardware Acceleration
        print("🔧 HARDWARE ACCELERATION STATUS")
        print("-" * 50)
        if dpdk:
            print(f"DPDK Status: {self.format_status(dpdk.get('status', 'UNKNOWN'))}")
            if dpdk.get('interfaces'):
                interface = dpdk['interfaces'][0]
                print(f"  Network: {interface.get('name', 'N/A')} @ {interface.get('link_speed', 'N/A')}")
                print(f"  Packets/sec: {interface.get('packets_per_second', 0):,}")
        
        if fpga:
            print(f"FPGA Status: {self.format_status(fpga.get('status', 'UNKNOWN'))}")
            if fpga.get('devices'):
                device = fpga['devices'][0]
                print(f"  Device: {device.get('name', 'N/A')}")
                print(f"  Utilization: {device.get('utilization_percent', 0):.1f}%")
        print()
        
        # Operational Metrics
        print("📈 OPERATIONAL METRICS")
        print("-" * 50)
        if status:
            ops = status.get('operational_metrics', {})
            print(f"Orders Processed: {ops.get('total_orders_processed', 0):,}")
            print(f"Risk Checks: {ops.get('total_risk_checks', 0):,}")
            print(f"Success Rate: {ops.get('success_rate_percent', 0):.3f}%")
            print(f"Error Rate: {ops.get('error_rate_percent', 0):.3f}%")
        
        # Compliance Status
        if status:
            compliance = status.get('compliance_status', {})
            print(f"\n🏛️ Regulatory Compliance:")
            print(f"  MiFID II: {'✅' if compliance.get('mifid_ii_compliant') else '❌'}")
            print(f"  FINRA: {'✅' if compliance.get('finra_compliant') else '❌'}")
            print(f"  SOX: {'✅' if compliance.get('sox_compliant') else '❌'}")
            print(f"  Audit Trail: {'✅' if compliance.get('audit_trail_active') else '❌'}")
        
        print("\n" + "=" * 100)
        print("🔄 Press Ctrl+C to stop monitoring | 📊 Dashboard auto-refreshes every 5 seconds")
        print("=" * 100)
    
    async def start_monitoring(self, refresh_interval: int = 5):
        """Start real-time monitoring with auto-refresh."""
        self.monitoring_active = True
        logger.info(f"Starting HFT monitoring dashboard (refresh every {refresh_interval}s)")
        
        try:
            while self.monitoring_active:
                await self.display_dashboard()
                await asyncio.sleep(refresh_interval)
        except KeyboardInterrupt:
            logger.info("Monitoring stopped by user")
        except Exception as e:
            logger.error(f"Monitoring error: {e}")
        finally:
            self.monitoring_active = False
    
    def stop_monitoring(self):
        """Stop monitoring."""
        self.monitoring_active = False


async def main():
    """Main monitoring function."""
    dashboard = HFTMonitoringDashboard()
    
    try:
        await dashboard.initialize()
        await dashboard.start_monitoring(refresh_interval=5)
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
    finally:
        await dashboard.cleanup()


if __name__ == "__main__":
    print("🚀 Starting AthenaTrader Phase 10 HFT Monitoring Dashboard...")
    print("📊 Real-time monitoring of HFT performance and compliance")
    print("🔄 Auto-refresh every 5 seconds")
    print()
    
    asyncio.run(main())
