"""
AthenaTrader Phase 11 Advanced Analytics Engine - ML Models API Routes

PRIORITY 1: ML-Based Market Prediction Models
- LSTM/Transformer neural networks for multi-timeframe price prediction
- Real-time sentiment analysis engine
- Market regime detection system using Hidden Markov Models
- Volatility forecasting models using GARCH
"""

import asyncio
import logging
import numpy as np
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Depends, Query, Body
from pydantic import BaseModel, Field
from ...core.model_manager import ModelManager
from ...core.data_pipeline import DataPipeline
from ...core.logging_config import performance_logger, ml_model_logger
from ...core.redis_client import model_cache, data_stream

logger = logging.getLogger("ml_models")
router = APIRouter()


# Pydantic models for request/response
class PricePredictionRequest(BaseModel):
    symbol: str = Field(..., description="Trading symbol (e.g., AAPL, BTC-USD)")
    horizon_minutes: int = Field(..., description="Prediction horizon in minutes (1, 5, 15, 60)")
    model_type: str = Field(default="lstm", description="Model type: lstm or transformer")
    use_cache: bool = Field(default=True, description="Use cached predictions if available")


class PricePredictionResponse(BaseModel):
    symbol: str
    model_name: str
    predicted_price: float
    horizon_minutes: int
    confidence_score: float
    inference_time_ms: float
    timestamp: str
    features_used: Dict[str, Any]
    cached: bool = False


class SentimentAnalysisRequest(BaseModel):
    symbol: str = Field(..., description="Trading symbol")
    text: str = Field(..., description="Text to analyze for sentiment")
    source: str = Field(default="manual", description="Source of the text (news, twitter, etc.)")


class SentimentAnalysisResponse(BaseModel):
    symbol: str
    source: str
    sentiment_score: float = Field(..., description="Sentiment score from -1 (negative) to 1 (positive)")
    sentiment_label: str = Field(..., description="Sentiment label: positive, negative, neutral")
    confidence: float
    inference_time_ms: float
    timestamp: str


class MarketRegimeRequest(BaseModel):
    symbol: str = Field(..., description="Trading symbol")
    lookback_days: int = Field(default=30, description="Number of days to analyze for regime detection")


class MarketRegimeResponse(BaseModel):
    symbol: str
    regime_type: str = Field(..., description="Detected regime: bull, bear, sideways")
    regime_probability: float
    volatility_level: str = Field(..., description="Volatility level: low, medium, high")
    trend_strength: float
    inference_time_ms: float
    timestamp: str


class VolatilityForecastRequest(BaseModel):
    symbol: str = Field(..., description="Trading symbol")
    forecast_horizon: str = Field(default="1_day", description="Forecast horizon: 1_day, 1_week, 1_month")


class VolatilityForecastResponse(BaseModel):
    symbol: str
    forecasted_volatility: float
    confidence: float
    forecast_horizon: str
    inference_time_ms: float
    timestamp: str


class MultiSymbolPredictionRequest(BaseModel):
    symbols: List[str] = Field(..., description="List of trading symbols")
    horizon_minutes: int = Field(..., description="Prediction horizon in minutes")
    model_type: str = Field(default="lstm", description="Model type: lstm or transformer")


# Dependency to get model manager
async def get_model_manager() -> ModelManager:
    """Get model manager from app state."""
    from fastapi import Request
    request = Request.scope
    if hasattr(request.get("app").state, "model_manager"):
        return request.get("app").state.model_manager
    raise HTTPException(status_code=503, detail="Model manager not initialized")


# Dependency to get data pipeline
async def get_data_pipeline() -> DataPipeline:
    """Get data pipeline from app state."""
    from fastapi import Request
    request = Request.scope
    if hasattr(request.get("app").state, "data_pipeline"):
        return request.get("app").state.data_pipeline
    raise HTTPException(status_code=503, detail="Data pipeline not initialized")


@router.get("/status")
async def get_ml_models_status():
    """Get status of all ML models."""
    try:
        # This would normally use dependency injection, but for simplicity:
        from ....main import app
        if hasattr(app.state, 'model_manager'):
            model_manager = app.state.model_manager
            status = await model_manager.get_model_status()
            return {
                "status": "operational",
                "models": status,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=503, detail="Model manager not available")
    except Exception as e:
        logger.error(f"Error getting ML models status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/predict/price", response_model=PricePredictionResponse)
async def predict_price(request: PricePredictionRequest):
    """
    Generate price prediction using LSTM or Transformer models.
    
    Supports multiple timeframes:
    - 1 minute: Short-term scalping predictions
    - 5 minutes: Intraday trading signals
    - 15 minutes: Swing trading entries
    - 60 minutes: Position trading decisions
    """
    try:
        start_time = datetime.now()
        
        # Validate inputs
        if request.horizon_minutes not in [1, 5, 15, 60]:
            raise HTTPException(
                status_code=400, 
                detail="Horizon must be 1, 5, 15, or 60 minutes"
            )
        
        if request.model_type not in ["lstm", "transformer"]:
            raise HTTPException(
                status_code=400,
                detail="Model type must be 'lstm' or 'transformer'"
            )
        
        # Get model manager and data pipeline
        from ....main import app
        model_manager = app.state.model_manager
        data_pipeline = app.state.data_pipeline
        
        # Check cache first if requested
        if request.use_cache:
            cached_prediction = await model_cache.get_cached_prediction(
                f"{request.model_type}_price_predictor",
                request.symbol,
                request.horizon_minutes
            )
            if cached_prediction:
                cached_prediction["cached"] = True
                return PricePredictionResponse(**cached_prediction)
        
        # Get market data features
        features_array = await data_pipeline.get_features_array(
            request.symbol, 
            lookback_periods=100
        )
        
        if features_array is None:
            raise HTTPException(
                status_code=404,
                detail=f"Insufficient market data for symbol {request.symbol}"
            )
        
        # Generate prediction
        prediction_result = await model_manager.predict_price(
            symbol=request.symbol,
            data=features_array,
            horizon_minutes=request.horizon_minutes,
            model_type=request.model_type
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/ml/predict/price",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        ml_model_logger.log_prediction(
            model_name=prediction_result["model_name"],
            input_features=features_array.shape[0],
            prediction_confidence=prediction_result["confidence_score"]
        )
        
        # Publish prediction to stream
        await data_stream.publish_prediction(
            model_name=prediction_result["model_name"],
            symbol=request.symbol,
            prediction=prediction_result
        )
        
        return PricePredictionResponse(**prediction_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in price prediction: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/analyze/sentiment", response_model=SentimentAnalysisResponse)
async def analyze_sentiment(request: SentimentAnalysisRequest):
    """
    Analyze sentiment of text related to a trading symbol.
    
    Supports multiple text sources:
    - News headlines and articles
    - Social media posts (Twitter, Reddit)
    - Analyst reports
    - Earnings call transcripts
    """
    try:
        start_time = datetime.now()
        
        # Validate inputs
        if len(request.text.strip()) == 0:
            raise HTTPException(status_code=400, detail="Text cannot be empty")
        
        if len(request.text) > 5000:
            raise HTTPException(status_code=400, detail="Text too long (max 5000 characters)")
        
        # Get model manager
        from ....main import app
        model_manager = app.state.model_manager
        
        # Analyze sentiment
        sentiment_result = await model_manager.analyze_sentiment(
            symbol=request.symbol,
            text=request.text,
            source=request.source
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/ml/analyze/sentiment",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        # Publish sentiment to stream
        await data_stream.publish_sentiment(request.symbol, sentiment_result)
        
        return SentimentAnalysisResponse(**sentiment_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in sentiment analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/detect/regime", response_model=MarketRegimeResponse)
async def detect_market_regime(request: MarketRegimeRequest):
    """
    Detect current market regime using Hidden Markov Models.
    
    Identifies three market states:
    - Bull market: Sustained upward trend with low volatility
    - Bear market: Sustained downward trend with high volatility
    - Sideways market: Range-bound trading with moderate volatility
    """
    try:
        start_time = datetime.now()
        
        # Validate inputs
        if request.lookback_days < 10:
            raise HTTPException(
                status_code=400,
                detail="Lookback period must be at least 10 days"
            )
        
        if request.lookback_days > 252:
            raise HTTPException(
                status_code=400,
                detail="Lookback period cannot exceed 252 days (1 trading year)"
            )
        
        # Get model manager and data pipeline
        from ....main import app
        model_manager = app.state.model_manager
        data_pipeline = app.state.data_pipeline
        
        # Get market data for regime analysis
        market_data = await data_pipeline.get_market_data(
            request.symbol,
            lookback_minutes=request.lookback_days * 24 * 60  # Convert days to minutes
        )
        
        if not market_data or len(market_data["prices"]) < request.lookback_days:
            raise HTTPException(
                status_code=404,
                detail=f"Insufficient market data for symbol {request.symbol}"
            )
        
        # Calculate returns for regime detection
        prices = [p["close"] for p in market_data["prices"]]
        returns = np.diff(np.log(prices))  # Log returns
        
        # Detect market regime
        regime_result = await model_manager.detect_market_regime(
            symbol=request.symbol,
            returns=returns
        )
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/ml/detect/regime",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return MarketRegimeResponse(**regime_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in regime detection: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/forecast/volatility", response_model=VolatilityForecastResponse)
async def forecast_volatility(request: VolatilityForecastRequest):
    """
    Forecast volatility using GARCH and stochastic volatility models.
    
    Provides volatility forecasts for:
    - 1 day: Intraday risk management
    - 1 week: Short-term position sizing
    - 1 month: Portfolio allocation decisions
    """
    try:
        start_time = datetime.now()
        
        # Validate inputs
        if request.forecast_horizon not in ["1_day", "1_week", "1_month"]:
            raise HTTPException(
                status_code=400,
                detail="Forecast horizon must be '1_day', '1_week', or '1_month'"
            )
        
        # Get model manager and data pipeline
        from ....main import app
        model_manager = app.state.model_manager
        data_pipeline = app.state.data_pipeline
        
        # Get market data for volatility analysis
        lookback_minutes = 30 * 24 * 60  # 30 days of data
        market_data = await data_pipeline.get_market_data(
            request.symbol,
            lookback_minutes=lookback_minutes
        )
        
        if not market_data or len(market_data["prices"]) < 100:
            raise HTTPException(
                status_code=404,
                detail=f"Insufficient market data for symbol {request.symbol}"
            )
        
        # Calculate returns for volatility forecasting
        prices = [p["close"] for p in market_data["prices"]]
        returns = np.diff(np.log(prices))  # Log returns
        
        # Forecast volatility
        volatility_result = await model_manager.forecast_volatility(
            symbol=request.symbol,
            returns=returns
        )
        
        # Update result with requested horizon
        volatility_result["forecast_horizon"] = request.forecast_horizon
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/ml/forecast/volatility",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return VolatilityForecastResponse(**volatility_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in volatility forecasting: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/predict/multi-symbol")
async def predict_multi_symbol(request: MultiSymbolPredictionRequest):
    """
    Generate predictions for multiple symbols simultaneously.
    
    Optimized for portfolio-level analysis and cross-asset correlation studies.
    """
    try:
        start_time = datetime.now()
        
        # Validate inputs
        if len(request.symbols) == 0:
            raise HTTPException(status_code=400, detail="Symbols list cannot be empty")
        
        if len(request.symbols) > 50:
            raise HTTPException(status_code=400, detail="Maximum 50 symbols per request")
        
        # Get model manager and data pipeline
        from ....main import app
        model_manager = app.state.model_manager
        data_pipeline = app.state.data_pipeline
        
        # Generate predictions for all symbols concurrently
        prediction_tasks = []
        for symbol in request.symbols:
            task = asyncio.create_task(
                _predict_single_symbol(
                    model_manager, data_pipeline, symbol, 
                    request.horizon_minutes, request.model_type
                )
            )
            prediction_tasks.append(task)
        
        # Wait for all predictions to complete
        prediction_results = await asyncio.gather(*prediction_tasks, return_exceptions=True)
        
        # Process results
        successful_predictions = []
        failed_predictions = []
        
        for i, result in enumerate(prediction_results):
            symbol = request.symbols[i]
            if isinstance(result, Exception):
                failed_predictions.append({
                    "symbol": symbol,
                    "error": str(result)
                })
            else:
                successful_predictions.append(result)
        
        # Log performance
        api_time = (datetime.now() - start_time).total_seconds() * 1000
        performance_logger.log_api_performance(
            endpoint="/ml/predict/multi-symbol",
            method="POST",
            response_time_ms=api_time,
            status_code=200
        )
        
        return {
            "successful_predictions": successful_predictions,
            "failed_predictions": failed_predictions,
            "total_symbols": len(request.symbols),
            "success_rate": len(successful_predictions) / len(request.symbols),
            "processing_time_ms": api_time,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in multi-symbol prediction: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _predict_single_symbol(model_manager: ModelManager, data_pipeline: DataPipeline,
                                symbol: str, horizon_minutes: int, model_type: str) -> Dict[str, Any]:
    """Helper function to predict price for a single symbol."""
    try:
        # Get features
        features_array = await data_pipeline.get_features_array(symbol, lookback_periods=100)
        if features_array is None:
            raise ValueError(f"Insufficient data for {symbol}")
        
        # Generate prediction
        prediction = await model_manager.predict_price(
            symbol=symbol,
            data=features_array,
            horizon_minutes=horizon_minutes,
            model_type=model_type
        )
        
        return prediction
        
    except Exception as e:
        raise ValueError(f"Prediction failed for {symbol}: {str(e)}")


@router.get("/models/performance")
async def get_models_performance():
    """Get performance metrics for all ML models."""
    try:
        from ....main import app
        model_manager = app.state.model_manager
        
        # Get model status
        model_status = await model_manager.get_model_status()
        
        # Get performance metrics from Redis
        from ...core.redis_client import performance_metrics
        
        performance_summary = await performance_metrics.get_performance_summary(
            "model_performance", hours=24
        )
        
        return {
            "model_status": model_status,
            "performance_summary": performance_summary,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting models performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data/market/{symbol}")
async def get_market_data(symbol: str, lookback_minutes: int = Query(default=60, ge=1, le=1440)):
    """Get recent market data for a symbol."""
    try:
        from ....main import app
        data_pipeline = app.state.data_pipeline
        
        market_data = await data_pipeline.get_market_data(symbol, lookback_minutes)
        
        if not market_data:
            raise HTTPException(
                status_code=404,
                detail=f"No market data available for symbol {symbol}"
            )
        
        return market_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting market data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data/sentiment/{symbol}")
async def get_sentiment_data(symbol: str):
    """Get recent news and sentiment data for a symbol."""
    try:
        from ....main import app
        data_pipeline = app.state.data_pipeline
        
        sentiment_data = await data_pipeline.get_news_sentiment(symbol)
        
        if not sentiment_data:
            raise HTTPException(
                status_code=404,
                detail=f"No sentiment data available for symbol {symbol}"
            )
        
        return sentiment_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sentiment data: {e}")
        raise HTTPException(status_code=500, detail=str(e))
