#!/usr/bin/env python3
"""
AthenaTrader Phase 10 System-Level Performance Tuning Script

This script performs comprehensive system-level performance tuning for
ultra-low latency HFT operations, including:

1. Huge pages configuration (minimum 1024 x 2MB pages)
2. CPU isolation for cores 0-1
3. IOMMU enablement for device passthrough
4. CPU governor and frequency scaling optimization
5. Kernel network parameter tuning
6. IRQ affinity optimization
7. System service optimization
"""

import os
import sys
import subprocess
import logging
import re
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SystemTuner:
    """System performance tuning for HFT deployment."""
    
    def __init__(self, dry_run: bool = False):
        """Initialize system tuner."""
        self.dry_run = dry_run
        self.tuning_log = []
        self.backup_files = {}
        
        if dry_run:
            logger.info("Running in DRY RUN mode - no changes will be made")
        else:
            logger.info("Running in LIVE mode - system changes will be applied")
    
    def tune_all_systems(self) -> bool:
        """Perform comprehensive system tuning."""
        logger.info("Starting comprehensive system tuning for HFT...")
        
        tuning_steps = [
            ("Configure huge pages", self.configure_huge_pages),
            ("Configure CPU isolation", self.configure_cpu_isolation),
            ("Enable IOMMU", self.enable_iommu),
            ("Optimize CPU governor", self.optimize_cpu_governor),
            ("Tune kernel network parameters", self.tune_network_parameters),
            ("Optimize IRQ affinity", self.optimize_irq_affinity),
            ("Disable unnecessary services", self.disable_unnecessary_services),
            ("Configure system limits", self.configure_system_limits),
            ("Optimize filesystem", self.optimize_filesystem)
        ]
        
        success = True
        for step_name, step_func in tuning_steps:
            logger.info(f"Executing: {step_name}")
            
            try:
                if not step_func():
                    logger.error(f"Step failed: {step_name}")
                    success = False
                else:
                    logger.info(f"Step completed: {step_name}")
            except Exception as e:
                logger.error(f"Step failed with exception: {step_name} - {e}")
                success = False
        
        # Save tuning log
        self._save_tuning_log()
        
        if success:
            logger.info("System tuning completed successfully")
            logger.info("REBOOT REQUIRED for all changes to take effect")
        else:
            logger.error("System tuning completed with errors")
        
        return success
    
    def configure_huge_pages(self) -> bool:
        """Configure huge pages for DPDK."""
        logger.info("Configuring huge pages...")
        
        try:
            # Check current huge pages configuration
            current_hugepages = self._get_current_hugepages()
            target_hugepages = 1024  # 2GB total (1024 x 2MB pages)
            
            logger.info(f"Current huge pages: {current_hugepages}")
            logger.info(f"Target huge pages: {target_hugepages}")
            
            if current_hugepages >= target_hugepages:
                logger.info("Huge pages already configured")
                return True
            
            # Configure huge pages at runtime
            if not self.dry_run:
                self._run_command(f"echo {target_hugepages} | sudo tee /sys/kernel/mm/hugepages/hugepages-2048kB/nr_hugepages")
            
            # Make persistent by adding to /etc/sysctl.conf
            sysctl_line = f"vm.nr_hugepages = {target_hugepages}"
            if not self._add_to_sysctl(sysctl_line):
                return False
            
            # Configure huge pages mount point
            if not self._configure_hugepages_mount():
                return False
            
            # Add to GRUB configuration for boot-time allocation
            grub_params = f"hugepagesz=2M hugepages={target_hugepages}"
            if not self._add_to_grub(grub_params):
                return False
            
            self._log_action("configure_huge_pages", "success", f"Configured {target_hugepages} huge pages")
            return True
            
        except Exception as e:
            logger.error(f"Huge pages configuration failed: {e}")
            self._log_action("configure_huge_pages", "failed", str(e))
            return False
    
    def configure_cpu_isolation(self) -> bool:
        """Configure CPU isolation for HFT cores."""
        logger.info("Configuring CPU isolation...")
        
        try:
            # Isolate cores 0-1 for HFT workloads
            isolated_cores = "0,1"
            
            # Check current isolation
            current_isolation = self._get_current_cpu_isolation()
            if isolated_cores in current_isolation:
                logger.info("CPU isolation already configured")
                return True
            
            # Add CPU isolation parameters to GRUB
            isolation_params = f"isolcpus={isolated_cores} nohz_full={isolated_cores} rcu_nocbs={isolated_cores}"
            if not self._add_to_grub(isolation_params):
                return False
            
            # Configure CPU affinity for system processes
            if not self._configure_system_cpu_affinity():
                return False
            
            self._log_action("configure_cpu_isolation", "success", f"Isolated cores {isolated_cores}")
            return True
            
        except Exception as e:
            logger.error(f"CPU isolation configuration failed: {e}")
            self._log_action("configure_cpu_isolation", "failed", str(e))
            return False
    
    def enable_iommu(self) -> bool:
        """Enable IOMMU for device passthrough."""
        logger.info("Enabling IOMMU...")
        
        try:
            # Check if IOMMU is already enabled
            if self._is_iommu_enabled():
                logger.info("IOMMU already enabled")
                return True
            
            # Detect CPU vendor
            cpu_vendor = self._get_cpu_vendor()
            
            if cpu_vendor == "intel":
                iommu_params = "intel_iommu=on iommu=pt"
            elif cpu_vendor == "amd":
                iommu_params = "amd_iommu=on iommu=pt"
            else:
                logger.warning("Unknown CPU vendor - using generic IOMMU settings")
                iommu_params = "iommu=pt"
            
            # Add IOMMU parameters to GRUB
            if not self._add_to_grub(iommu_params):
                return False
            
            self._log_action("enable_iommu", "success", f"Enabled IOMMU with {iommu_params}")
            return True
            
        except Exception as e:
            logger.error(f"IOMMU enablement failed: {e}")
            self._log_action("enable_iommu", "failed", str(e))
            return False
    
    def optimize_cpu_governor(self) -> bool:
        """Optimize CPU governor and frequency scaling."""
        logger.info("Optimizing CPU governor...")
        
        try:
            # Set CPU governor to performance
            if not self.dry_run:
                self._run_command("echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor")
            
            # Disable CPU frequency scaling
            try:
                if not self.dry_run:
                    self._run_command("sudo cpupower frequency-set -g performance")
            except Exception:
                logger.warning("cpupower not available - manual governor setting applied")
            
            # Disable CPU idle states for isolated cores
            if not self.dry_run:
                for core in [0, 1]:  # Isolated cores
                    try:
                        self._run_command(f"echo 1 | sudo tee /sys/devices/system/cpu/cpu{core}/cpuidle/state*/disable")
                    except Exception:
                        logger.warning(f"Could not disable idle states for core {core}")
            
            # Make CPU governor persistent
            systemd_service = self._create_cpu_governor_service()
            if systemd_service and not self.dry_run:
                self._run_command("sudo systemctl enable cpu-performance-governor.service")
            
            self._log_action("optimize_cpu_governor", "success", "Set CPU governor to performance")
            return True
            
        except Exception as e:
            logger.error(f"CPU governor optimization failed: {e}")
            self._log_action("optimize_cpu_governor", "failed", str(e))
            return False
    
    def tune_network_parameters(self) -> bool:
        """Tune kernel network parameters for ultra-low latency."""
        logger.info("Tuning kernel network parameters...")
        
        try:
            network_params = {
                # Increase network buffer sizes
                'net.core.rmem_max': '134217728',
                'net.core.wmem_max': '134217728',
                'net.core.rmem_default': '65536',
                'net.core.wmem_default': '65536',
                'net.ipv4.tcp_rmem': '4096 65536 134217728',
                'net.ipv4.tcp_wmem': '4096 65536 134217728',
                
                # Increase network device queue length
                'net.core.netdev_max_backlog': '30000',
                'net.core.netdev_budget': '600',
                
                # Optimize TCP settings
                'net.ipv4.tcp_no_metrics_save': '1',
                'net.ipv4.tcp_congestion_control': 'bbr',
                'net.ipv4.tcp_low_latency': '1',
                'net.ipv4.tcp_timestamps': '0',
                'net.ipv4.tcp_sack': '0',
                
                # Reduce network latency
                'net.ipv4.tcp_fastopen': '3',
                'net.core.busy_read': '50',
                'net.core.busy_poll': '50',
                
                # Optimize for high-frequency trading
                'net.ipv4.tcp_thin_linear_timeouts': '1',
                'net.ipv4.tcp_thin_dupack': '1'
            }
            
            for param, value in network_params.items():
                sysctl_line = f"{param} = {value}"
                if not self._add_to_sysctl(sysctl_line):
                    return False
            
            # Apply sysctl changes immediately
            if not self.dry_run:
                self._run_command("sudo sysctl -p")
            
            self._log_action("tune_network_parameters", "success", f"Configured {len(network_params)} network parameters")
            return True
            
        except Exception as e:
            logger.error(f"Network parameter tuning failed: {e}")
            self._log_action("tune_network_parameters", "failed", str(e))
            return False
    
    def optimize_irq_affinity(self) -> bool:
        """Optimize IRQ affinity for network interfaces."""
        logger.info("Optimizing IRQ affinity...")
        
        try:
            # Get network interfaces
            interfaces = self._get_network_interfaces()
            
            for interface in interfaces:
                if 'eth' in interface or 'ens' in interface:
                    # Set IRQ affinity to non-isolated cores (cores 2+)
                    irq_numbers = self._get_interface_irqs(interface)
                    
                    for irq in irq_numbers:
                        # Bind IRQ to cores 2-3 (non-isolated cores)
                        if not self.dry_run:
                            try:
                                self._run_command(f"echo 0c | sudo tee /proc/irq/{irq}/smp_affinity")
                                logger.info(f"Set IRQ {irq} affinity for {interface}")
                            except Exception:
                                logger.warning(f"Could not set IRQ affinity for {irq}")
            
            self._log_action("optimize_irq_affinity", "success", "Optimized IRQ affinity")
            return True
            
        except Exception as e:
            logger.error(f"IRQ affinity optimization failed: {e}")
            self._log_action("optimize_irq_affinity", "failed", str(e))
            return False
    
    def disable_unnecessary_services(self) -> bool:
        """Disable unnecessary system services."""
        logger.info("Disabling unnecessary services...")
        
        try:
            # Services to disable for HFT optimization
            services_to_disable = [
                'bluetooth.service',
                'cups.service',
                'avahi-daemon.service',
                'ModemManager.service',
                'packagekit.service',
                'snapd.service',
                'thermald.service'
            ]
            
            disabled_count = 0
            for service in services_to_disable:
                try:
                    if not self.dry_run:
                        result = self._run_command(f"sudo systemctl disable {service}")
                        if result:
                            disabled_count += 1
                            logger.info(f"Disabled service: {service}")
                except Exception:
                    logger.debug(f"Service {service} not found or already disabled")
            
            self._log_action("disable_unnecessary_services", "success", f"Disabled {disabled_count} services")
            return True
            
        except Exception as e:
            logger.error(f"Service optimization failed: {e}")
            self._log_action("disable_unnecessary_services", "failed", str(e))
            return False
    
    def configure_system_limits(self) -> bool:
        """Configure system limits for HFT applications."""
        logger.info("Configuring system limits...")
        
        try:
            limits_config = [
                "* soft nofile 1048576",
                "* hard nofile 1048576",
                "* soft nproc 1048576", 
                "* hard nproc 1048576",
                "* soft memlock unlimited",
                "* hard memlock unlimited",
                "root soft nofile 1048576",
                "root hard nofile 1048576",
                "root soft nproc 1048576",
                "root hard nproc 1048576"
            ]
            
            limits_file = "/etc/security/limits.conf"
            if not self.dry_run:
                self._backup_file(limits_file)
                
                with open(limits_file, 'a') as f:
                    f.write("\n# AthenaTrader HFT Limits\n")
                    for limit in limits_config:
                        f.write(f"{limit}\n")
            
            self._log_action("configure_system_limits", "success", "Configured system limits")
            return True
            
        except Exception as e:
            logger.error(f"System limits configuration failed: {e}")
            self._log_action("configure_system_limits", "failed", str(e))
            return False
    
    def optimize_filesystem(self) -> bool:
        """Optimize filesystem for low-latency operations."""
        logger.info("Optimizing filesystem...")
        
        try:
            # Add noatime mount option to reduce filesystem overhead
            fstab_file = "/etc/fstab"
            if not self.dry_run:
                self._backup_file(fstab_file)
                
                # Read current fstab
                with open(fstab_file, 'r') as f:
                    fstab_content = f.read()
                
                # Add noatime option to root filesystem
                modified_content = re.sub(
                    r'(\S+\s+/\s+\S+\s+)(\S+)',
                    r'\1\2,noatime',
                    fstab_content
                )
                
                # Write modified fstab
                with open(fstab_file, 'w') as f:
                    f.write(modified_content)
            
            self._log_action("optimize_filesystem", "success", "Added noatime mount option")
            return True
            
        except Exception as e:
            logger.error(f"Filesystem optimization failed: {e}")
            self._log_action("optimize_filesystem", "failed", str(e))
            return False
    
    def _get_current_hugepages(self) -> int:
        """Get current huge pages configuration."""
        try:
            with open('/proc/meminfo', 'r') as f:
                meminfo = f.read()
            
            match = re.search(r'HugePages_Total:\s*(\d+)', meminfo)
            return int(match.group(1)) if match else 0
        except Exception:
            return 0
    
    def _get_current_cpu_isolation(self) -> str:
        """Get current CPU isolation configuration."""
        try:
            with open('/proc/cmdline', 'r') as f:
                cmdline = f.read()
            
            match = re.search(r'isolcpus=([0-9,-]+)', cmdline)
            return match.group(1) if match else ""
        except Exception:
            return ""
    
    def _is_iommu_enabled(self) -> bool:
        """Check if IOMMU is enabled."""
        try:
            with open('/proc/cmdline', 'r') as f:
                cmdline = f.read()
            
            return 'intel_iommu=on' in cmdline or 'amd_iommu=on' in cmdline
        except Exception:
            return False
    
    def _get_cpu_vendor(self) -> str:
        """Get CPU vendor."""
        try:
            with open('/proc/cpuinfo', 'r') as f:
                cpuinfo = f.read()
            
            if 'GenuineIntel' in cpuinfo:
                return "intel"
            elif 'AuthenticAMD' in cpuinfo:
                return "amd"
            else:
                return "unknown"
        except Exception:
            return "unknown"
    
    def _add_to_sysctl(self, line: str) -> bool:
        """Add line to sysctl.conf."""
        try:
            sysctl_file = "/etc/sysctl.conf"
            
            if not self.dry_run:
                self._backup_file(sysctl_file)
                
                # Check if line already exists
                with open(sysctl_file, 'r') as f:
                    content = f.read()
                
                if line not in content:
                    with open(sysctl_file, 'a') as f:
                        f.write(f"\n{line}\n")
            
            return True
        except Exception as e:
            logger.error(f"Failed to add to sysctl.conf: {e}")
            return False
    
    def _add_to_grub(self, params: str) -> bool:
        """Add parameters to GRUB configuration."""
        try:
            grub_file = "/etc/default/grub"
            
            if not self.dry_run:
                self._backup_file(grub_file)
                
                # Read current GRUB config
                with open(grub_file, 'r') as f:
                    content = f.read()
                
                # Modify GRUB_CMDLINE_LINUX_DEFAULT
                if 'GRUB_CMDLINE_LINUX_DEFAULT=' in content:
                    # Add parameters to existing line
                    content = re.sub(
                        r'GRUB_CMDLINE_LINUX_DEFAULT="([^"]*)"',
                        rf'GRUB_CMDLINE_LINUX_DEFAULT="\1 {params}"',
                        content
                    )
                else:
                    # Add new line
                    content += f'\nGRUB_CMDLINE_LINUX_DEFAULT="{params}"\n'
                
                # Write modified GRUB config
                with open(grub_file, 'w') as f:
                    f.write(content)
                
                # Update GRUB
                self._run_command("sudo update-grub")
            
            return True
        except Exception as e:
            logger.error(f"Failed to update GRUB: {e}")
            return False
    
    def _configure_hugepages_mount(self) -> bool:
        """Configure huge pages mount point."""
        try:
            mount_point = "/mnt/huge"
            
            if not self.dry_run:
                # Create mount point
                os.makedirs(mount_point, exist_ok=True)
                
                # Add to fstab
                fstab_line = "nodev /mnt/huge hugetlbfs defaults 0 0"
                fstab_file = "/etc/fstab"
                
                with open(fstab_file, 'r') as f:
                    content = f.read()
                
                if fstab_line not in content:
                    with open(fstab_file, 'a') as f:
                        f.write(f"\n{fstab_line}\n")
                
                # Mount immediately
                self._run_command(f"sudo mount -t hugetlbfs nodev {mount_point}")
            
            return True
        except Exception as e:
            logger.error(f"Huge pages mount configuration failed: {e}")
            return False
    
    def _configure_system_cpu_affinity(self) -> bool:
        """Configure CPU affinity for system processes."""
        try:
            # Move system processes to non-isolated cores
            if not self.dry_run:
                # Move IRQ threads
                self._run_command("echo 0c | sudo tee /proc/irq/default_smp_affinity")
                
                # Move RCU threads
                for pid in self._get_rcu_pids():
                    self._run_command(f"sudo taskset -cp 2-3 {pid}")
            
            return True
        except Exception as e:
            logger.error(f"System CPU affinity configuration failed: {e}")
            return False
    
    def _create_cpu_governor_service(self) -> bool:
        """Create systemd service for CPU governor."""
        try:
            service_content = """[Unit]
Description=Set CPU Governor to Performance
After=multi-user.target

[Service]
Type=oneshot
ExecStart=/bin/bash -c 'echo performance | tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor'
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
"""
            
            service_file = "/etc/systemd/system/cpu-performance-governor.service"
            
            if not self.dry_run:
                with open(service_file, 'w') as f:
                    f.write(service_content)
                
                self._run_command("sudo systemctl daemon-reload")
            
            return True
        except Exception as e:
            logger.error(f"CPU governor service creation failed: {e}")
            return False
    
    def _get_network_interfaces(self) -> List[str]:
        """Get list of network interfaces."""
        try:
            result = subprocess.run(['ip', 'link', 'show'], capture_output=True, text=True)
            interfaces = []
            
            for line in result.stdout.split('\n'):
                if ':' in line and 'state' in line:
                    interface = line.split(':')[1].strip()
                    interfaces.append(interface)
            
            return interfaces
        except Exception:
            return []
    
    def _get_interface_irqs(self, interface: str) -> List[str]:
        """Get IRQ numbers for network interface."""
        try:
            result = subprocess.run(['grep', interface, '/proc/interrupts'], 
                                  capture_output=True, text=True)
            irqs = []
            
            for line in result.stdout.split('\n'):
                if line:
                    irq = line.split(':')[0].strip()
                    if irq.isdigit():
                        irqs.append(irq)
            
            return irqs
        except Exception:
            return []
    
    def _get_rcu_pids(self) -> List[str]:
        """Get RCU thread PIDs."""
        try:
            result = subprocess.run(['pgrep', 'rcu'], capture_output=True, text=True)
            return result.stdout.strip().split('\n') if result.stdout else []
        except Exception:
            return []
    
    def _backup_file(self, filepath: str):
        """Backup file before modification."""
        if os.path.exists(filepath):
            backup_path = f"{filepath}.athena_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(filepath, backup_path)
            self.backup_files[filepath] = backup_path
            logger.info(f"Backed up {filepath} to {backup_path}")
    
    def _run_command(self, command: str) -> bool:
        """Run shell command."""
        try:
            logger.debug(f"Running command: {command}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            else:
                logger.error(f"Command failed: {command}\nError: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Command execution failed: {command}\nError: {e}")
            return False
    
    def _log_action(self, action: str, status: str, details: str):
        """Log tuning action."""
        self.tuning_log.append({
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        })
    
    def _save_tuning_log(self):
        """Save tuning log to file."""
        import json
        
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': self.dry_run,
            'tuning_actions': self.tuning_log,
            'backup_files': self.backup_files
        }
        
        with open('system_tuning_log.json', 'w') as f:
            json.dump(log_data, f, indent=2)
        
        logger.info("Tuning log saved to system_tuning_log.json")


def main():
    """Main system tuning function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Tune system for HFT performance")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be done without making changes")
    parser.add_argument("--validate", action="store_true",
                       help="Validate current system configuration")
    
    args = parser.parse_args()
    
    if args.validate:
        # Validation logic would go here
        logger.info("System validation not yet implemented")
        sys.exit(0)
    
    tuner = SystemTuner(dry_run=args.dry_run)
    
    if tuner.tune_all_systems():
        logger.info("System tuning completed successfully")
        if not args.dry_run:
            logger.info("REBOOT REQUIRED for all changes to take effect")
        sys.exit(0)
    else:
        logger.error("System tuning failed")
        sys.exit(1)


if __name__ == "__main__":
    main()
